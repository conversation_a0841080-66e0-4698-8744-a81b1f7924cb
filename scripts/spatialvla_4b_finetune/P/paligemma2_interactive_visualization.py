#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Interactive Visualization
===================================

为PaliGemma2 Logit Lens分析结果创建交互式HTML可视化界面。

功能特点：
1. 16×16 patch网格可视化，对应256个vision tokens  
2. 鼠标悬停显示每层的top-k预测
3. 点击锁定特定patch进行深度分析
4. 动态层级滑动条控制
5. 实时token-patch映射显示

作者: Assistant
日期: 2025-01-14
"""

import os
import json
import base64
from typing import Dict, List, Any, Optional
import numpy as np
from PIL import Image
import logging

logger = logging.getLogger(__name__)


class PaliGemma2InteractiveVisualizer:
    """PaliGemma2交互式可视化器"""
    
    def __init__(self, output_dir: str = "./paligemma2_visualization"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def create_interactive_html(self, 
                              logit_lens_results: Dict[str, Any],
                              image_path: str,
                              save_path: str) -> str:
        """创建交互式HTML界面"""
        
        # 编码图像
        image_base64 = self._encode_image(image_path)
        
        # 获取图像尺寸信息
        try:
            from PIL import Image
            original_image = Image.open(image_path)
            original_width, original_height = original_image.size
            
            # 计算显示尺寸（保持比例，最大边不超过350px）
            max_display_size = 350
            if original_width > original_height:
                display_width = max_display_size
                display_height = int((original_height / original_width) * max_display_size)
            else:
                display_height = max_display_size
                display_width = int((original_width / original_height) * max_display_size)
        except Exception as e:
            logger.warning(f"获取图像尺寸失败: {e}")
            display_width, display_height = 350, 350
            original_width, original_height = 640, 512
        
        # 处理logit lens数据
        processed_data = self._process_logit_lens_data(logit_lens_results)
        
        # 添加图像尺寸信息到数据中
        processed_data.update({
            'image_display_width': display_width,
            'image_display_height': display_height,
            'image_original_width': original_width,
            'image_original_height': original_height
        })
        
        # 生成HTML内容
        html_content = self._generate_html_template(image_base64, processed_data)
        
        # 保存HTML文件
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"交互式HTML界面已保存到: {save_path}")
        return save_path
    
    def _encode_image(self, image_path: str) -> str:
        """将图像编码为base64字符串"""
        try:
            logger.info(f"正在编码图像: {image_path}")

            # 检查文件是否存在
            if not os.path.exists(image_path):
                logger.error(f"图像文件不存在: {image_path}")
                return ""

            with open(image_path, 'rb') as f:
                image_data = f.read()

            if not image_data:
                logger.error(f"图像文件为空: {image_path}")
                return ""

            encoded = base64.b64encode(image_data).decode('utf-8')
            logger.info(f"图像编码成功，长度: {len(encoded)}")
            return encoded

        except Exception as e:
            logger.error(f"图像编码失败: {e}")
            import traceback
            traceback.print_exc()
            return ""
    
    def _process_logit_lens_data(self, logit_lens_results: Dict[str, Any]) -> Dict[str, Any]:
        """处理logit lens数据为可视化格式 - 修复版本"""
        
        if 'results' not in logit_lens_results:
            return {'patches': {}, 'layers': [], 'texts': {}}
        
        results = logit_lens_results['results']
        
        # 按patch和层组织数据
        patch_data = {}  # {patch_id: {layer: top_tokens}}
        text_data = {}   # {text_pos: {layer: top_tokens}}
        layers = set()
        
        logger.info(f"处理 {len(results)} 个logit lens结果...")
        
        for result in results:
            layer = result['layer']
            layers.add(layer)
            
            if result['token_type'] == 'vision':
                patch_id = result['patch_id']
                if patch_id not in patch_data:
                    patch_data[patch_id] = {}
                patch_data[patch_id][layer] = {
                    'top_tokens': result['top_tokens'],
                    'position': result['position'],
                    'patch_row': result.get('patch_row', patch_id // 16),
                    'patch_col': result.get('patch_col', patch_id % 16)
                }
            
            elif result['token_type'] == 'text':
                text_pos = result.get('text_token_id', result['position'])
                if text_pos not in text_data:
                    text_data[text_pos] = {}
                text_data[text_pos][layer] = {
                    'top_tokens': result['top_tokens'],
                    'position': result['position']
                }
        
        logger.info(f"数据处理完成:")
        logger.info(f"  - Vision patches: {len(patch_data)}")
        logger.info(f"  - Text positions: {len(text_data)}")
        logger.info(f"  - Layers: {sorted(list(layers))}")
        
        # 添加模型输出信息（如果存在）
        model_output_info = {}
        if 'model_output' in logit_lens_results:
            model_output_info = logit_lens_results['model_output']
        
        return {
            'patches': patch_data,
            'texts': text_data,
            'layers': sorted(list(layers)),
            'num_patches': len(patch_data),
            'num_texts': len(text_data),
            'model_output': model_output_info,  # 新增：模型输出信息
            'image_display_width': 350,
            'image_display_height': 350,
            'image_original_width': 640,
            'image_original_height': 512
        }
    
    def _generate_html_template(self, image_base64: str, data: Dict[str, Any]) -> str:
        """生成HTML模板"""
        
        # 将数据转换为JSON字符串
        data_json = json.dumps(data, ensure_ascii=False)
        
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma2 Logit Lens Interactive Analysis</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        
        .header {{
            text-align: center;
            margin-bottom: 30px;
        }}
        
        .main-content {{
            display: flex;
            gap: 20px;
        }}
        
        .left-panel {{
            flex: 1;
            max-width: 400px;
        }}
        
        .right-panel {{
            flex: 2;
        }}
        
        .image-container {{
            position: relative;
            margin-bottom: 20px;
            width: {data.get('image_display_width', 350)}px;
        }}
        
        .image-header {{
            margin-bottom: 10px;
            font-weight: bold;
        }}
        
        .image-wrapper {{
            position: relative;
            width: {data.get('image_display_width', 350)}px;
            height: {data.get('image_display_height', 350)}px;
        }}
        
        .source-image {{
            width: {data.get('image_display_width', 350)}px;
            height: {data.get('image_display_height', 350)}px;
            border: 2px solid #ddd;
            border-radius: 8px;
            display: block;
            object-fit: fill;
            object-position: top left;
        }}
        
        .patch-grid {{
            display: grid;
            grid-template-columns: repeat(16, 1fr);
            grid-template-rows: repeat(16, 1fr);
            gap: 1px;
            width: 350px;
            height: 350px;
            margin: 20px 0;
            border: 2px solid #333;
            background-color: #333;
        }}
        
        .patch {{
            background-color: #e0e0e0;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }}
        
        .patch:hover {{
            background-color: #ffeb3b;
            transform: scale(1.1);
            z-index: 10;
            border: 2px solid #ff5722;
        }}
        
        .patch.selected {{
            background-color: #ff5722;
            border: 2px solid #d32f2f;
        }}
        
        .patch.has-data {{
            background-color: #4caf50;
        }}
        
        .patch.has-data:hover {{
            background-color: #8bc34a;
        }}
        
        .controls {{
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
        }}
        
        .layer-selector {{
            margin: 10px 0;
        }}
        
        .layer-slider {{
            width: 100%;
            margin: 10px 0;
        }}
        
        .analysis-panel {{
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            min-height: 400px;
        }}
        
        .token-info {{
            background-color: white;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }}
        
        .token-list {{
            list-style: none;
            padding: 0;
        }}
        
        .token-item {{
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }}
        
        .token-text {{
            font-family: monospace;
            background-color: #e1f5fe;
            padding: 2px 6px;
            border-radius: 3px;
        }}
        
        .token-prob {{
            font-weight: bold;
            color: #1976d2;
        }}
        
        .legend {{
            margin: 20px 0;
            padding: 15px;
            background-color: #e8f5e8;
            border-radius: 8px;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            margin: 5px 0;
        }}
        
        .legend-color {{
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }}
        
        .stats-panel {{
            margin: 20px 0;
            padding: 15px;
            background-color: #fff3e0;
            border-radius: 8px;
        }}
        
        .instruction {{
            color: #666;
            font-style: italic;
            margin: 10px 0;
        }}
        
        .model-output-panel {{
            margin: 20px 0;
            padding: 15px;
            background-color: #f0f8ff;
            border-radius: 8px;
            border-left: 4px solid #0066cc;
        }}
        
        .output-text {{
            font-family: monospace;
            background-color: #ffffff;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            border: 1px solid #ddd;
            white-space: pre-wrap;
            max-height: 150px;
            overflow-y: auto;
        }}
        
        .highlight-box {{
            position: absolute;
            border: 3px solid #ff5722;
            pointer-events: none;
            display: none;
            box-shadow: 0 0 10px rgba(255, 87, 34, 0.5);
            z-index: 10;
        }}
        
        .debug-info {{
            position: absolute;
            top: 5px;
            left: 5px;
            background: rgba(0,0,0,0.7);
            color: white;
            font-size: 12px;
            padding: 2px 5px;
            border-radius: 3px;
            z-index: 20;
            pointer-events: none;
        }}
        
        .tooltip {{
            position: fixed;
            background: rgba(255,255,255,0.95);
            border: 2px solid #333;
            border-radius: 8px;
            padding: 10px;
            font-size: 13px;
            z-index: 1000;
            pointer-events: none;
            max-width: 250px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            display: none;
        }}
        
        .tooltip-header {{
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 4px;
        }}
        
        .tooltip-item {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 4px 0;
            padding: 2px 0;
        }}
        
        .tooltip-token {{
            font-family: monospace;
            background-color: #e3f2fd;
            padding: 2px 6px;
            border-radius: 3px;
            max-width: 150px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }}
        
        .tooltip-prob {{
            font-weight: bold;
            color: #1976d2;
            margin-left: 8px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 PaliGemma2 Logit Lens Interactive Analysis</h1>
            <p>Explore how vision patches evolve across transformer layers</p>
        </div>
        
        <div class="main-content">
            <div class="left-panel">
                <div class="image-container">
                    <h3 class="image-header">📸 Source Image</h3>
                    <div class="image-wrapper">
                        <img src="data:image/jpeg;base64,{image_base64}" alt="Source Image" class="source-image" id="source-image">
                        <div class="highlight-box" id="highlight-box"></div>
                    </div>
                </div>
                
                <div class="model-output-panel">
                    <h4>🤖 Model Output</h4>
                    <div class="output-text" id="model-output">
                        {self._format_model_output_for_html(data.get('model_output', {}))}
                    </div>
                </div>
                
                <div class="legend">
                    <h4>🎨 Legend</h4>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #4caf50;"></div>
                        <span>Patch with analysis data</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #e0e0e0;"></div>
                        <span>Patch without data</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #ff5722;"></div>
                        <span>Selected patch</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color" style="background-color: #ffeb3b;"></div>
                        <span>Hovered patch</span>
                    </div>
                </div>
                
                <div class="stats-panel">
                    <h4>📊 Statistics</h4>
                    <p><strong>Analyzed patches:</strong> <span id="patch-count">{data.get('num_patches', 0)}</span></p>
                    <p><strong>Analyzed layers:</strong> <span id="layer-count">{len(data.get('layers', []))}</span></p>
                    <p><strong>Text positions:</strong> <span id="text-count">{data.get('num_texts', 0)}</span></p>
                </div>
            </div>
            
            <div class="right-panel">
                <div class="controls">
                    <h3>🎛️ Controls</h3>
                    <div class="layer-selector">
                        <label for="layer-slider"><strong>Select Layer:</strong></label>
                        <input type="range" id="layer-slider" class="layer-slider" 
                               min="0" max="{len(data.get('layers', [])) - 1}" value="0">
                        <span id="layer-display">Layer {data.get('layers', [0])[0] if data.get('layers') else 0}</span>
                    </div>
                    <p class="instruction">
                        💡 <strong>Instructions:</strong><br>
                        • Move the slider to explore different layers<br>
                        • Hover over patches to see quick preview<br>
                        • Click patches to lock detailed analysis<br>
                        • Click on image to highlight corresponding patches
                    </p>
                </div>
                
                <div class="patch-grid" id="patch-grid">
                    <!-- Patches will be generated by JavaScript -->
                </div>
                
                <div class="analysis-panel">
                    <h3 id="analysis-title">🧠 Analysis Results</h3>
                    <div id="analysis-content">
                        <p class="instruction">Select a layer and hover/click on patches to see the analysis.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Enhanced Tooltip for showing Top5 predictions -->
    <div id="enhanced-tooltip" class="tooltip">
        <div class="tooltip-header">Layer 0 - Patch 0</div>
        <div class="tooltip-content">
            <!-- Content will be dynamically generated -->
        </div>
    </div>
    
    <script>
        // 数据
        const logitLensData = {data_json};
        let currentLayer = {data.get('layers', [0])[0] if data.get('layers') else 0};
        let selectedPatch = null;
        let isImageLocked = false;
        
        // 获取图像相关元素和尺寸信息
        const sourceImage = document.getElementById('source-image');
        const highlightBox = document.getElementById('highlight-box');
        const imageDisplayWidth = {data.get('image_display_width', 350)};
        const imageDisplayHeight = {data.get('image_display_height', 350)};
        const originalWidth = {data.get('image_original_width', 640)};
        const originalHeight = {data.get('image_original_height', 512)};
        const gridSize = 16;  // 16x16网格
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {{
            initializePatchGrid();
            initializeLayerSlider();
            initializeImageInteraction();
            updateDisplay();
        }});
        
        function initializePatchGrid() {{
            const grid = document.getElementById('patch-grid');
            grid.innerHTML = '';
            
            // 创建16x16 = 256个patch
            for (let i = 0; i < 256; i++) {{
                const patch = document.createElement('div');
                patch.className = 'patch';
                patch.dataset.patchId = i;
                
                // 检查是否有数据
                if (logitLensData.patches[i]) {{
                    patch.classList.add('has-data');
                }}
                
                // 添加事件监听器
                patch.addEventListener('mouseenter', handlePatchHover);
                patch.addEventListener('mouseleave', handlePatchLeave);
                patch.addEventListener('click', handlePatchClick);
                
                grid.appendChild(patch);
            }}
        }}
        
        function initializeLayerSlider() {{
            const slider = document.getElementById('layer-slider');
            const layers = logitLensData.layers;
            
            if (layers.length > 0) {{
                slider.max = layers.length - 1;
                slider.addEventListener('input', function() {{
                    currentLayer = layers[parseInt(this.value)];
                    document.getElementById('layer-display').textContent = `Layer ${{currentLayer}}`;
                    updateDisplay();
                }});
            }}
        }}
        
        function initializeImageInteraction() {{
            // 检查图像的实际DOM尺寸和位置
            const imageRect = sourceImage.getBoundingClientRect();
            const containerRect = sourceImage.parentElement.getBoundingClientRect();
            
            console.log(`Container: ${{containerRect.width}}x${{containerRect.height}}`);
            console.log(`Image: ${{imageRect.width}}x${{imageRect.height}}`);
            console.log(`Expected: ${{imageDisplayWidth}}x${{imageDisplayHeight}}`);
            
            // 计算图像在容器中的实际偏移
            const imageOffsetX = imageRect.left - containerRect.left;
            const imageOffsetY = imageRect.top - containerRect.top;
            console.log(`Image offset in container: (${{imageOffsetX}}, ${{imageOffsetY}})`);
            
            // 检查图像的自然尺寸vs显示尺寸
            console.log(`Image natural: ${{sourceImage.naturalWidth}}x${{sourceImage.naturalHeight}}`);
            console.log(`Image display: ${{sourceImage.width}}x${{sourceImage.height}}`);
            
            // 添加图像点击和悬停事件
            sourceImage.addEventListener('mousemove', handleImageMouseMove);
            sourceImage.addEventListener('click', handleImageClick);
            sourceImage.addEventListener('mouseleave', handleImageMouseLeave);
        }}
        
        function handleImageMouseMove(event) {{
            const rect = sourceImage.getBoundingClientRect();
            const borderWidth = 2;
            const x = event.clientX - rect.left - borderWidth;
            const y = event.clientY - rect.top - borderWidth;
            
            if (!isImageLocked) {{
                const patchIndex = getPatchIndexFromImageEvent(event);
                
                if (patchIndex >= 0 && patchIndex < 256) {{
                    highlightImagePatch(patchIndex);
                    updatePatchGridHighlight(patchIndex);
                    showEnhancedTooltip(event, patchIndex);
                }} else {{
                    highlightBox.style.display = 'none';
                    updatePatchGridHighlight(-1);
                    hideEnhancedTooltip();
                }}
            }}
        }}
        
        function handleImageClick(event) {{
            const patchIndex = getPatchIndexFromImageEvent(event);
            if (patchIndex >= 0 && patchIndex < 256) {{
                isImageLocked = !isImageLocked;
                
                if (isImageLocked) {{
                    selectedPatch = patchIndex;
                    highlightImagePatch(patchIndex);
                    showDetailedAnalysis(patchIndex);
                    
                    // 更新patch grid选中状态
                    document.querySelectorAll('.patch').forEach(p => p.classList.remove('selected'));
                    const patchElement = document.querySelector(`[data-patch-id="${{patchIndex}}"]`);
                    if (patchElement) {{
                        patchElement.classList.add('selected');
                    }}
                }} else {{
                    selectedPatch = null;
                    highlightBox.style.display = 'none';
                    document.querySelectorAll('.patch').forEach(p => p.classList.remove('selected'));
                    hideEnhancedTooltip();
                }}
            }}
        }}
        
        function handleImageMouseLeave() {{
            if (!isImageLocked) {{
                highlightBox.style.display = 'none';
                updatePatchGridHighlight(-1);
                hideEnhancedTooltip();
            }}
        }}
        
        function getPatchIndexFromImageEvent(event) {{
            const rect = sourceImage.getBoundingClientRect();
            const borderWidth = 2;
            
            // 考虑border，计算实际图像内容区域的坐标
            const x = event.clientX - rect.left - borderWidth;
            const y = event.clientY - rect.top - borderWidth;
            
            // 使用图像的实际内容尺寸（减去border）
            const actualContentWidth = rect.width - (borderWidth * 2);
            const actualContentHeight = rect.height - (borderWidth * 2);
            
            // 检查是否在图像内容范围内
            if (x < 0 || y < 0 || x >= actualContentWidth || y >= actualContentHeight) {{
                return -1;  // 超出图像内容范围
            }}
            
            // 转换为原图坐标 - 使用实际尺寸
            const originalX = (x / actualContentWidth) * originalWidth;
            const originalY = (y / actualContentHeight) * originalHeight;
            
            const patchX = Math.floor(originalX / (originalWidth / gridSize));
            const patchY = Math.floor(originalY / (originalHeight / gridSize));
            
            // 确保patch索引在有效范围内
            const patchIndex = patchY * gridSize + patchX;
            return (patchIndex >= 0 && patchIndex < 256) ? patchIndex : -1;
        }}
        
        function highlightImagePatch(patchIndex) {{
            // 计算patch在图像中的位置
            const row = Math.floor(patchIndex / gridSize);
            const col = patchIndex % gridSize;
            
            // 获取图像和包装器的位置信息
            const imageRect = sourceImage.getBoundingClientRect();
            const wrapperRect = sourceImage.parentElement.getBoundingClientRect();
            
            // 计算图像相对于wrapper的偏移
            const imageOffsetX = imageRect.left - wrapperRect.left;
            const imageOffsetY = imageRect.top - wrapperRect.top;
            
            // 计算patch的实际尺寸（考虑border）
            const borderWidth = 2;
            const imageContentWidth = imageRect.width - (borderWidth * 2);
            const imageContentHeight = imageRect.height - (borderWidth * 2);
            
            // 计算每个patch在显示图像中的尺寸
            const patchDisplayWidth = imageContentWidth / gridSize;
            const patchDisplayHeight = imageContentHeight / gridSize;
            
            // 计算高亮框的位置（相对于wrapper）
            const left = imageOffsetX + borderWidth + (col * patchDisplayWidth);
            const top = imageOffsetY + borderWidth + (row * patchDisplayHeight);
            const width = patchDisplayWidth;
            const height = patchDisplayHeight;
            
            // 设置高亮框位置
            highlightBox.style.left = `${{left}}px`;
            highlightBox.style.top = `${{top}}px`;
            highlightBox.style.width = `${{width}}px`;
            highlightBox.style.height = `${{height}}px`;
            highlightBox.style.display = 'block';
        }}
        
        function updatePatchGridHighlight(patchIndex) {{
            // 清除所有hover状态
            document.querySelectorAll('.patch').forEach(p => {{
                if (!p.classList.contains('selected')) {{
                    p.style.backgroundColor = '';
                }}
            }});
            
            // 高亮指定patch
            if (patchIndex >= 0) {{
                const patchElement = document.querySelector(`[data-patch-id="${{patchIndex}}"]`);
                if (patchElement && !patchElement.classList.contains('selected')) {{
                    patchElement.style.backgroundColor = '#ffeb3b';
                }}
            }}
        }}
        
        function handlePatchHover(event) {{
            const patchId = parseInt(event.target.dataset.patchId);
            showEnhancedTooltip(event, patchId);
            
            // 同时在图像上高亮对应区域
            if (!isImageLocked) {{
                highlightImagePatch(patchId);
            }}
        }}
        
        function handlePatchLeave(event) {{
            hideEnhancedTooltip();
            if (!isImageLocked) {{
                highlightBox.style.display = 'none';
            }}
        }}
        
        function handlePatchClick(event) {{
            const patchId = parseInt(event.target.dataset.patchId);
            
            // 更新选中状态
            document.querySelectorAll('.patch').forEach(p => p.classList.remove('selected'));
            event.target.classList.add('selected');
            
            selectedPatch = patchId;
            isImageLocked = true;
            highlightImagePatch(patchId);
            showDetailedAnalysis(patchId);
        }}
        
        function showDetailedAnalysis(patchId) {{
            const patchData = logitLensData.patches[patchId];
            const analysisContent = document.getElementById('analysis-content');
            const analysisTitle = document.getElementById('analysis-title');
            
            if (!patchData) {{
                analysisContent.innerHTML = '<p>No data available for this patch.</p>';
                return;
            }}
            
            analysisTitle.textContent = `🧠 Analysis: Patch ${{patchId}} (Row ${{Math.floor(patchId / 16)}}, Col ${{patchId % 16}})`;
            
            let html = '';
            
            // 当前层的详细信息
            if (patchData[currentLayer]) {{
                const layerData = patchData[currentLayer];
                html += `
                    <div class="token-info">
                        <h4>Layer ${{currentLayer}} - Position ${{layerData.position}}</h4>
                        <ul class="token-list">
                `;
                
                layerData.top_tokens.forEach((token, index) => {{
                    html += `
                        <li class="token-item">
                            <span class="token-text">${{token.token.trim()}}</span>
                            <span class="token-prob">${{(token.probability * 100).toFixed(2)}}%</span>
                        </li>
                    `;
                }});
                
                html += `
                        </ul>
                    </div>
                `;
            }} else {{
                html += `<p>No data available for Layer ${{currentLayer}}.</p>`;
            }}
            
            // 显示其他层的概览
            const availableLayers = Object.keys(patchData).map(Number).sort((a, b) => a - b);
            if (availableLayers.length > 1) {{
                html += `
                    <div class="token-info">
                        <h4>📈 Evolution Across Layers</h4>
                        <div style="max-height: 200px; overflow-y: auto;">
                `;
                
                availableLayers.forEach(layer => {{
                    const layerData = patchData[layer];
                    const topToken = layerData.top_tokens[0];
                    const isCurrentLayer = layer === currentLayer;
                    html += `
                        <div style="padding: 5px; margin: 3px 0; background: ${{isCurrentLayer ? '#e3f2fd' : '#f5f5f5'}}; border-radius: 3px;">
                            <strong>Layer ${{layer}}:</strong> "${{topToken.token.trim()}}" (${{(topToken.probability * 100).toFixed(1)}}%)
                        </div>
                    `;
                }});
                
                html += `
                        </div>
                    </div>
                `;
            }}
            
            analysisContent.innerHTML = html;
        }}
        
        function updateDisplay() {{
            // 更新patch网格显示
            document.querySelectorAll('.patch').forEach(patch => {{
                const patchId = parseInt(patch.dataset.patchId);
                const patchData = logitLensData.patches[patchId];
                
                // 重置类
                patch.classList.remove('has-data');
                if (patchData && patchData[currentLayer]) {{
                    patch.classList.add('has-data');
                }}
            }});
            
            // 如果有选中的patch，更新其分析
            if (selectedPatch !== null) {{
                showDetailedAnalysis(selectedPatch);
            }}
        }}
        
        function showEnhancedTooltip(event, patchIndex) {{
            const tooltip = document.getElementById('enhanced-tooltip');
            const patchData = logitLensData.patches[patchIndex];
            
            if (!patchData || !patchData[currentLayer]) {{
                hideEnhancedTooltip();
                return;
            }}
            
            const layerData = patchData[currentLayer];
            const topTokens = layerData.top_tokens.slice(0, 5); // Get top 5 tokens
            
            // Update tooltip header
            const header = tooltip.querySelector('.tooltip-header');
            header.textContent = `Layer ${{currentLayer}} - Patch ${{patchIndex}}`;
            
            // Generate content for top 5 tokens
            const content = tooltip.querySelector('.tooltip-content');
            content.innerHTML = '';
            
            topTokens.forEach((tokenData, index) => {{
                const item = document.createElement('div');
                item.className = 'tooltip-item';
                
                const tokenSpan = document.createElement('span');
                tokenSpan.className = 'tooltip-token';
                tokenSpan.textContent = tokenData.token.trim() || `[Token_${{tokenData.token_id}}]`;
                
                const probSpan = document.createElement('span');
                probSpan.className = 'tooltip-prob';
                probSpan.textContent = `${{(tokenData.probability * 100).toFixed(2)}}%`;
                
                item.appendChild(tokenSpan);
                item.appendChild(probSpan);
                content.appendChild(item);
            }});
            
            // Position tooltip near mouse cursor
            positionTooltip(tooltip, event);
            tooltip.style.display = 'block';
        }}
        
        function hideEnhancedTooltip() {{
            const tooltip = document.getElementById('enhanced-tooltip');
            tooltip.style.display = 'none';
        }}
        
        function positionTooltip(tooltip, event) {{
            const tooltipRect = tooltip.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            let x = event.clientX + 15; // 15px offset from cursor
            let y = event.clientY + 15;
            
            // Adjust if tooltip would go off-screen
            if (x + tooltipRect.width > viewportWidth) {{
                x = event.clientX - tooltipRect.width - 15;
            }}
            
            if (y + tooltipRect.height > viewportHeight) {{
                y = event.clientY - tooltipRect.height - 15;
            }}
            
            // Ensure tooltip stays within viewport
            x = Math.max(10, Math.min(x, viewportWidth - tooltipRect.width - 10));
            y = Math.max(10, Math.min(y, viewportHeight - tooltipRect.height - 10));
            
            tooltip.style.left = `${{x}}px`;
            tooltip.style.top = `${{y}}px`;
        }}
    </script>
</body>
</html>
        """
        
        return html_template
    
    def _format_model_output_for_html(self, model_output: Dict[str, Any]) -> str:
        """格式化模型输出用于HTML显示"""
        if not model_output:
            return "Model output not available"
        
        try:
            if 'error' in model_output:
                return f"⚠️ Generation Error: {model_output['error']}"
            
            lines = []
            
            # 添加输入提示
            if 'input_prompt' in model_output:
                lines.append(f"📝 Prompt: {model_output['input_prompt']}")
            
            # 添加生成结果
            if 'generated_text' in model_output:
                generated_text = model_output['generated_text']
                generation_length = model_output.get('generation_length', 0)
                
                # 如果文本太长，截断显示
                if len(generated_text) > 300:
                    truncated_text = generated_text[:300] + "..."
                    lines.append(f"🤖 Generated ({generation_length} tokens): {truncated_text}")
                    lines.append("(Text truncated for display)")
                else:
                    lines.append(f"🤖 Generated ({generation_length} tokens): {generated_text}")
            
            return "\n".join(lines) if lines else "Model output available but could not be formatted"
            
        except Exception as e:
            return f"Error formatting model output: {e}"
    
    def create_comparative_analysis(self, 
                                  results_dict: Dict[str, Dict[str, Any]], 
                                  save_path: str) -> str:
        """创建多个结果的比较分析界面"""
        
        # 处理多个结果的数据
        comparative_data = {}
        for name, results in results_dict.items():
            comparative_data[name] = self._process_logit_lens_data(results)
        
        # 生成比较HTML
        html_content = self._generate_comparative_html(comparative_data)
        
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"比较分析界面已保存到: {save_path}")
        return save_path
    
    def _generate_comparative_html(self, data: Dict[str, Dict[str, Any]]) -> str:
        """生成比较分析的HTML模板"""
        # 这里可以实现更复杂的比较界面
        # 为简化，先返回基本框架
        
        data_json = json.dumps(data, ensure_ascii=False)
        
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>PaliGemma2 Comparative Analysis</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .comparison-container {{ display: flex; gap: 20px; }}
        .analysis-column {{ flex: 1; border: 1px solid #ddd; padding: 15px; }}
    </style>
</head>
<body>
    <h1>🔬 PaliGemma2 Comparative Logit Lens Analysis</h1>
    <div class="comparison-container" id="comparison-container">
        <!-- Content will be generated by JavaScript -->
    </div>
    
    <script>
        const comparativeData = {data_json};
        
        // 初始化比较界面
        document.addEventListener('DOMContentLoaded', function() {{
            initializeComparison();
        }});
        
        function initializeComparison() {{
            const container = document.getElementById('comparison-container');
            
            Object.keys(comparativeData).forEach(name => {{
                const column = document.createElement('div');
                column.className = 'analysis-column';
                column.innerHTML = `
                    <h3>${{name}}</h3>
                    <p>Patches: ${{Object.keys(comparativeData[name].patches).length}}</p>
                    <p>Layers: ${{comparativeData[name].layers.length}}</p>
                `;
                container.appendChild(column);
            }});
        }}
    </script>
</body>
</html>
        """
        
        return html_template


def create_visualization_demo():
    """创建可视化演示"""
    # 模拟一些数据用于演示
    demo_data = {
        'results': [
            {
                'layer': 15,
                'position': 25,
                'token_type': 'vision',
                'patch_id': 25,
                'patch_row': 1,
                'patch_col': 9,
                'top_tokens': [
                    {'token': 'blue', 'probability': 0.15, 'token_id': 123},
                    {'token': 'sky', 'probability': 0.12, 'token_id': 456},
                    {'token': 'color', 'probability': 0.08, 'token_id': 789}
                ],
                'prompt': 'Describe this image'
            }
        ]
    }
    
    visualizer = PaliGemma2InteractiveVisualizer()
    
    # 创建一个简单的测试图像路径（实际使用时替换为真实路径）
    test_image_path = "/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images/real_image_20250610_170727.png"
    html_path = "demo_visualization.html"
    
    
    visualizer.create_interactive_html(demo_data, test_image_path, html_path)
    print(f"演示可视化已创建: {html_path}")



if __name__ == "__main__":
    create_visualization_demo() 