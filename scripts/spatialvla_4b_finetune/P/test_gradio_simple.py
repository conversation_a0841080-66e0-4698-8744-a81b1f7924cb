#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的Gradio测试脚本
"""

import sys
import os
import logging
from PIL import Image
import numpy as np

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_gradio_components():
    """测试Gradio组件是否能正常工作"""
    try:
        # 导入必要的模块
        from visual_space.app import initialize_model, analyze_image
        from paligemma2_logit_lens_analysis import PaliGemma2LogitLensConfig
        
        logger.info("✅ 成功导入Gradio组件")
        
        # 测试模型初始化
        logger.info("🚀 测试模型初始化...")
        success, error_msg = initialize_model("google/paligemma2-3b-pt-224", "cpu")
        
        if success:
            logger.info("✅ 模型初始化成功")
            
            # 创建一个测试图像
            test_image = Image.new('RGB', (224, 224), color='blue')
            
            # 测试分析功能
            logger.info("🔍 测试图像分析...")
            html_result, plot_result, summary_result = analyze_image(
                image=test_image,
                prompt="What color is this image?",
                model_choice="google/paligemma2-3b-pt-224",
                device_choice="cpu",
                target_layers=["20", "24"]
            )
            
            if "❌" not in html_result:
                logger.info("✅ 图像分析测试成功")
                logger.info(f"HTML结果长度: {len(html_result)}")
                logger.info(f"摘要: {summary_result[:200]}...")
                return True, "所有测试通过"
            else:
                logger.error(f"❌ 图像分析失败: {html_result[:500]}")
                return False, html_result
        else:
            logger.error(f"❌ 模型初始化失败: {error_msg}")
            return False, error_msg
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)

if __name__ == "__main__":
    success, message = test_gradio_components()
    if success:
        print(f"🎉 {message}")
    else:
        print(f"❌ {message}")
        sys.exit(1)
