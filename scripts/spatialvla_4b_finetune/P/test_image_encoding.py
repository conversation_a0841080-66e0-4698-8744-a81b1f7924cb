#!/usr/bin/env python3
"""
测试图像编码功能
"""

import os
import sys
import base64
import tempfile
from PIL import Image
import numpy as np

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from paligemma2_interactive_visualization import PaliGemma2InteractiveVisualizer

def create_test_image():
    """创建一个测试图像"""
    # 创建一个简单的测试图像
    img_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    # 保存到临时文件
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        img.save(tmp.name)
        return tmp.name

def test_image_encoding():
    """测试图像编码功能"""
    print("🧪 开始测试图像编码功能...")
    
    # 创建测试图像
    image_path = create_test_image()
    print(f"✅ 创建测试图像: {image_path}")
    print(f"📁 文件存在: {os.path.exists(image_path)}")
    print(f"📏 文件大小: {os.path.getsize(image_path)} bytes")
    
    try:
        # 创建可视化器
        visualizer = PaliGemma2InteractiveVisualizer()
        
        # 测试图像编码
        encoded = visualizer._encode_image(image_path)
        
        if encoded:
            print(f"✅ 图像编码成功!")
            print(f"📊 编码长度: {len(encoded)}")
            print(f"🔤 编码前缀: {encoded[:50]}...")
            
            # 测试解码
            try:
                decoded_data = base64.b64decode(encoded)
                print(f"✅ 解码测试成功，解码数据长度: {len(decoded_data)}")
            except Exception as e:
                print(f"❌ 解码测试失败: {e}")
                
        else:
            print("❌ 图像编码失败!")
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理临时文件
        try:
            os.unlink(image_path)
            print(f"🧹 清理临时文件: {image_path}")
        except:
            pass

if __name__ == "__main__":
    test_image_encoding()
