#!/usr/bin/env python3
"""
测试HTML显示功能
"""

import os
import gradio as gr
import tempfile
from PIL import Image
import numpy as np
import base64

def create_test_html():
    """创建一个简单的测试HTML"""
    # 创建一个简单的测试图像
    img_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
    img = Image.fromarray(img_array)
    
    # 保存到临时文件并编码
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        img.save(tmp.name)
        
        # 读取并编码
        with open(tmp.name, 'rb') as f:
            image_data = f.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        
        # 清理临时文件
        os.unlink(tmp.name)
    
    # 创建HTML内容
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>测试HTML显示</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                padding: 20px;
                background-color: #f0f0f0;
            }}
            .container {{
                background-color: white;
                padding: 20px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .test-image {{
                max-width: 200px;
                border: 2px solid #333;
                border-radius: 5px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🧪 HTML显示测试</h1>
            <p>这是一个测试页面，用于验证HTML在Gradio中的显示。</p>
            
            <h2>📸 测试图像</h2>
            <img src="data:image/png;base64,{image_base64}" alt="测试图像" class="test-image">
            
            <h2>📊 测试数据</h2>
            <ul>
                <li>图像编码长度: {len(image_base64)}</li>
                <li>当前时间: <span id="current-time"></span></li>
            </ul>
            
            <div style="background-color: #e8f4fd; padding: 15px; border-radius: 5px; margin-top: 20px;">
                <h3>✅ 如果你能看到这个内容，说明HTML显示正常！</h3>
            </div>
        </div>
        
        <script>
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        </script>
    </body>
    </html>
    """
    
    return html_content

def test_gradio_html():
    """测试Gradio HTML组件"""
    
    def generate_test_html():
        return create_test_html()
    
    # 创建Gradio界面
    with gr.Blocks(title="HTML显示测试") as demo:
        gr.Markdown("# 🧪 Gradio HTML显示测试")
        
        test_btn = gr.Button("生成测试HTML", variant="primary")
        html_output = gr.HTML(label="HTML输出")
        
        test_btn.click(
            fn=generate_test_html,
            outputs=html_output
        )
        
        # 默认显示
        demo.load(
            fn=generate_test_html,
            outputs=html_output
        )
    
    return demo

if __name__ == "__main__":
    demo = test_gradio_html()
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        share=False,
        debug=True
    )
