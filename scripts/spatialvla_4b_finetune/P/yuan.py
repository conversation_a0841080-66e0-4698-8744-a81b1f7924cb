#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Logit Lens Analysis - 基于参考实现的正确版本
==================================================

参考 /home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/llava-interp/scripts/logit_lens/
将SpatialVLA中的Logit Lens方法正确适配到PaliGemma2模型上。

作者: Assistant
日期: 2025-01-14
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn.functional as F
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any, Union
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
from PIL import Image
from tqdm import tqdm
import argparse
import logging
from collections import defaultdict
import pickle
import base64
from io import BytesIO
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入transformers
try:
    from transformers import (
        AutoProcessor, AutoModelForImageTextToText, 
        PaliGemmaForConditionalGeneration,
        set_seed
    )
    logger.info("成功导入Transformers组件。")
except ImportError as e:
    logger.error(f"导入Transformers失败: {e}")
    sys.exit(1)

import warnings
warnings.filterwarnings("ignore")


class PaliGemma2HookedModel:
    """PaliGemma2模型包装器 - 参考HookedLVLM实现"""
    
    def __init__(self, 
                 model_path: str = "google/paligemma2-3b-pt-224",
                 device: str = "cuda:0",
                 quantize_type: str = "bf16"):
        
        self.device = torch.device(device)
        self.model_path = model_path
        
        # 加载处理器
        self.processor = AutoProcessor.from_pretrained(model_path)
        
        # 数据类型映射
        dtype_map = {
            "bf16": torch.bfloat16,
            "fp16": torch.float16,
            "fp32": torch.float32
        }
        torch_dtype = dtype_map.get(quantize_type, torch.bfloat16)
        
        # 加载模型
        self.model = PaliGemmaForConditionalGeneration.from_pretrained(
            model_path,
            torch_dtype=torch_dtype,
            device_map=device
        )
        
        self.model.eval()
        
        logger.info(f"PaliGemma2HookedModel初始化完成")
        logger.info(f"  - 设备: {device}")
        logger.info(f"  - 数据类型: {quantize_type}")
    
    def forward(self, 
                image: Union[str, Image.Image], 
                prompt: str,
                output_hidden_states: bool = False,
                output_attentions: bool = False):
        """
        前向传播，参考HookedLVLM.forward
        
        Args:
            image: 图像路径或PIL Image对象
            prompt: 文本提示
            output_hidden_states: 是否输出隐藏状态
            output_attentions: 是否输出注意力
            
        Returns:
            模型输出，包含hidden_states等
        """
        
        # 处理图像输入
        if isinstance(image, str):
            image = Image.open(image).convert('RGB')
        
        # 预处理输入
        inputs = self.processor(text=prompt, images=image, return_tensors="pt")
        
        # 移动到设备
        for key, value in inputs.items():
            if torch.is_tensor(value):
                inputs[key] = value.to(self.device)
        
        # 前向传播
        with torch.no_grad():
            outputs = self.model(
                **inputs, 
                output_hidden_states=output_hidden_states,
                output_attentions=output_attentions
            )
        
        return outputs


def create_interactive_logit_lens(hidden_states, norm, lm_head, tokenizer, image, model_name, 
                                 image_filename, prompt, save_folder=".", 
                                 image_size=224, patch_size=14, misc_text=""):
    """
    创建交互式Logit Lens HTML - 适配PaliGemma2
    
    参考: llava-interp/src/lvlm_lens.py::create_interactive_logit_lens
    针对PaliGemma2的特点进行调整
    """
    
    # 对PaliGemma2进行特殊处理
    # PaliGemma2使用不同的token结构
    
    # Tokenize the prompt to understand structure
    input_ids = tokenizer.encode(prompt, add_special_tokens=True)
    logger.info(f"Tokenized prompt length: {len(input_ids)}")
    
    # PaliGemma2特定：视觉token数量计算
    # 通常是 (image_size // patch_size) ** 2
    img_token_count = (image_size // patch_size) ** 2
    logger.info(f"Expected vision tokens: {img_token_count}")
    
    # 从hidden_states推断序列长度和token结构
    if len(hidden_states) > 0:
        sequence_length = hidden_states[0].size(1)
        logger.info(f"Actual sequence length from hidden states: {sequence_length}")
    else:
        logger.error("No hidden states available")
        return
    
    # 创建token标签 - PaliGemma2特定逻辑
    token_labels = []
    
    # 假设前img_token_count个位置是视觉tokens
    for i in range(min(img_token_count, sequence_length)):
        token_labels.append(f"<IMG{(i+1):03d}>")
    
    # 剩余的是文本tokens
    remaining_length = sequence_length - len(token_labels)
    if remaining_length > 0:
        # 尝试解码文本部分
        try:
            # 简化处理：假设文本tokens在后面
            for i in range(remaining_length):
                token_labels.append(f"<TXT{(i+1):03d}>")
        except:
            # 如果解码失败，使用通用标签
            for i in range(remaining_length):
                token_labels.append(f"<UNK{(i+1):03d}>")
    
    logger.info(f"Created {len(token_labels)} token labels")
    
    # 处理隐藏状态并计算logits
    num_layers = len(hidden_states)
    all_top_tokens = []
    
    logger.info(f"Processing {num_layers} layers...")
    
    for layer in tqdm(range(num_layers), desc="Processing layers"):
        layer_hidden_states = hidden_states[layer]  # [batch, seq_len, hidden_size]
        
        # 应用最终norm
        normalized = norm(layer_hidden_states)
        
        # 通过lm_head获取logits
        logits = lm_head(normalized)
        
        # 计算概率
        probs = torch.softmax(logits, dim=-1)
        
        # 获取每个位置的top-5 tokens
        top_5_values, top_5_indices = torch.topk(probs, k=5, dim=-1)
        
        layer_top_tokens = []
        for pos in range(sequence_length):
            top_5_tokens = []
            top_5_probs = []
            
            for k in range(5):
                try:
                    token_id = top_5_indices[0, pos, k].item()
                    token_text = tokenizer.decode([token_id], skip_special_tokens=False)
                    prob = top_5_values[0, pos, k].item()
                    
                    # 清理token文本显示
                    clean_token = token_text.replace('\n', '\\n').replace('\r', '\\r')
                    if len(clean_token) > 20:
                        clean_token = clean_token[:20] + "..."
                    
                    top_5_tokens.append(clean_token)
                    top_5_probs.append(f"{prob:.4f}")
                    
                except Exception as e:
                    logger.warning(f"Token解码失败 at layer {layer}, pos {pos}, k {k}: {e}")
                    top_5_tokens.append(f"<UNK_{token_id}>")
                    top_5_probs.append("0.0000")
            
            layer_top_tokens.append(list(zip(top_5_tokens, top_5_probs)))
        
        all_top_tokens.append(layer_top_tokens)
    
    # 处理图像：中心裁剪和调整大小
    img_w, img_h = image.size
    min_dim = min(img_w, img_h)
    left = (img_w - min_dim) / 2
    top = (img_h - min_dim) / 2
    right = (img_w + min_dim) / 2
    bottom = (img_h + min_dim) / 2
    image_cropped = image.crop((left, top, right, bottom))
    image_resized = image_cropped.resize((image_size, image_size), Image.LANCZOS)
    
    # 转换图像为base64
    buffered = BytesIO()
    image_resized.save(buffered, format="PNG")
    img_str = base64.b64encode(buffered.getvalue()).decode()
    
    # 生成HTML内容
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma2 Interactive Logit Lens</title>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        .container { display: flex; }
        .image-container { 
            flex: 0 0 auto; 
            margin: 20px; 
            position: relative;
            width: IMAGESIZEPLACEHOLDER px;
        }
        .highlight-box {
            position: absolute;
            border: 2px solid red;
            pointer-events: none;
            display: none;
        }
        .table-container { 
            flex: 1 1 auto;
            position: relative;
            max-height: 90vh;
            overflow: auto;
            margin: 20px;
        }
        table { 
            border-collapse: separate;
            border-spacing: 0;
        }
        th, td { 
            border: 1px solid #ddd; 
            padding: 8px; 
            text-align: center;
            min-width: 80px;
        }
        th { 
            background-color: #f2f2f2; 
            font-weight: bold;
        }
        .corner-header {
            position: sticky;
            top: 0;
            left: 0;
            z-index: 3;
            background-color: #f2f2f2;
        }
        .row-header {
            position: sticky;
            left: 0;
            z-index: 2;
            background-color: #f2f2f2;
        }
        .col-header {
            position: sticky;
            top: 0;
            z-index: 1;
            background-color: #f2f2f2;
        }
        #tooltip {
            display: none;
            position: fixed;
            background: white;
            border: 1px solid black;
            padding: 5px;
            z-index: 1000;
            pointer-events: none;
            max-width: 300px;
            font-size: 14px;
        }
        .highlighted-row {
            background-color: #ffff99;
        }
        .image-info {
            margin-top: 10px;
            font-size: 14px;
            width: 100%;
            word-wrap: break-word;
        }
        .prompt {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .instructions {
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="image-container">
            <img src="data:image/png;base64,IMAGEPLACEHOLDER" alt="Input Image" style="width: IMAGESIZEPLACEHOLDER px; height: IMAGESIZEPLACEHOLDER px;">
            <div class="highlight-box"></div>
            <div class="image-info">
                <p class="prompt">Prompt: "PROMPTPLACEHOLDER"</p>
                <p class="instructions">Instructions: Click on image to lock the patch, click on image/table to unlock</p>
                <p>Model: PaliGemma2</p>
                <p>Info: MISCPLACEHOLDER</p>
            </div>
        </div>
        <div class="table-container">
            <table id="logitLens"></table>
        </div>
    </div>
    <div id="tooltip"></div>
<script>
    const data = DATAPLACEMENT;
    const tokenLabels = TOKENLABELSPLACEMENT;
    const tooltip = document.getElementById('tooltip');
    const highlightBox = document.querySelector('.highlight-box');
    const image = document.querySelector('.image-container img');
    const table = document.getElementById('logitLens');
    
    const imageSize = IMAGESIZEPLACEHOLDER;
    const patchSize = PATCHSIZEPLACEHOLDER;
    const gridSize = imageSize / patchSize;
    
    let isLocked = false;
    let highlightedRow = null;
    let lockedPatchIndex = null;
    
    // Create corner header
    const cornerHeader = table.createTHead().insertRow();
    cornerHeader.insertCell().textContent = 'Token/Layer';
    cornerHeader.cells[0].classList.add('corner-header');
    
    // Create layer headers
    for (let i = 0; i < data.length; i++) {
        const th = document.createElement('th');
        th.textContent = `Layer ${i + 1}`;
        th.classList.add('col-header');
        cornerHeader.appendChild(th);
    }
    
    // Create rows with token labels
    for (let pos = 0; pos < tokenLabels.length; pos++) {
        const row = table.insertRow();
        const rowHeader = row.insertCell();
        rowHeader.textContent = tokenLabels[pos];
        rowHeader.classList.add('row-header');
        
        for (let layer = 0; layer < data.length; layer++) {
            const cell = row.insertCell();
            if (data[layer][pos] && data[layer][pos][0]) {
                const topToken = data[layer][pos][0][0];
                cell.textContent = topToken;
            } else {
                cell.textContent = "N/A";
            }
            
            cell.addEventListener('mouseover', (e) => {
                if (!isLocked) {
                    showTooltip(e, layer, pos, false);
                }
            });
            cell.addEventListener('mousemove', updateTooltipPosition);
            cell.addEventListener('mouseout', () => {
                if (!isLocked) {
                    hideTooltip();
                }
            });
        }
    }

    function showTooltip(e, layer, pos, shouldScroll = false) {
        if (data[layer][pos]) {
            tooltip.innerHTML = data[layer][pos].map(([token, prob]) => `${token}: ${prob}`).join('<br>');
            tooltip.style.display = 'block';
            updateTooltipPosition(e);
            
            if (tokenLabels[pos].startsWith('<IMG')) {
                const patchIndex = parseInt(tokenLabels[pos].slice(4, 7));
                highlightImagePatch(patchIndex);
                highlightTableRow(pos, shouldScroll);
            } else {
                highlightBox.style.display = 'none';
                unhighlightTableRow();
            }
        }
    }

    function hideTooltip() {
        tooltip.style.display = 'none';
        if (!isLocked) {
            highlightBox.style.display = 'none';
            unhighlightTableRow();
        }
    }

    function updateTooltipPosition(e) {
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        let x = e.clientX + 10;
        let y = e.clientY + 10;

        if (x + tooltipRect.width > viewportWidth) {
            x = e.clientX - tooltipRect.width - 10;
        }

        if (y + tooltipRect.height > viewportHeight) {
            y = e.clientY - tooltipRect.height - 10;
        }

        x = Math.max(0, x);
        y = Math.max(0, y);

        tooltip.style.left = x + 'px';
        tooltip.style.top = y + 'px';
    }
    
    function highlightImagePatch(patchIndex) {
        const scaleFactor = image.width / imageSize;
        const row = Math.floor((patchIndex - 1) / gridSize);
        const col = (patchIndex - 1) % gridSize;
        
        const left = col * patchSize * scaleFactor;
        const top = row * patchSize * scaleFactor;
        const size = patchSize * scaleFactor;
        
        highlightBox.style.left = `${left}px`;
        highlightBox.style.top = `${top}px`;
        highlightBox.style.width = `${size}px`;
        highlightBox.style.height = `${size}px`;
        highlightBox.style.display = 'block';
    }

    function highlightTableRow(rowIndex, shouldScroll = false) {
        if (highlightedRow) {
            highlightedRow.classList.remove('highlighted-row');
        }
        highlightedRow = table.rows[rowIndex + 1];  // +1 to account for header row
        highlightedRow.classList.add('highlighted-row');
        if (shouldScroll) {
            highlightedRow.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    function unhighlightTableRow() {
        if (highlightedRow) {
            highlightedRow.classList.remove('highlighted-row');
            highlightedRow = null;
        }
    }

    image.addEventListener('mousemove', (e) => {
        if (!isLocked) {
            const patchIndex = getPatchIndexFromMouseEvent(e);
            highlightImagePatch(patchIndex);
            const tokenIndex = getTokenIndexFromPatchIndex(patchIndex);
            if (tokenIndex !== -1) {
                showTooltip(e, 0, tokenIndex, true);
            }
        }
    });

    image.addEventListener('mouseout', () => {
        if (!isLocked) {
            hideTooltip();
        }
    });

    image.addEventListener('click', (e) => {
        isLocked = !isLocked;
        if (isLocked) {
            lockedPatchIndex = getPatchIndexFromMouseEvent(e);
            highlightImagePatch(lockedPatchIndex);
            const tokenIndex = getTokenIndexFromPatchIndex(lockedPatchIndex);
            if (tokenIndex !== -1) {
                highlightTableRow(tokenIndex, true);
            }
        } else {
            lockedPatchIndex = null;
            hideTooltip();
        }
    });

    table.addEventListener('click', () => {
        isLocked = false;
        lockedPatchIndex = null;
        hideTooltip();
    });

    function getPatchIndexFromMouseEvent(e) {
        const rect = image.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        const patchX = Math.floor(x / (image.width / gridSize));
        const patchY = Math.floor(y / (image.height / gridSize));
        return patchY * gridSize + patchX + 1;
    }

    function getTokenIndexFromPatchIndex(patchIndex) {
        return tokenLabels.findIndex(label => label === `<IMG${patchIndex.toString().padStart(3, '0')}>`);
    }
</script>
</body>
</html>
    """
    
    # 替换占位符
    html_content = html_content.replace('IMAGEPLACEHOLDER', img_str)
    html_content = html_content.replace('DATAPLACEMENT', json.dumps(all_top_tokens))
    html_content = html_content.replace('TOKENLABELSPLACEMENT', json.dumps(token_labels))
    html_content = html_content.replace('IMAGESIZEPLACEHOLDER', str(image_size))
    html_content = html_content.replace('PATCHSIZEPLACEHOLDER', str(patch_size))
    html_content = html_content.replace('PROMPTPLACEHOLDER', prompt)
    html_content = html_content.replace('MISCPLACEHOLDER', misc_text)

    # 创建输出文件名
    output_filename = f"{model_name}_{Path(image_filename).stem}_logit_lens.html"
    output_path = Path(save_folder) / output_filename

    # 写入文件
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"Interactive logit lens HTML已保存到: {output_path}")
    return str(output_path)


def process_images(image_folder, save_folder, device, quantize_type, num_images=None, prompt="Describe this image."):
    """处理图像的主函数 - 参考create_logit_lens.py"""
    
    # 创建输出目录
    os.makedirs(save_folder, exist_ok=True)
    
    # 初始化模型
    logger.info("初始化PaliGemma2模型...")
    model = PaliGemma2HookedModel(device=device, quantize_type=quantize_type)
    
    # 获取模型组件
    norm = model.model.language_model.model.norm
    lm_head = model.model.language_model.lm_head
    tokenizer = model.processor.tokenizer
    model_name = "paligemma2-3b-pt-224"
    
    logger.info("模型组件获取成功:")
    logger.info(f"  - norm: {type(norm)}")
    logger.info(f"  - lm_head: {type(lm_head)} {lm_head.weight.shape}")
    logger.info(f"  - tokenizer: {type(tokenizer)}")
    
    # 加载图像
    def is_image_file(filename):
        valid_extensions = ('.jpeg', '.jpg', '.png', '.JPEG', '.JPG', '.PNG')
        return filename.lower().endswith(valid_extensions)
    
    if os.path.isfile(image_folder):
        # 单个图像文件
        image_files = [image_folder]
        image_folder = os.path.dirname(image_folder)
    else:
        # 图像目录
        image_files = [f for f in os.listdir(image_folder) if is_image_file(f)]
        if num_images:
            image_files = image_files[:num_images]
        image_files = [os.path.join(image_folder, f) for f in image_files]
    
    logger.info(f"找到 {len(image_files)} 个图像文件")
    
    images = {}
    for image_path in image_files:
        try:
            image = Image.open(image_path).convert('RGB')
            images[image_path] = image
            logger.info(f"成功加载图像: {image_path}")
        except IOError as e:
            logger.error(f"无法打开图像文件: {image_path}, 错误: {e}")
    
    # 处理每个图像
    for image_path, image in tqdm(images.items(), desc="Processing images"):
        logger.info(f"\n=== 处理图像: {image_path} ===")
        
        try:
            # 构建prompt
            text_question = prompt
            full_prompt = f"USER: <image>\n{text_question} ASSISTANT:"
            
            logger.info(f"使用prompt: {full_prompt}")
            
            # 运行前向传播
            outputs = model.forward(image, full_prompt, output_hidden_states=True)
            
            # 提取hidden_states
            if hasattr(outputs, 'language_model_outputs') and outputs.language_model_outputs is not None:
                hidden_states = outputs.language_model_outputs.hidden_states
                logger.info("从language_model_outputs获取hidden_states")
            else:
                hidden_states = outputs.hidden_states
                logger.info("从outputs直接获取hidden_states")
            
            logger.info(f"获取到 {len(hidden_states)} 层隐藏状态")
            
            # 创建Logit Lens可视化
            html_path = create_interactive_logit_lens(
                hidden_states=hidden_states,
                norm=norm,
                lm_head=lm_head,
                tokenizer=tokenizer,
                image=image,
                model_name=model_name,
                image_filename=image_path,
                prompt=full_prompt,
                save_folder=save_folder,
                image_size=224,  # PaliGemma2使用224x224
                patch_size=14    # 标准patch大小
            )
            
            logger.info(f"成功生成Logit Lens: {html_path}")
            
        except Exception as e:
            logger.error(f"处理图像 {image_path} 时出错: {e}")
            import traceback
            traceback.print_exc()
            continue
    
    logger.info("所有图像处理完成!")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PaliGemma2 Logit Lens Analysis")
    
    parser.add_argument("--image_folder", default="/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images", 
                       help="图像文件夹路径或单个图像路径")
    parser.add_argument("--save_folder", default="/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/paligemma2_logit_lens_results", 
                       help="结果保存目录")
    parser.add_argument("--device", default="cuda:0", help="运行设备")
    parser.add_argument("--quantize_type", default="bf16", help="量化类型")
    parser.add_argument("--num_images", type=int, default=1, help="处理图像数量")
    parser.add_argument("--prompt", default="Describe this image.", help="输入提示词")
    
    args = parser.parse_args()
    
    logger.info("=== PaliGemma2 Logit Lens Analysis开始 ===")
    logger.info(f"参数: {vars(args)}")
    
    process_images(
        image_folder=args.image_folder,
        save_folder=args.save_folder,
        device=args.device,
        quantize_type=args.quantize_type,
        num_images=args.num_images,
        prompt=args.prompt
    )
    
    logger.info("=== 分析完成 ===")


if __name__ == "__main__":
    main() 