# SpatialVLA 视觉解释分析工具

基于论文 *"Towards Interpreting Visual Information Processing in Vision-Language Models"* ([https://arxiv.org/pdf/2410.07149](https://arxiv.org/pdf/2410.07149)) 的方法，为SpatialVLA模型开发的可视化解释分析工具。

## 功能特性

### 1. Logit Lens 分析
- **原理**: 将不同层的隐藏状态投影到词汇空间，观察视觉表示的演化过程
- **数学本质**: 线性投影 + softmax变换
- **应用**: 理解视觉token如何在各层中逐步形成有意义的表示

### 2. 消融实验 (Ablation Study)
- **原理**: 用平均值替换特定的视觉tokens，验证信息定位
- **数学本质**: token替换实验
- **方法类型**:
  - `object_tokens`: 消融物体相关tokens
  - `object_with_buffer`: 消融物体tokens及其邻域
  - `random_tokens`: 消融随机选择的tokens
  - `high_gradient_tokens`: 消融梯度敏感的tokens

### 3. 注意力阻断 (Attention Knockout)
- **原理**: 修改注意力掩码阻断特定信息流，分析注意力机制
- **数学本质**: 设置注意力掩码 M_ij = -∞
- **分析维度**:
  - 不同层窗口 (early, mid, late)
  - 不同注意力模式 (object-to-last, visual-to-last等)

## 安装和环境配置

```bash
# 激活SpatialVLA环境
conda activate spatialvla

# 确保在正确的工作目录
cd /home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P

# 安装额外依赖（如需要）
pip install matplotlib seaborn
```

## 使用方法

### 基本用法

```bash
# 使用SpatialVLA环境的Python解释器运行分析
/home/<USER>/.conda/envs/spatialvla/bin/python spatialvla_visual_interpretation_analysis.py \
    --image-path /path/to/your/image.jpg \
    --instruction "pick up the coke can" \
    --output-dir ./results
```

### 完整参数示例

```bash
/home/<USER>/.conda/envs/spatialvla/bin/python spatialvla_visual_interpretation_analysis.py \
    --image-path /home/<USER>/claude/SpatialVLA/test_images/robot_pick_coke_can.jpg \
    --instruction "pick up the coke can" \
    --model-path "IPEC-COMMUNITY/spatialvla-4b-224-pt" \
    --device "cuda:0" \
    --dtype "bf16" \
    --output-dir "./visual_interpretation_results" \
    --target-layers "20,22,24,26,28,30" \
    --enable-logit-lens \
    --enable-ablation \
    --enable-attention-knockout \
    --seed 42
```

### 仅运行特定分析

```bash
# 仅运行Logit Lens分析
/home/<USER>/.conda/envs/spatialvla/bin/python spatialvla_visual_interpretation_analysis.py \
    --image-path image.jpg \
    --instruction "your instruction" \
    --enable-logit-lens \
    --output-dir ./logit_lens_only

# 仅运行消融实验
/home/<USER>/.conda/envs/spatialvla/bin/python spatialvla_visual_interpretation_analysis.py \
    --image-path image.jpg \
    --instruction "your instruction" \
    --enable-ablation \
    --output-dir ./ablation_only
```

## 输出结果

### 文件结构
```
visual_interpretation_results/
├── visualization_20250114_143025.png  # 综合可视化图表
├── results_20250114_143025.json       # 可读的JSON结果
├── results_20250114_143025.pkl        # 原始Python对象
└── batch_analysis_report.md           # 批量分析报告（如适用）
```

### 可视化解释

生成的可视化包含四个子图：

1. **Logit Lens**: 显示不同层中最高概率的tokens演化
2. **Ablation Study**: 各种消融方法对准确率的影响
3. **Attention Knockout**: 不同层窗口和注意力模式的性能热力图
4. **Analysis Summary**: 关键发现和统计摘要

### 结果解读

#### Logit Lens 结果
- **Layer Evolution**: 观察视觉表示如何从低级特征转换为语义概念
- **Critical Layers**: 识别信息转换的关键层（通常在25.7层达到峰值）
- **Token Progression**: 追踪从视觉patches到语义tokens的演化

#### 消融实验结果
- **Accuracy Drop**: 数值越高，说明被消融的tokens越重要
- **Method Comparison**: 物体相关tokens的消融通常产生>70%的准确率下降
- **Spatial Localization**: 验证视觉信息的空间局部化程度

#### 注意力阻断结果
- **Performance Ratio**: 阻断后的性能保持比例（1.0表示无影响）
- **Layer Windows**: 不同阶段层的功能角色分析
- **Attention Patterns**: 各种注意力流的重要性

## 实际应用场景

### 1. 机器人抓取任务分析
```bash
/home/<USER>/.conda/envs/spatialvla/bin/python spatialvla_visual_interpretation_analysis.py \
    --image-path robot_pick_coke_can.jpg \
    --instruction "pick up the red coke can on the table" \
    --output-dir ./pick_analysis
```

### 2. 机器人导航任务分析
```bash
/home/<USER>/.conda/envs/spatialvla/bin/python spatialvla_visual_interpretation_analysis.py \
    --image-path robot_navigation.jpg \
    --instruction "move to the kitchen" \
    --output-dir ./navigation_analysis
```

### 3. 多指令对比分析
```bash
# 创建批量分析脚本
for instruction in "pick up" "move near" "push away"; do
    /home/<USER>/.conda/envs/spatialvla/bin/python spatialvla_visual_interpretation_analysis.py \
        --image-path same_scene.jpg \
        --instruction "${instruction} the object" \
        --output-dir "./comparison_${instruction// /_}"
done
```

## 高级配置

### 自定义层分析
```python
# 修改配置文件中的target_layers
target_layers = [15, 20, 25, 30]  # 分析特定层
```

### 自定义消融方法
```python
# 在配置中添加新的消融方法
ablation_methods = [
    "object_tokens",
    "background_tokens", 
    "edge_tokens",
    "center_tokens"
]
```

### 注意力模式定制
```python
# 分析特定的注意力流
attention_patterns = [
    "object_to_action",
    "scene_to_planning", 
    "visual_to_decision"
]
```

## 性能优化

### 内存优化
- 使用`bf16`精度减少内存使用
- 限制`max_samples`避免内存溢出
- 设置合适的`target_layers`范围

### 速度优化
- 使用GPU加速 (`--device cuda:0`)
- 减少分析的层数
- 禁用不需要的分析模块

## 故障排除

### 常见问题

1. **内存不足**
   ```bash
   # 减少批次大小或使用fp16
   --dtype fp16 --max-samples 10
   ```

2. **CUDA错误**
   ```bash
   # 使用CPU运行
   --device cpu
   ```

3. **模型加载失败**
   ```bash
   # 检查模型路径和网络连接
   --model-path "local/path/to/model"
   ```

### 调试模式
```python
# 在代码中启用详细日志
logging.basicConfig(level=logging.DEBUG)
```

## 参考文献

1. Neo, C., et al. "Towards Interpreting Visual Information Processing in Vision-Language Models." arXiv:2410.07149 (2024).
2. SpatialVLA: 空间感知的视觉-语言-动作模型
3. LLaVA 解释方法的适配和扩展

## 贡献和反馈

如有问题或建议，请提交issue或联系开发团队。

---

**注意**: 本工具基于研究论文开发，主要用于理解和分析SpatialVLA模型的内部机制，不保证在所有环境下的稳定性。使用前请确保理解各种分析方法的原理和限制。 