#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型加载功能
"""

import sys
import os
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_model_loading():
    """测试模型加载功能"""
    try:
        # 导入分析器
        from paligemma2_logit_lens_analysis import PaliGemma2LogitLensAnalyzer, PaliGemma2LogitLensConfig
        
        logger.info("✅ 成功导入分析器")
        
        # 创建配置
        config = PaliGemma2LogitLensConfig(
            model_path="google/paligemma2-3b-pt-224",
            device="cpu",  # 使用CPU测试
            dtype="fp32",
            output_dir="./test_outputs",
            target_layers=[20, 24],
            max_samples=1,
            enable_visualization=True,
            max_tokens_display=3
        )
        
        logger.info("✅ 配置创建成功")
        
        # 创建分析器
        logger.info("🚀 开始创建分析器...")
        analyzer = PaliGemma2LogitLensAnalyzer(config)
        
        logger.info("✅ 分析器创建成功!")
        logger.info(f"模型层数: {analyzer.num_layers}")
        logger.info(f"隐藏维度: {analyzer.hidden_size}")
        logger.info(f"词汇表大小: {analyzer.vocab_size}")
        
        return True, "模型加载测试成功"
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)

if __name__ == "__main__":
    success, message = test_model_loading()
    if success:
        print(f"🎉 {message}")
    else:
        print(f"❌ {message}")
        sys.exit(1)
