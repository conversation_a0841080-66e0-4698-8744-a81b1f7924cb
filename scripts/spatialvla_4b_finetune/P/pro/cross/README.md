# PaliGemma2 单层跨Token分析系统

🎯 **揭示Token间相互影响和信息流动模式的深度分析工具**

## 🌟 核心功能

### 🔬 分析维度
1. **Token影响力矩阵** - 计算每对Token间的相互影响强度
2. **信息流动分析** - 识别信息在Token间的传播路径和枢纽节点
3. **Token聚类分析** - 发现功能相似的Token群组
4. **位置模式分析** - 分析不同位置类型间的交互模式

### 🎨 可视化输出
- **影响力热力图** - 直观显示Token间影响关系
- **信息流网络图** - 展示Token间的连接和流动
- **位置模式图** - 对比不同类型Token的交互特征
- **交互式HTML界面** - 支持动态探索和详细分析

## 🚀 快速开始

### 1. 基本使用

```python
from single_layer_cross_token_analyzer import (
    SingleLayerCrossTokenAnalyzer,
    CrossTokenAnalysisConfig
)

# 创建配置
config = CrossTokenAnalysisConfig(
    target_layers=[15],  # 分析第15层
    vision_patch_sample_rate=0.2,  # 采样20%的vision patches
    output_dir="./results"
)

# 创建分析器
analyzer = SingleLayerCrossTokenAnalyzer(config)

# 执行分析
results = analyzer.analyze_layer_cross_token(
    image_path="your_image.jpg",
    prompt="Describe this image",
    target_layer=15,
    generation_step=0
)

# 创建可视化
analyzer.create_cross_token_visualizations(
    results, "./results/analysis.png", "your_image.jpg"
)
```

### 2. 命令行使用

```bash
python single_layer_cross_token_analyzer.py \
    --image_path your_image.jpg \
    --prompt "Describe this image" \
    --target_layer 15 \
    --output_dir ./results \
    --vision_sample_rate 0.2
```

### 3. 运行示例

```bash
# 运行使用示例（需要准备example_image.jpg）
python example_usage.py
```

## 📊 输出结果

### 分析结果结构
```json
{
  "target_layer": 15,
  "sequence_length": 267,
  "analyzed_positions": [0, 16, 32, ...],
  "influence_matrix": {
    "matrix": [[0.0, 0.123, ...], ...],
    "positions": [0, 16, 32, ...],
    "top_influences": [
      {
        "source_pos": 45,
        "target_pos": 260,
        "influence_score": 0.234
      }
    ],
    "statistics": {
      "max_influence": 0.234,
      "mean_influence": 0.045,
      "sparsity": 0.78
    }
  },
  "information_flow": {
    "hub_tokens": [
      {
        "position": 128,
        "total_degree": 2.45
      }
    ],
    "key_paths": [[12, 45, 0.156], ...]
  },
  "token_clusters": {
    "clusters": {
      "0": [0, 16, 32],
      "1": [256, 257, 258]
    }
  },
  "positional_patterns": {
    "vision_to_text": [...],
    "text_to_vision": [...],
    "statistics": {...}
  }
}
```

### 可视化文件
- `*_influence_heatmap.png` - Token影响力热力图
- `*_flow_network.png` - 信息流网络图
- `*_positional_patterns.png` - 位置模式分析图
- `*.html` - 交互式分析界面

## ⚙️ 配置参数

### CrossTokenAnalysisConfig 主要参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `model_path` | str | "google/paligemma2-3b-pt-224" | 模型路径 |
| `target_layers` | List[int] | [10, 15, 20, 25] | 要分析的层 |
| `vision_patch_sample_rate` | float | 0.2 | vision patch采样率 |
| `text_positions_limit` | int | 15 | 最大分析的text位置数 |
| `analyze_influence_matrix` | bool | True | 是否计算影响力矩阵 |
| `analyze_information_flow` | bool | True | 是否分析信息流 |
| `analyze_token_clusters` | bool | True | 是否进行聚类分析 |
| `create_interactive_html` | bool | True | 是否创建交互式HTML |

## 🔍 核心算法

### 1. Token影响力计算
```python
influence_score = (
    attention_contribution * 0.4 +     # 注意力权重贡献
    representation_similarity * 0.3 +  # 表示相似度
    prediction_influence * 0.3         # 预测能力影响
)
```

### 2. 信息流分析
- 基于注意力权重构建有向图
- 识别高度连接的枢纽节点
- 发现关键信息传播路径

### 3. Token聚类
- 使用层次聚类算法
- 基于Token表示的余弦相似度
- 自动确定最优聚类数

## 🎯 应用场景

### 1. 模型理解
- 分析不同层的Token交互模式
- 理解视觉-文本信息融合机制
- 发现模型的注意力偏好

### 2. 模型优化
- 识别冗余连接和关键路径
- 指导模型剪枝和压缩
- 优化注意力机制设计

### 3. 可解释性研究
- 可视化模型内部工作机制
- 分析生成过程中的信息流动
- 理解多模态表示学习

## 📈 性能优化

### 内存优化
- 使用采样策略减少计算量
- 支持批处理和并行计算
- 自动清理中间状态

### 计算优化
- Hook系统避免重复前向传播
- 稀疏矩阵存储影响力数据
- 可配置的分析深度

## 🛠️ 依赖要求

```bash
pip install torch transformers pillow matplotlib seaborn
pip install networkx scikit-learn numpy scipy tqdm
```

## 📝 使用示例

### 示例1: 单层分析
```python
# 分析特定层的Token交互
results = analyzer.analyze_layer_cross_token(
    image_path="image.jpg",
    prompt="What is in this image?",
    target_layer=20,
    generation_step=0
)
```

### 示例2: 多层对比
```python
# 对比不同层的特征
for layer in [10, 15, 20, 25]:
    results = analyzer.analyze_layer_cross_token(
        image_path="image.jpg",
        prompt="Describe the scene",
        target_layer=layer
    )
    # 分析和对比结果...
```

### 示例3: 生成过程分析
```python
# 分析生成过程中的变化
for step in range(5):
    results = analyzer.analyze_layer_cross_token(
        image_path="image.jpg",
        prompt="This image shows",
        target_layer=15,
        generation_step=step
    )
    # 分析演化趋势...
```

## 🔧 自定义扩展

### 添加新的分析维度
```python
class CustomMetrics(CrossTokenMetrics):
    def custom_analysis(self, layer_states):
        # 实现自定义分析逻辑
        pass
```

### 自定义可视化
```python
def custom_visualization(analysis_results):
    # 实现自定义可视化
    pass
```

## 📚 技术细节

### Hook机制
- 使用PyTorch的forward hook捕获中间状态
- 安全的状态管理，不影响模型计算
- 支持多层并行分析

### 数学原理
- 基于注意力权重的信息流建模
- 余弦相似度衡量表示相关性
- 图论算法分析连接模式

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

### 开发环境设置
```bash
git clone <repository>
cd single_layer_cross_token_analyzer
pip install -r requirements.txt
```

### 测试
```bash
python -m pytest tests/
```

## 📄 许可证

MIT License

## 📞 联系方式

如有问题或建议，请提交Issue或联系开发团队。
