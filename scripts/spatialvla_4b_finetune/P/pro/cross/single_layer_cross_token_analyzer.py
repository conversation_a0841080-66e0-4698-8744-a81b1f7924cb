#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Single Layer Cross-Token Analysis System
==================================================

🎯 单层跨Token分析 - 揭示Token间相互影响和信息流动模式

### 核心洞察
单层跨token分析可以揭示：
- Token间的相互影响：在同一层中，不同位置的token如何相互作用
- 信息流动模式：信息如何在token之间传播
- 位置特异性：不同位置的token在同一层中的行为差异
- 注意力模式：哪些token对其他token的预测影响最大

### 技术架构
基于两个参考文件的优势：
1. 复用自回归logit lens的全位置分析框架
2. 复用Hook系统的安全组件捕获机制
3. 新增跨Token影响力计算和可视化

### 分析维度
1. Token表示分析（Representation Analysis）
2. 注意力交互分析（Attention Interaction Analysis）  
3. 预测能力传播分析（Predictive Power Propagation）
4. 梯度流分析（Gradient Flow Analysis）

作者: Assistant
日期: 2025-01-14
版本: 单层跨Token分析系统 v1.0
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn.functional as F
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any, Generator
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
from PIL import Image
from tqdm import tqdm
import argparse
import logging
from collections import defaultdict
import pickle
import copy
import random
import cv2
import time
import networkx as nx
from scipy.spatial.distance import cosine
from sklearn.cluster import AgglomerativeClustering
from sklearn.manifold import TSNE
import seaborn as sns

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入transformers
try:
    from transformers import (
        AutoProcessor, AutoModelForImageTextToText, 
        PaliGemmaForConditionalGeneration,
        set_seed
    )
    logger.info("成功导入Transformers组件。")
except ImportError as e:
    logger.error(f"导入Transformers失败: {e}")
    sys.exit(1)

import warnings
warnings.filterwarnings("ignore")


@dataclass
class CrossTokenAnalysisConfig:
    """单层跨Token分析配置"""
    
    # 模型配置
    model_path: str = "google/paligemma2-3b-pt-224"
    device: str = "cuda:0" if torch.cuda.is_available() else "cpu"
    dtype: str = "bf16"
    
    # 生成配置
    max_new_tokens: int = 15
    do_sample: bool = False
    temperature: float = 1.0
    
    # 跨Token分析配置
    target_layers: List[int] = field(default_factory=lambda: [10, 15, 20, 25])  # 分析关键层
    top_k_tokens: int = 5
    
    # 位置分析配置
    analyze_all_positions: bool = True   # 是否分析所有位置
    vision_patch_sample_rate: float = 0.2  # vision patch采样率（降低计算开销）
    text_positions_limit: int = 15  # 最大分析的text位置数
    
    # 跨Token分析维度
    analyze_influence_matrix: bool = True      # Token影响力矩阵
    analyze_information_flow: bool = True      # 信息流动分析
    analyze_token_clusters: bool = True        # Token聚类分析
    analyze_positional_patterns: bool = True  # 位置模式分析
    
    # 输出配置
    output_dir: str = "./single_layer_cross_token_results"
    save_detailed_analysis: bool = True
    
    # 可视化配置
    enable_visualization: bool = True
    create_influence_heatmap: bool = True
    create_flow_network: bool = True
    create_interactive_html: bool = True


class EnhancedCrossTokenHookManager:
    """
    增强的跨Token Hook管理器
    扩展Hook系统以支持跨Token分析
    """
    
    def __init__(self):
        self.hooks = []
        self.captured_states = {}
        self.current_step = 0
        
    def reset_for_step(self, step: int):
        """为新的生成步骤重置状态"""
        self.current_step = step
        self.captured_states[step] = {}
        
    def register_cross_token_hooks(self, model, target_layer: int):
        """注册用于跨token分析的hooks"""
        logger.info(f"🔗 注册跨Token分析Hook用于层 {target_layer}...")
        
        # 清除现有hooks
        self.clear_hooks()
        
        if target_layer >= len(model.language_model.model.layers):
            logger.warning(f"⚠️ 层索引 {target_layer} 超出范围")
            return
            
        layer = model.language_model.model.layers[target_layer]
        
        # Hook 1: 捕获自注意力权重矩阵
        def attention_weights_hook(module, input, output):
            # output: (hidden_states, attention_weights, present_kv)
            try:
                if isinstance(output, tuple) and len(output) >= 2:
                    attn_weights = output[1]  # [batch, heads, seq_len, seq_len]

                    # 检查注意力权重是否有效
                    if attn_weights is not None and torch.is_tensor(attn_weights):
                        if self.current_step not in self.captured_states:
                            self.captured_states[self.current_step] = {}
                        if target_layer not in self.captured_states[self.current_step]:
                            self.captured_states[self.current_step][target_layer] = {}

                        self.captured_states[self.current_step][target_layer]['attention_weights'] = attn_weights.clone().detach()
                    else:
                        logger.warning(f"注意力权重为None或无效，层 {target_layer}")
            except Exception as e:
                logger.warning(f"捕获注意力权重失败，层 {target_layer}: {e}")
        
        # Hook 2: 捕获每个token位置的中间表示
        def token_wise_hidden_hook(module, input, output):
            try:
                # 分别存储每个位置的hidden state
                hidden_states = output[0] if isinstance(output, tuple) else output

                if hidden_states is not None and torch.is_tensor(hidden_states):
                    batch_size, seq_len, hidden_dim = hidden_states.shape

                    token_states = {}
                    for pos in range(seq_len):
                        token_states[pos] = hidden_states[:, pos, :].clone().detach()

                    if self.current_step not in self.captured_states:
                        self.captured_states[self.current_step] = {}
                    if target_layer not in self.captured_states[self.current_step]:
                        self.captured_states[self.current_step][target_layer] = {}

                    self.captured_states[self.current_step][target_layer]['token_wise_states'] = token_states
                else:
                    logger.warning(f"隐藏状态为None或无效，层 {target_layer}")
            except Exception as e:
                logger.warning(f"捕获token状态失败，层 {target_layer}: {e}")
        
        # Hook 3: 捕获层输入状态（用于QKV分析）
        def layer_input_hook(module, input):
            try:
                # 安全地处理输入参数
                if isinstance(input, tuple) and len(input) > 0:
                    hidden_states = input[0]

                    if hidden_states is not None and torch.is_tensor(hidden_states):
                        if self.current_step not in self.captured_states:
                            self.captured_states[self.current_step] = {}
                        if target_layer not in self.captured_states[self.current_step]:
                            self.captured_states[self.current_step][target_layer] = {}

                        # 存储层输入状态
                        self.captured_states[self.current_step][target_layer]['layer_input'] = hidden_states.clone().detach()
                    else:
                        logger.warning(f"层输入状态为None或无效，层 {target_layer}")
            except Exception as e:
                logger.warning(f"捕获层输入失败，层 {target_layer}: {e}")

        # 注册hooks
        attn_hook = layer.self_attn.register_forward_hook(attention_weights_hook)
        self.hooks.append(attn_hook)

        hidden_hook = layer.register_forward_hook(token_wise_hidden_hook)
        self.hooks.append(hidden_hook)

        # 注册层输入hook（更安全的方式）
        input_hook = layer.register_forward_pre_hook(layer_input_hook)
        self.hooks.append(input_hook)
        
        logger.info(f"✅ 成功注册 {len(self.hooks)} 个跨Token分析Hook")
    
    def clear_hooks(self):
        """清除所有Hook"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
    
    def get_captured_states(self, step: int, layer_idx: int) -> Dict[str, Any]:
        """获取指定步骤和层的捕获状态"""
        return self.captured_states.get(step, {}).get(layer_idx, {})


class CrossTokenMetrics:
    """跨Token分析指标计算器"""
    
    def __init__(self, config: CrossTokenAnalysisConfig):
        self.config = config
    
    def compute_cross_token_influence(self, layer_states: Dict[str, Any], 
                                    position_i: int, position_j: int,
                                    norm_layer, lm_head) -> float:
        """
        计算position_i对position_j的影响力
        
        基于以下因素：
        1. 注意力权重 a[i,j]
        2. 表示相似度 sim(h_i, h_j)
        3. 预测能力变化 ΔP(y|h_j with/without h_i)
        """
        influence_score = 0.0
        
        # 1. 注意力贡献
        if 'attention_weights' in layer_states:
            attn_weights = layer_states['attention_weights']
            # 平均所有注意力头的权重
            attention_contribution = attn_weights[:, :, position_j, position_i].mean().item()
            influence_score += attention_contribution * 0.4  # 权重40%
        
        # 2. 表示相似度
        if 'token_wise_states' in layer_states:
            token_states = layer_states['token_wise_states']
            if position_i in token_states and position_j in token_states:
                h_i = token_states[position_i].squeeze()
                h_j = token_states[position_j].squeeze()
                representation_similarity = F.cosine_similarity(h_i, h_j, dim=0).item()
                influence_score += abs(representation_similarity) * 0.3  # 权重30%
        
        # 3. 预测影响（简化版本）
        if 'token_wise_states' in layer_states:
            prediction_influence = self._compute_prediction_influence_simplified(
                layer_states, position_i, position_j, norm_layer, lm_head
            )
            influence_score += prediction_influence * 0.3  # 权重30%
        
        return influence_score
    
    def _compute_prediction_influence_simplified(self, layer_states: Dict[str, Any],
                                               source_pos: int, target_pos: int,
                                               norm_layer, lm_head) -> float:
        """简化版预测影响计算"""
        try:
            token_states = layer_states['token_wise_states']
            if target_pos not in token_states:
                return 0.0
                
            # 获取target_pos的正常预测
            target_hidden = token_states[target_pos].squeeze()
            normalized_hidden = norm_layer(target_hidden.unsqueeze(0)).squeeze(0)
            target_logits = lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)
            target_probs = F.softmax(target_logits, dim=-1)
            
            # 计算预测的确定性（熵的倒数）
            entropy = -torch.sum(target_probs * torch.log(target_probs + 1e-10))
            certainty = 1.0 / (entropy + 1e-6)
            
            return certainty.item() * 0.01  # 缩放到合适范围
            
        except Exception as e:
            logger.warning(f"预测影响计算失败: {e}")
            return 0.0
    
    def analyze_information_flow(self, layer_states: Dict[str, Any]) -> Dict[str, Any]:
        """分析信息在token间的流动模式"""
        if 'token_wise_states' not in layer_states:
            return {}
            
        token_states = layer_states['token_wise_states']
        seq_len = len(token_states)
        
        # 构建信息流图
        flow_graph = np.zeros((seq_len, seq_len))
        
        positions = sorted(token_states.keys())
        for i, pos_i in enumerate(positions):
            for j, pos_j in enumerate(positions):
                if i != j:
                    # 基于注意力权重计算信息流强度
                    if 'attention_weights' in layer_states:
                        attn_weights = layer_states['attention_weights']
                        flow_strength = attn_weights[:, :, pos_j, pos_i].mean().item()
                        flow_graph[i, j] = flow_strength
        
        # 识别关键路径和枢纽节点
        key_paths = self._identify_critical_paths(flow_graph)
        hub_tokens = self._identify_hub_tokens(flow_graph, positions)
        
        return {
            'flow_matrix': flow_graph,
            'key_paths': key_paths,
            'hub_tokens': hub_tokens,
            'positions': positions
        }
    
    def _identify_critical_paths(self, flow_graph: np.ndarray) -> List[Tuple[int, int, float]]:
        """识别关键信息流路径"""
        paths = []

        # 检查是否有正值
        positive_values = flow_graph[flow_graph > 0]
        if len(positive_values) == 0:
            logger.warning("流图中没有正值连接")
            return paths

        threshold = np.percentile(positive_values, 80)  # 取前20%的连接

        for i in range(flow_graph.shape[0]):
            for j in range(flow_graph.shape[1]):
                if flow_graph[i, j] > threshold:
                    paths.append((i, j, flow_graph[i, j]))

        return sorted(paths, key=lambda x: x[2], reverse=True)[:10]  # 返回前10个关键路径
    
    def _identify_hub_tokens(self, flow_graph: np.ndarray, positions: List[int]) -> List[Dict[str, Any]]:
        """识别枢纽token（高度连接的节点）"""
        in_degrees = np.sum(flow_graph, axis=0)
        out_degrees = np.sum(flow_graph, axis=1)
        total_degrees = in_degrees + out_degrees
        
        # 找到度数最高的节点
        hub_indices = np.argsort(total_degrees)[-5:]  # 前5个枢纽
        
        hubs = []
        for idx in hub_indices:
            if idx < len(positions):
                hubs.append({
                    'position': positions[idx],
                    'in_degree': in_degrees[idx],
                    'out_degree': out_degrees[idx],
                    'total_degree': total_degrees[idx]
                })
        
        return sorted(hubs, key=lambda x: x['total_degree'], reverse=True)
    
    def analyze_token_clusters(self, layer_states: Dict[str, Any]) -> Dict[str, Any]:
        """识别功能相似的token群组"""
        if 'token_wise_states' not in layer_states:
            return {}
            
        token_states = layer_states['token_wise_states']
        positions = sorted(token_states.keys())
        
        # 提取token embeddings
        embeddings = []
        for pos in positions:
            # 转换BFloat16到Float32再转numpy
            embedding = token_states[pos].squeeze().cpu().float().numpy()
            embeddings.append(embedding)
        
        embeddings = np.array(embeddings)
        
        # 使用层次聚类
        n_clusters = min(5, len(positions) // 3)  # 动态确定聚类数
        if n_clusters < 2:
            return {'clusters': [], 'cluster_assignments': []}
            
        clustering = AgglomerativeClustering(n_clusters=n_clusters, linkage='ward')
        cluster_labels = clustering.fit_predict(embeddings)
        
        # 组织聚类结果
        clusters = {}
        for i, label in enumerate(cluster_labels):
            if label not in clusters:
                clusters[label] = []
            clusters[label].append(positions[i])
        
        return {
            'clusters': clusters,
            'cluster_assignments': cluster_labels.tolist(),
            'positions': positions
        }


class SingleLayerCrossTokenAnalyzer:
    """单层跨Token分析器 - 主要分析类"""

    def __init__(self, config: CrossTokenAnalysisConfig):
        self.config = config
        self.device = torch.device(config.device)

        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)

        # 设置随机种子
        set_seed(1234)

        # 初始化组件
        self.hook_manager = EnhancedCrossTokenHookManager()
        self.metrics_calculator = CrossTokenMetrics(config)

        # 初始化模型和处理器
        self._load_model()

        # 结果存储
        self.analysis_results = {}

        logger.info("单层跨Token分析器初始化完成")

    def _load_model(self):
        """加载PaliGemma2模型和处理器"""
        logger.info("正在加载PaliGemma2模型...")

        try:
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.config.model_path)

            # 加载模型
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)

            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device
            )

            # 设置为评估模式
            self.model.eval()

            # 获取模型架构信息
            self.num_layers = len(self.model.language_model.model.layers)
            self.hidden_size = self.model.language_model.config.hidden_size
            self.vocab_size = self.model.language_model.config.vocab_size

            # 获取关键组件
            self.norm_layer = self.model.language_model.model.norm
            self.lm_head = self.model.language_model.lm_head

            logger.info(f"模型加载成功:")
            logger.info(f"  - 语言模型层数: {self.num_layers}")
            logger.info(f"  - 隐藏维度: {self.hidden_size}")
            logger.info(f"  - 词汇表大小: {self.vocab_size}")

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise

    def analyze_layer_cross_token(self, image_path: str, prompt: str,
                                 target_layer: int, generation_step: int = 0) -> Dict[str, Any]:
        """
        🎯 分析指定层的跨Token关系 - 核心方法

        Args:
            image_path: 输入图像路径
            prompt: 输入提示文本
            target_layer: 要分析的目标层
            generation_step: 生成步骤（0表示初始状态）
        """
        logger.info(f"🎯 开始单层跨Token分析...")
        logger.info(f"📸 图像: {image_path}")
        logger.info(f"💬 提示: {prompt}")
        logger.info(f"🧠 目标层: {target_layer}")
        logger.info(f"📊 生成步骤: {generation_step}")

        # 验证层索引
        if target_layer >= self.num_layers:
            raise ValueError(f"层索引 {target_layer} 超出范围 [0, {self.num_layers-1}]")

        # 准备输入
        image = Image.open(image_path).convert('RGB')
        inputs = self.processor(text=prompt, images=image, return_tensors="pt")

        # 移动到设备
        for key, value in inputs.items():
            if torch.is_tensor(value):
                inputs[key] = value.to(self.device)

        # 如果需要生成到特定步骤，先进行生成
        if generation_step > 0:
            inputs = self._generate_to_step(inputs, generation_step)

        # 注册Hook
        self.hook_manager.register_cross_token_hooks(self.model, target_layer)
        self.hook_manager.reset_for_step(0)

        # 执行前向传播以捕获状态
        with torch.no_grad():
            try:
                # 关键修复：添加output_attentions=True来获取注意力权重
                outputs = self.model(**inputs, output_hidden_states=True, output_attentions=True, return_dict=True)

                # 从模型输出中提取注意力权重
                if hasattr(outputs, 'language_model_outputs') and outputs.language_model_outputs is not None:
                    if hasattr(outputs.language_model_outputs, 'attentions'):
                        all_attentions = outputs.language_model_outputs.attentions
                        if target_layer < len(all_attentions):
                            layer_attention = all_attentions[target_layer]
                            # 存储到captured_states中
                            if 0 not in self.hook_manager.captured_states:
                                self.hook_manager.captured_states[0] = {}
                            if target_layer not in self.hook_manager.captured_states[0]:
                                self.hook_manager.captured_states[0][target_layer] = {}
                            self.hook_manager.captured_states[0][target_layer]['attention_weights'] = layer_attention.clone().detach()
                            logger.info(f"✅ 成功从模型输出获取层 {target_layer} 的注意力权重: {layer_attention.shape}")
                        else:
                            logger.warning(f"目标层 {target_layer} 超出注意力权重范围")
                    else:
                        logger.warning("模型输出中没有注意力权重")
                elif hasattr(outputs, 'attentions'):
                    all_attentions = outputs.attentions
                    if target_layer < len(all_attentions):
                        layer_attention = all_attentions[target_layer]
                        # 存储到captured_states中
                        if 0 not in self.hook_manager.captured_states:
                            self.hook_manager.captured_states[0] = {}
                        if target_layer not in self.hook_manager.captured_states[0]:
                            self.hook_manager.captured_states[0][target_layer] = {}
                        self.hook_manager.captured_states[0][target_layer]['attention_weights'] = layer_attention.clone().detach()
                        logger.info(f"✅ 成功从直接输出获取层 {target_layer} 的注意力权重: {layer_attention.shape}")
                    else:
                        logger.warning(f"目标层 {target_layer} 超出注意力权重范围")
                else:
                    logger.warning("模型输出中没有注意力权重")

            except Exception as e:
                logger.error(f"模型前向传播失败: {e}")
                self.hook_manager.clear_hooks()
                return {}

        # 获取捕获的状态
        layer_states = self.hook_manager.get_captured_states(0, target_layer)

        # 清除hooks
        self.hook_manager.clear_hooks()

        if not layer_states:
            logger.error(f"未能捕获层 {target_layer} 的状态")
            return {}

        # 执行跨Token分析
        analysis_results = self._perform_cross_token_analysis(
            layer_states, target_layer, inputs['input_ids'].shape[1]
        )

        # 添加元信息
        analysis_results.update({
            'target_layer': target_layer,
            'generation_step': generation_step,
            'sequence_length': inputs['input_ids'].shape[1],
            'image_path': image_path,
            'prompt': prompt,
            'model_info': {
                'model_path': self.config.model_path,
                'num_layers': self.num_layers,
                'hidden_size': self.hidden_size
            }
        })

        # 存储结果
        self.analysis_results[f"layer_{target_layer}_step_{generation_step}"] = analysis_results

        logger.info(f"✅ 单层跨Token分析完成")
        logger.info(f"   📊 分析了 {len(analysis_results.get('analyzed_positions', []))} 个位置")
        logger.info(f"   🔗 计算了 {len(analysis_results.get('influence_matrix', {}))} 个影响关系")

        return analysis_results

    def _generate_to_step(self, initial_inputs: Dict[str, torch.Tensor],
                         target_step: int) -> Dict[str, torch.Tensor]:
        """生成到指定步骤"""
        current_inputs = initial_inputs

        for step in range(target_step):
            with torch.no_grad():
                outputs = self.model(**current_inputs, return_dict=True)
                next_token_logits = outputs.logits[0, -1, :]
                next_token_id = next_token_logits.argmax().item()

                # 检查是否结束
                if next_token_id == self.processor.tokenizer.eos_token_id:
                    break

                # 更新输入
                new_token_tensor = torch.tensor([[next_token_id]], device=self.device)
                updated_input_ids = torch.cat([current_inputs['input_ids'], new_token_tensor], dim=1)

                new_attention = torch.ones((1, 1), device=self.device,
                                         dtype=current_inputs['attention_mask'].dtype)
                updated_attention_mask = torch.cat([current_inputs['attention_mask'], new_attention], dim=1)

                current_inputs = {
                    'input_ids': updated_input_ids,
                    'attention_mask': updated_attention_mask,
                    'pixel_values': current_inputs['pixel_values']
                }

        return current_inputs

    def _perform_cross_token_analysis(self, layer_states: Dict[str, Any],
                                    target_layer: int, sequence_length: int) -> Dict[str, Any]:
        """执行跨Token分析的核心方法"""
        logger.info(f"🔬 开始执行层 {target_layer} 的跨Token分析...")

        # 确定要分析的位置
        analyzed_positions = self._determine_analysis_positions(sequence_length)
        position_types = self._classify_positions(analyzed_positions, sequence_length)

        logger.info(f"   📍 分析位置数: {len(analyzed_positions)}")
        logger.info(f"   📊 位置类型: {self._count_position_types(position_types)}")

        analysis_results = {
            'analyzed_positions': analyzed_positions,
            'position_types': position_types,
            'analysis_summary': {}
        }

        # 1. Token影响力矩阵分析
        if self.config.analyze_influence_matrix:
            logger.info("   🔗 计算Token影响力矩阵...")
            influence_matrix = self._compute_influence_matrix(
                layer_states, analyzed_positions
            )
            analysis_results['influence_matrix'] = influence_matrix

        # 2. 信息流动分析
        if self.config.analyze_information_flow:
            logger.info("   🌊 分析信息流动模式...")
            flow_analysis = self.metrics_calculator.analyze_information_flow(layer_states)
            analysis_results['information_flow'] = flow_analysis

        # 3. Token聚类分析
        if self.config.analyze_token_clusters:
            logger.info("   🎯 进行Token聚类分析...")
            cluster_analysis = self.metrics_calculator.analyze_token_clusters(layer_states)
            analysis_results['token_clusters'] = cluster_analysis

        # 4. 位置模式分析
        if self.config.analyze_positional_patterns:
            logger.info("   📐 分析位置模式...")
            positional_patterns = self._analyze_positional_patterns(
                layer_states, analyzed_positions, position_types
            )
            analysis_results['positional_patterns'] = positional_patterns

        # 5. 生成分析摘要
        analysis_results['analysis_summary'] = self._generate_analysis_summary(analysis_results)

        logger.info(f"✅ 层 {target_layer} 跨Token分析完成")
        return analysis_results

    def _determine_analysis_positions(self, sequence_length: int) -> List[int]:
        """确定要分析的位置"""
        positions = []

        vision_patch_count = 256

        # 1. Vision patches - 支持采样
        if self.config.analyze_all_positions:
            vision_sample_count = int(vision_patch_count * self.config.vision_patch_sample_rate)
            if vision_sample_count == vision_patch_count:
                positions.extend(list(range(vision_patch_count)))
            else:
                # 均匀采样
                step_size = max(1, vision_patch_count // vision_sample_count)
                sampled_patches = list(range(0, vision_patch_count, step_size))[:vision_sample_count]
                positions.extend(sampled_patches)

        # 2. Text tokens - 限制数量
        text_start = vision_patch_count
        if sequence_length > text_start:
            text_positions = list(range(text_start, sequence_length))
            if len(text_positions) > self.config.text_positions_limit:
                # 取最后N个位置（包括最新生成的token）
                text_positions = text_positions[-self.config.text_positions_limit:]
            positions.extend(text_positions)

        return sorted(positions)

    def _classify_positions(self, positions: List[int], sequence_length: int) -> Dict[int, str]:
        """分类位置类型"""
        position_types = {}

        vision_patch_count = 256
        initial_seq_len = 261  # 估算的初始序列长度

        for pos in positions:
            if pos < vision_patch_count:
                position_types[pos] = 'vision_patch'
            elif pos < initial_seq_len:
                position_types[pos] = 'prompt_token'
            else:
                position_types[pos] = 'generated_token'

        return position_types

    def _count_position_types(self, position_types: Dict[int, str]) -> Dict[str, int]:
        """统计位置类型数量"""
        counts = {'vision_patch': 0, 'prompt_token': 0, 'generated_token': 0}
        for pos_type in position_types.values():
            if pos_type in counts:
                counts[pos_type] += 1
        return counts

    def _compute_influence_matrix(self, layer_states: Dict[str, Any],
                                positions: List[int]) -> Dict[str, Any]:
        """计算Token间影响力矩阵"""
        n_positions = len(positions)
        influence_matrix = np.zeros((n_positions, n_positions))

        # 计算每对位置间的影响力
        for i, pos_i in enumerate(positions):
            for j, pos_j in enumerate(positions):
                if i != j:
                    influence = self.metrics_calculator.compute_cross_token_influence(
                        layer_states, pos_i, pos_j, self.norm_layer, self.lm_head
                    )
                    influence_matrix[i, j] = influence

        # 分析影响力模式
        top_influences = self._find_top_influences(influence_matrix, positions)
        influence_stats = self._compute_influence_statistics(influence_matrix)

        return {
            'matrix': influence_matrix,
            'positions': positions,
            'top_influences': top_influences,
            'statistics': influence_stats
        }

    def _find_top_influences(self, influence_matrix: np.ndarray,
                           positions: List[int], top_k: int = 10) -> List[Dict[str, Any]]:
        """找到最强的影响关系"""
        influences = []

        for i in range(influence_matrix.shape[0]):
            for j in range(influence_matrix.shape[1]):
                if i != j and influence_matrix[i, j] > 0:
                    influences.append({
                        'source_idx': i,
                        'target_idx': j,
                        'source_pos': positions[i],
                        'target_pos': positions[j],
                        'influence_score': influence_matrix[i, j]
                    })

        # 按影响力排序
        influences.sort(key=lambda x: x['influence_score'], reverse=True)
        return influences[:top_k]

    def _compute_influence_statistics(self, influence_matrix: np.ndarray) -> Dict[str, float]:
        """计算影响力统计信息"""
        non_zero_influences = influence_matrix[influence_matrix > 0]

        if len(non_zero_influences) == 0:
            return {
                'mean_influence': 0.0,
                'max_influence': 0.0,
                'std_influence': 0.0,
                'sparsity': 1.0
            }

        return {
            'mean_influence': float(np.mean(non_zero_influences)),
            'max_influence': float(np.max(non_zero_influences)),
            'std_influence': float(np.std(non_zero_influences)),
            'sparsity': float(np.sum(influence_matrix == 0) / influence_matrix.size)
        }

    def _analyze_positional_patterns(self, layer_states: Dict[str, Any],
                                   positions: List[int],
                                   position_types: Dict[int, str]) -> Dict[str, Any]:
        """分析不同位置类型的token行为模式"""
        patterns = {
            'vision_to_vision': [],
            'vision_to_text': [],
            'text_to_vision': [],
            'text_to_text': []
        }

        # 分析不同类型间的交互
        for src_pos in positions:
            for tgt_pos in positions:
                if src_pos != tgt_pos:
                    src_type = position_types.get(src_pos, 'unknown')
                    tgt_type = position_types.get(tgt_pos, 'unknown')

                    # 简化类型映射
                    src_category = 'vision' if src_type == 'vision_patch' else 'text'
                    tgt_category = 'vision' if tgt_type == 'vision_patch' else 'text'

                    interaction_type = f"{src_category}_to_{tgt_category}"

                    if interaction_type in patterns:
                        influence = self.metrics_calculator.compute_cross_token_influence(
                            layer_states, src_pos, tgt_pos, self.norm_layer, self.lm_head
                        )
                        patterns[interaction_type].append({
                            'source': src_pos,
                            'target': tgt_pos,
                            'influence': influence,
                            'source_type': src_type,
                            'target_type': tgt_type
                        })

        # 计算每种模式的统计信息
        pattern_stats = {}
        for pattern_type, interactions in patterns.items():
            if interactions:
                influences = [inter['influence'] for inter in interactions]
                pattern_stats[pattern_type] = {
                    'count': len(interactions),
                    'mean_influence': np.mean(influences),
                    'max_influence': np.max(influences),
                    'std_influence': np.std(influences)
                }
            else:
                pattern_stats[pattern_type] = {
                    'count': 0,
                    'mean_influence': 0.0,
                    'max_influence': 0.0,
                    'std_influence': 0.0
                }

        return {
            'patterns': patterns,
            'statistics': pattern_stats
        }

    def _generate_analysis_summary(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析摘要"""
        summary = {
            'total_positions': len(analysis_results.get('analyzed_positions', [])),
            'position_breakdown': self._count_position_types(
                analysis_results.get('position_types', {})
            )
        }

        # 影响力矩阵摘要
        if 'influence_matrix' in analysis_results:
            influence_data = analysis_results['influence_matrix']
            summary['influence_summary'] = {
                'total_connections': len(influence_data.get('top_influences', [])),
                'strongest_influence': influence_data.get('statistics', {}).get('max_influence', 0),
                'average_influence': influence_data.get('statistics', {}).get('mean_influence', 0)
            }

        # 信息流摘要
        if 'information_flow' in analysis_results:
            flow_data = analysis_results['information_flow']
            summary['flow_summary'] = {
                'hub_tokens_count': len(flow_data.get('hub_tokens', [])),
                'critical_paths_count': len(flow_data.get('key_paths', []))
            }

        # 聚类摘要
        if 'token_clusters' in analysis_results:
            cluster_data = analysis_results['token_clusters']
            summary['cluster_summary'] = {
                'num_clusters': len(cluster_data.get('clusters', {})),
                'largest_cluster_size': max([len(cluster) for cluster in cluster_data.get('clusters', {}).values()], default=0)
            }

        return summary

    def create_cross_token_visualizations(self, analysis_results: Dict[str, Any],
                                        save_path: str, image_path: str):
        """创建跨Token分析的可视化"""
        if not self.config.enable_visualization:
            return

        logger.info("🎨 创建跨Token分析可视化...")

        # 1. 创建影响力热力图
        if self.config.create_influence_heatmap and 'influence_matrix' in analysis_results:
            self._create_influence_heatmap(analysis_results, save_path)

        # 2. 创建信息流网络图
        if self.config.create_flow_network and 'information_flow' in analysis_results:
            self._create_flow_network_plot(analysis_results, save_path)

        # 3. 创建位置模式分析图
        if 'positional_patterns' in analysis_results:
            self._create_positional_patterns_plot(analysis_results, save_path)

        # 4. 创建交互式HTML
        if self.config.create_interactive_html:
            self._create_interactive_cross_token_html(analysis_results, save_path, image_path)

    def _create_influence_heatmap(self, analysis_results: Dict[str, Any], save_path: str):
        """创建Token影响力热力图"""
        influence_data = analysis_results['influence_matrix']
        matrix = influence_data['matrix']
        positions = influence_data['positions']

        plt.figure(figsize=(12, 10))

        # 创建热力图
        sns.heatmap(matrix,
                   xticklabels=[f"P{p}" for p in positions],
                   yticklabels=[f"P{p}" for p in positions],
                   cmap='viridis',
                   cbar_kws={'label': 'Influence Score'})

        plt.title(f'Cross-Token Influence Matrix - Layer {analysis_results["target_layer"]}')
        plt.xlabel('Target Position')
        plt.ylabel('Source Position')
        plt.tight_layout()

        heatmap_path = save_path.replace('.png', '_influence_heatmap.png')
        plt.savefig(heatmap_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"✅ 影响力热力图保存: {heatmap_path}")

    def _create_flow_network_plot(self, analysis_results: Dict[str, Any], save_path: str):
        """创建信息流网络图"""
        flow_data = analysis_results['information_flow']

        if 'flow_matrix' not in flow_data or 'positions' not in flow_data:
            logger.warning("信息流数据不完整，跳过网络图创建")
            return

        flow_matrix = flow_data['flow_matrix']
        positions = flow_data['positions']

        # 创建网络图
        G = nx.DiGraph()

        # 添加节点
        for i, pos in enumerate(positions):
            G.add_node(i, position=pos)

        # 添加边（只添加强连接）
        threshold = np.percentile(flow_matrix[flow_matrix > 0], 75) if np.any(flow_matrix > 0) else 0

        for i in range(flow_matrix.shape[0]):
            for j in range(flow_matrix.shape[1]):
                if flow_matrix[i, j] > threshold:
                    G.add_edge(i, j, weight=flow_matrix[i, j])

        # 绘制网络图
        plt.figure(figsize=(14, 10))

        pos_layout = nx.spring_layout(G, k=1, iterations=50)

        # 绘制节点
        nx.draw_networkx_nodes(G, pos_layout, node_color='lightblue',
                              node_size=300, alpha=0.7)

        # 绘制边
        edges = G.edges()
        weights = [G[u][v]['weight'] for u, v in edges]
        nx.draw_networkx_edges(G, pos_layout, edgelist=edges,
                              width=[w*5 for w in weights], alpha=0.6,
                              edge_color='gray', arrows=True)

        # 添加标签
        labels = {i: f"P{positions[i]}" for i in range(len(positions))}
        nx.draw_networkx_labels(G, pos_layout, labels, font_size=8)

        plt.title(f'Information Flow Network - Layer {analysis_results["target_layer"]}')
        plt.axis('off')
        plt.tight_layout()

        network_path = save_path.replace('.png', '_flow_network.png')
        plt.savefig(network_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"✅ 信息流网络图保存: {network_path}")

    def _create_positional_patterns_plot(self, analysis_results: Dict[str, Any], save_path: str):
        """创建位置模式分析图"""
        pattern_data = analysis_results['positional_patterns']
        pattern_stats = pattern_data['statistics']

        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 交互类型统计
        ax1 = axes[0, 0]
        pattern_types = list(pattern_stats.keys())
        counts = [pattern_stats[pt]['count'] for pt in pattern_types]

        ax1.bar(pattern_types, counts, color=['blue', 'green', 'orange', 'red'])
        ax1.set_title('Interaction Type Counts')
        ax1.set_ylabel('Number of Interactions')
        ax1.tick_params(axis='x', rotation=45)

        # 2. 平均影响力对比
        ax2 = axes[0, 1]
        mean_influences = [pattern_stats[pt]['mean_influence'] for pt in pattern_types]

        ax2.bar(pattern_types, mean_influences, color=['blue', 'green', 'orange', 'red'])
        ax2.set_title('Average Influence by Interaction Type')
        ax2.set_ylabel('Average Influence Score')
        ax2.tick_params(axis='x', rotation=45)

        # 3. 影响力分布
        ax3 = axes[1, 0]
        for i, pattern_type in enumerate(pattern_types):
            if pattern_stats[pattern_type]['count'] > 0:
                interactions = pattern_data['patterns'][pattern_type]
                influences = [inter['influence'] for inter in interactions]
                ax3.hist(influences, alpha=0.7, label=pattern_type, bins=10)

        ax3.set_title('Influence Score Distribution')
        ax3.set_xlabel('Influence Score')
        ax3.set_ylabel('Frequency')
        ax3.legend()

        # 4. 最强影响关系
        ax4 = axes[1, 1]
        top_influences = []
        top_labels = []

        for pattern_type in pattern_types:
            if pattern_stats[pattern_type]['max_influence'] > 0:
                top_influences.append(pattern_stats[pattern_type]['max_influence'])
                top_labels.append(pattern_type)

        if top_influences:
            ax4.barh(top_labels, top_influences, color=['blue', 'green', 'orange', 'red'][:len(top_labels)])
            ax4.set_title('Strongest Influence by Type')
            ax4.set_xlabel('Max Influence Score')

        plt.tight_layout()

        patterns_path = save_path.replace('.png', '_positional_patterns.png')
        plt.savefig(patterns_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"✅ 位置模式分析图保存: {patterns_path}")

    def _create_interactive_cross_token_html(self, analysis_results: Dict[str, Any],
                                           save_path: str, image_path: str):
        """创建交互式跨Token分析HTML"""
        logger.info("🌐 创建交互式跨Token分析HTML...")

        # 编码图像
        import base64
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.warning(f"图像编码失败: {e}")
            image_base64 = ""

        # 序列化数据
        serializable_data = self._make_serializable(analysis_results)

        # 构建HTML文件名
        base_dir = os.path.dirname(save_path)
        layer_idx = analysis_results['target_layer']
        step = analysis_results['generation_step']
        html_filename = f"cross_token_analysis_layer_{layer_idx}_step_{step}.html"
        html_save_path = os.path.join(base_dir, html_filename)

        # 生成HTML内容
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma2 单层跨Token分析 - Layer {layer_idx}</title>
    <style>
        body {{ margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }}
        .container {{ max-width: 1800px; margin: 0 auto; }}
        .header {{ text-align: center; background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .main-content {{ display: flex; gap: 20px; }}
        .left-panel {{ flex: 0 0 400px; background: white; padding: 20px; border-radius: 10px; }}
        .right-panel {{ flex: 1; background: white; padding: 20px; border-radius: 10px; }}

        .analysis-section {{ margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; }}
        .influence-matrix {{ width: 100%; border-collapse: collapse; margin: 10px 0; }}
        .influence-matrix th, .influence-matrix td {{ border: 1px solid #ddd; padding: 4px; text-align: center; font-size: 10px; }}
        .influence-matrix th {{ background: #f2f2f2; }}

        .high-influence {{ background-color: #ff5722; color: white; }}
        .medium-influence {{ background-color: #ff9800; color: white; }}
        .low-influence {{ background-color: #4caf50; color: white; }}
        .no-influence {{ background-color: #e0e0e0; }}

        .stats-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; }}
        .stat-card {{ background: white; padding: 15px; border-radius: 8px; border-left: 4px solid #2196f3; }}
        .stat-value {{ font-size: 24px; font-weight: bold; color: #2196f3; }}
        .stat-label {{ color: #666; margin-top: 5px; }}

        .top-influences {{ margin: 15px 0; }}
        .influence-item {{ display: flex; justify-content: space-between; padding: 8px; margin: 5px 0; background: white; border-radius: 4px; }}
        .position-badge {{ padding: 2px 6px; border-radius: 3px; font-size: 11px; }}
        .vision-badge {{ background: #2196f3; color: white; }}
        .prompt-badge {{ background: #4caf50; color: white; }}
        .generated-badge {{ background: #ff9800; color: white; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 PaliGemma2 单层跨Token分析</h1>
            <h2>Layer {layer_idx} - Step {step}</h2>
            <p><strong>提示:</strong> "{analysis_results['prompt']}"</p>
            <p><strong>序列长度:</strong> {analysis_results['sequence_length']} | <strong>分析位置:</strong> {len(analysis_results['analyzed_positions'])}</p>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <div class="image-container">
                    <h3>📸 源图像</h3>
                    {"<img src='data:image/jpeg;base64," + image_base64 + "' style='max-width: 100%; border-radius: 8px;'>" if image_base64 else "<p>图像不可用</p>"}
                </div>

                <div class="analysis-section">
                    <h3>📊 分析摘要</h3>
                    <div class="stats-grid" id="summaryStats">
                        <!-- 动态内容 -->
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>🔗 最强影响关系</h3>
                    <div class="top-influences" id="topInfluences">
                        <!-- 动态内容 -->
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <div class="analysis-section">
                    <h3>🔥 Token影响力矩阵</h3>
                    <div style="overflow: auto; max-height: 600px;">
                        <table class="influence-matrix" id="influenceMatrix">
                            <!-- 动态内容 -->
                        </table>
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>🌊 信息流动分析</h3>
                    <div id="flowAnalysis">
                        <!-- 动态内容 -->
                    </div>
                </div>

                <div class="analysis-section">
                    <h3>📐 位置模式分析</h3>
                    <div id="positionalAnalysis">
                        <!-- 动态内容 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
    const analysisData = {json.dumps(serializable_data, indent=2)};

    // 初始化显示
    updateDisplay();

    function updateDisplay() {{
        updateSummaryStats();
        updateTopInfluences();
        updateInfluenceMatrix();
        updateFlowAnalysis();
        updatePositionalAnalysis();
    }}

    function updateSummaryStats() {{
        const summary = analysisData.analysis_summary || {{}};
        const summaryDiv = document.getElementById('summaryStats');

        let html = '';

        // 基本统计
        html += `
        <div class="stat-card">
            <div class="stat-value">${{summary.total_positions || 0}}</div>
            <div class="stat-label">总分析位置</div>
        </div>
        `;

        // 位置类型分布
        const breakdown = summary.position_breakdown || {{}};
        html += `
        <div class="stat-card">
            <div class="stat-value">${{breakdown.vision_patch || 0}}</div>
            <div class="stat-label">视觉Patches</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${{breakdown.prompt_token || 0}}</div>
            <div class="stat-label">提示Tokens</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${{breakdown.generated_token || 0}}</div>
            <div class="stat-label">生成Tokens</div>
        </div>
        `;

        // 影响力统计
        const influenceSum = summary.influence_summary || {{}};
        html += `
        <div class="stat-card">
            <div class="stat-value">${{(influenceSum.strongest_influence || 0).toFixed(3)}}</div>
            <div class="stat-label">最强影响力</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">${{(influenceSum.average_influence || 0).toFixed(3)}}</div>
            <div class="stat-label">平均影响力</div>
        </div>
        `;

        summaryDiv.innerHTML = html;
    }}

    function updateTopInfluences() {{
        const influences = analysisData.influence_matrix?.top_influences || [];
        const topDiv = document.getElementById('topInfluences');

        let html = '';
        influences.slice(0, 10).forEach((inf, idx) => {{
            const sourceType = getPositionType(inf.source_pos);
            const targetType = getPositionType(inf.target_pos);

            html += `
            <div class="influence-item">
                <div>
                    <span class="position-badge ${{sourceType}}-badge">P${{inf.source_pos}}</span>
                    →
                    <span class="position-badge ${{targetType}}-badge">P${{inf.target_pos}}</span>
                </div>
                <div style="font-weight: bold;">${{inf.influence_score.toFixed(4)}}</div>
            </div>
            `;
        }});

        topDiv.innerHTML = html || '<p>暂无影响关系数据</p>';
    }}

    function updateInfluenceMatrix() {{
        const matrixData = analysisData.influence_matrix;
        if (!matrixData) return;

        const matrix = matrixData.matrix;
        const positions = matrixData.positions;
        const matrixDiv = document.getElementById('influenceMatrix');

        let html = '<thead><tr><th>Source\\Target</th>';
        positions.forEach(pos => {{
            html += `<th>P${{pos}}</th>`;
        }});
        html += '</tr></thead><tbody>';

        for (let i = 0; i < matrix.length; i++) {{
            html += `<tr><th>P${{positions[i]}}</th>`;
            for (let j = 0; j < matrix[i].length; j++) {{
                const value = matrix[i][j];
                const cellClass = getInfluenceClass(value);
                const displayValue = value > 0 ? value.toFixed(3) : '-';
                html += `<td class="${{cellClass}}">${{displayValue}}</td>`;
            }}
            html += '</tr>';
        }}
        html += '</tbody>';

        matrixDiv.innerHTML = html;
    }}

    function updateFlowAnalysis() {{
        const flowData = analysisData.information_flow || {{}};
        const flowDiv = document.getElementById('flowAnalysis');

        let html = '<h4>🎯 枢纽Tokens</h4>';
        const hubs = flowData.hub_tokens || [];
        if (hubs.length > 0) {{
            html += '<ul>';
            hubs.forEach(hub => {{
                const posType = getPositionType(hub.position);
                html += `<li><span class="position-badge ${{posType}}-badge">P${{hub.position}}</span> - 总度数: ${{hub.total_degree.toFixed(3)}}</li>`;
            }});
            html += '</ul>';
        }} else {{
            html += '<p>暂无枢纽节点数据</p>';
        }}

        html += '<h4>🔗 关键路径</h4>';
        const paths = flowData.key_paths || [];
        if (paths.length > 0) {{
            html += '<ul>';
            paths.slice(0, 5).forEach(path => {{
                const sourcePos = flowData.positions?.[path[0]] || path[0];
                const targetPos = flowData.positions?.[path[1]] || path[1];
                html += `<li>P${{sourcePos}} → P${{targetPos}} (强度: ${{path[2].toFixed(3)}})</li>`;
            }});
            html += '</ul>';
        }} else {{
            html += '<p>暂无关键路径数据</p>';
        }}

        flowDiv.innerHTML = html;
    }}

    function updatePositionalAnalysis() {{
        const patternData = analysisData.positional_patterns || {{}};
        const patternDiv = document.getElementById('positionalAnalysis');

        const stats = patternData.statistics || {{}};
        let html = '<div class="stats-grid">';

        Object.entries(stats).forEach(([patternType, stat]) => {{
            html += `
            <div class="stat-card">
                <div class="stat-value">${{stat.count}}</div>
                <div class="stat-label">${{patternType.replace('_', ' → ')}}</div>
                <div style="font-size: 12px; color: #666;">平均: ${{stat.mean_influence.toFixed(4)}}</div>
            </div>
            `;
        }});

        html += '</div>';
        patternDiv.innerHTML = html;
    }}

    function getPositionType(position) {{
        if (position < 256) return 'vision';
        if (position < 261) return 'prompt';
        return 'generated';
    }}

    function getInfluenceClass(value) {{
        if (value === 0) return 'no-influence';
        if (value > 0.1) return 'high-influence';
        if (value > 0.05) return 'medium-influence';
        return 'low-influence';
    }}
</script>
</body>
</html>
        """

        # 保存HTML
        with open(html_save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"✅ 交互式跨Token分析HTML保存: {html_save_path}")
        return html_save_path

    def _make_serializable(self, obj):
        """将对象转换为JSON可序列化格式"""
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, torch.Tensor):
            return obj.cpu().float().numpy().tolist()
        elif isinstance(obj, dict):
            # 确保字典键是字符串
            new_dict = {}
            for key, value in obj.items():
                if isinstance(key, (np.integer, int)):
                    new_key = str(key)
                elif isinstance(key, (np.floating, float)):
                    new_key = str(key)
                else:
                    new_key = key
                new_dict[new_key] = self._make_serializable(value)
            return new_dict
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.floating)):
            return obj.item()
        else:
            return obj


def main():
    """主函数 - 演示单层跨Token分析的使用"""
    import argparse

    parser = argparse.ArgumentParser(description='PaliGemma2 单层跨Token分析')
    parser.add_argument('--image_path', type=str, default='/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images/real_image_20250610_170727.png', help='输入图像路径')
    parser.add_argument('--prompt', type=str, default="Describe this image", help='输入提示文本')
    parser.add_argument('--target_layer', type=int, default=15, help='要分析的目标层')
    parser.add_argument('--generation_step', type=int, default=0, help='生成步骤（0表示初始状态）')
    parser.add_argument('--output_dir', type=str, default='./cross_token_results', help='输出目录')
    parser.add_argument('--model_path', type=str, default='google/paligemma2-3b-pt-224', help='模型路径')
    parser.add_argument('--vision_sample_rate', type=float, default=0.1, help='视觉patch采样率')

    args = parser.parse_args()

    # 检查图像文件是否存在
    if not os.path.exists(args.image_path):
        logger.error(f"图像文件不存在: {args.image_path}")
        logger.info("请提供有效的图像路径，或使用 --image_path 参数指定")
        return

    # 创建配置
    config = CrossTokenAnalysisConfig(
        model_path=args.model_path,
        target_layers=[args.target_layer],
        output_dir=args.output_dir,
        vision_patch_sample_rate=args.vision_sample_rate,
        enable_visualization=True,
        create_influence_heatmap=True,
        create_flow_network=True,
        create_interactive_html=True
    )

    # 创建分析器
    analyzer = SingleLayerCrossTokenAnalyzer(config)

    # 执行分析
    logger.info(f"🚀 开始分析层 {args.target_layer}...")
    results = analyzer.analyze_layer_cross_token(
        image_path=args.image_path,
        prompt=args.prompt,
        target_layer=args.target_layer,
        generation_step=args.generation_step
    )

    # 创建可视化
    save_path = os.path.join(args.output_dir, f"layer_{args.target_layer}_analysis.png")
    analyzer.create_cross_token_visualizations(results, save_path, args.image_path)

    # 保存结果
    results_path = os.path.join(args.output_dir, f"layer_{args.target_layer}_results.json")
    with open(results_path, 'w', encoding='utf-8') as f:
        json.dump(analyzer._make_serializable(results), f, indent=2, ensure_ascii=False)

    logger.info(f"🎉 分析完成！")
    logger.info(f"   📊 结果保存: {results_path}")
    logger.info(f"   🎨 可视化保存: {save_path}")

    # 打印关键发现
    print_key_findings(results)


def print_key_findings(results: Dict[str, Any]):
    """打印关键发现"""
    print("\n" + "="*60)
    print(f"🎯 单层跨Token分析 - 关键发现")
    print("="*60)

    # 基本信息
    print(f"📍 目标层: {results['target_layer']}")
    print(f"📏 序列长度: {results['sequence_length']}")
    print(f"🔍 分析位置数: {len(results['analyzed_positions'])}")

    # 位置类型分布
    summary = results.get('analysis_summary', {})
    breakdown = summary.get('position_breakdown', {})
    print(f"\n📊 位置类型分布:")
    print(f"   🔵 视觉Patches: {breakdown.get('vision_patch', 0)}")
    print(f"   🟢 提示Tokens: {breakdown.get('prompt_token', 0)}")
    print(f"   🟠 生成Tokens: {breakdown.get('generated_token', 0)}")

    # 影响力分析
    if 'influence_matrix' in results:
        influence_data = results['influence_matrix']
        stats = influence_data.get('statistics', {})
        print(f"\n🔗 影响力分析:")
        print(f"   💪 最强影响力: {stats.get('max_influence', 0):.4f}")
        print(f"   📈 平均影响力: {stats.get('mean_influence', 0):.4f}")
        print(f"   🕳️ 稀疏度: {stats.get('sparsity', 0):.2%}")

        # 最强影响关系
        top_influences = influence_data.get('top_influences', [])
        if top_influences:
            print(f"\n🏆 最强影响关系 (Top 5):")
            for i, inf in enumerate(top_influences[:5]):
                print(f"   {i+1}. P{inf['source_pos']} → P{inf['target_pos']}: {inf['influence_score']:.4f}")

    # 信息流分析
    if 'information_flow' in results:
        flow_data = results['information_flow']
        hub_tokens = flow_data.get('hub_tokens', [])
        if hub_tokens:
            print(f"\n🎯 枢纽Tokens (Top 3):")
            for i, hub in enumerate(hub_tokens[:3]):
                print(f"   {i+1}. P{hub['position']}: 总度数 {hub['total_degree']:.4f}")

    # 位置模式分析
    if 'positional_patterns' in results:
        pattern_data = results['positional_patterns']
        pattern_stats = pattern_data.get('statistics', {})
        print(f"\n📐 位置交互模式:")
        for pattern_type, stats in pattern_stats.items():
            if stats['count'] > 0:
                print(f"   {pattern_type.replace('_', ' → ')}: {stats['count']} 个连接, "
                      f"平均影响力 {stats['mean_influence']:.4f}")

    print("\n" + "="*60)


def demo_multi_layer_analysis():
    """演示多层分析的示例"""
    logger.info("🚀 演示多层跨Token分析...")

    # 示例配置
    config = CrossTokenAnalysisConfig(
        target_layers=[10, 15, 20, 25],  # 分析多个关键层
        vision_patch_sample_rate=0.1,   # 降低采样率以加快速度
        output_dir="./multi_layer_cross_token_demo"
    )

    analyzer = SingleLayerCrossTokenAnalyzer(config)

    # 示例输入（需要根据实际情况修改）
    image_path = "example_image.jpg"  # 替换为实际图像路径
    prompt = "What do you see in this image?"

    all_results = {}

    # 分析每一层
    for layer_idx in config.target_layers:
        logger.info(f"🧠 分析层 {layer_idx}...")

        try:
            results = analyzer.analyze_layer_cross_token(
                image_path=image_path,
                prompt=prompt,
                target_layer=layer_idx,
                generation_step=0
            )

            all_results[f"layer_{layer_idx}"] = results

            # 创建该层的可视化
            save_path = os.path.join(config.output_dir, f"layer_{layer_idx}_analysis.png")
            analyzer.create_cross_token_visualizations(results, save_path, image_path)

        except Exception as e:
            logger.error(f"层 {layer_idx} 分析失败: {e}")
            continue

    # 保存所有结果
    all_results_path = os.path.join(config.output_dir, "all_layers_results.json")
    with open(all_results_path, 'w', encoding='utf-8') as f:
        json.dump(analyzer._make_serializable(all_results), f, indent=2, ensure_ascii=False)

    logger.info(f"🎉 多层分析完成！结果保存: {all_results_path}")

    # 比较不同层的特征
    compare_layers(all_results)


def compare_layers(all_results: Dict[str, Dict[str, Any]]):
    """比较不同层的跨Token特征"""
    print("\n" + "="*60)
    print("🔬 多层跨Token特征对比")
    print("="*60)

    for layer_key, results in all_results.items():
        layer_idx = results['target_layer']
        summary = results.get('analysis_summary', {})

        print(f"\n🧠 Layer {layer_idx}:")

        # 影响力特征
        if 'influence_summary' in summary:
            inf_sum = summary['influence_summary']
            print(f"   💪 最强影响力: {inf_sum.get('strongest_influence', 0):.4f}")
            print(f"   📈 平均影响力: {inf_sum.get('average_influence', 0):.4f}")

        # 流动特征
        if 'flow_summary' in summary:
            flow_sum = summary['flow_summary']
            print(f"   🎯 枢纽节点数: {flow_sum.get('hub_tokens_count', 0)}")
            print(f"   🔗 关键路径数: {flow_sum.get('critical_paths_count', 0)}")

        # 聚类特征
        if 'cluster_summary' in summary:
            cluster_sum = summary['cluster_summary']
            print(f"   🎯 聚类数: {cluster_sum.get('num_clusters', 0)}")
            print(f"   📊 最大聚类大小: {cluster_sum.get('largest_cluster_size', 0)}")


if __name__ == "__main__":
    main()
