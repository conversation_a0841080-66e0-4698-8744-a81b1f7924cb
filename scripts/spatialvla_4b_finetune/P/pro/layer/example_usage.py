#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨层分析系统使用示例
==================

展示如何使用跨层Logit Lens分析器进行深度分析

作者: Assistant
日期: 2025-01-14
"""

import os
import sys
from PIL import Image

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cross_layer_logit_lens_analyzer import CrossLayerAnalysisConfig, CrossLayerLogitLensAnalyzer


def example_basic_analysis():
    """基本分析示例"""
    print("🎯 基本跨层分析示例")
    print("=" * 50)
    
    # 创建测试图像
    test_image = Image.new('RGB', (224, 224), color='blue')
    test_image.save("example_image.jpg")
    
    # 配置分析器
    config = CrossLayerAnalysisConfig(
        model_path="google/paligemma2-3b-pt-224",
        max_new_tokens=10,
        target_layers=[0, 5, 10, 15, 20, 25],  # 关键层
        output_dir="./example_basic_results",
        enable_visualization=True
    )
    
    # 创建分析器
    analyzer = CrossLayerLogitLensAnalyzer(config)
    
    # 执行分析
    results = analyzer.analyze_cross_layer_generation(
        image_path="example_image.jpg",
        prompt="What color is this image?"
    )
    
    # 打印结果
    print("\n📊 分析结果:")
    print(f"生成步骤数: {results['generation_results']['generation_steps']}")
    print(f"最终生成文本: '{results['generation_results']['final_generated_text']}'")
    
    print("\n🔍 关键发现:")
    for finding in results['summary']['key_findings']:
        print(f"  • {finding}")
    
    print(f"\n📁 详细结果保存在: {config.output_dir}")
    
    # 清理
    os.remove("example_image.jpg")


def example_detailed_analysis():
    """详细分析示例"""
    print("\n🔬 详细跨层分析示例")
    print("=" * 50)
    
    # 创建更复杂的测试图像
    test_image = Image.new('RGB', (224, 224), color='red')
    test_image.save("detailed_image.jpg")
    
    # 详细配置
    config = CrossLayerAnalysisConfig(
        model_path="google/paligemma2-3b-pt-224",
        max_new_tokens=15,
        target_layers=list(range(0, 26, 2)),  # 每隔一层分析
        analyze_layer_pairs=True,
        track_prediction_trajectory=True,
        measure_representation_distance=True,
        top_k_trajectory=5,
        output_dir="./example_detailed_results",
        enable_visualization=True,
        create_trajectory_heatmap=True
    )
    
    # 创建分析器
    analyzer = CrossLayerLogitLensAnalyzer(config)
    
    # 执行分析
    results = analyzer.analyze_cross_layer_generation(
        image_path="detailed_image.jpg",
        prompt="Describe the color and appearance of this image in detail"
    )
    
    # 详细结果分析
    print("\n📈 详细分析结果:")
    
    # 预测轨迹分析
    trajectories = results['cross_layer_analysis']['prediction_trajectories']
    if trajectories:
        first_trajectory = trajectories[0]
        print(f"关键层数量: {len(first_trajectory['critical_layers'])}")
        print(f"概率突变点: {len(first_trajectory['probability_jumps'])}")
    
    # 层间相似度分析
    similarities = results['cross_layer_analysis']['layer_similarities']
    if similarities:
        first_similarity = similarities[0]
        print(f"层间相似度分析: {len(first_similarity['similarity_matrix'])} 个层对")
        print(f"表示突变检测: {len(first_similarity['representation_shifts'])} 个突变点")
    
    # 层组模式分析
    group_patterns = results['cross_layer_analysis']['layer_group_patterns']
    if group_patterns:
        first_pattern = group_patterns[0]
        print("\n🧠 层组行为模式:")
        for group_name, group_data in first_pattern.items():
            if isinstance(group_data, dict) and 'avg_entropy' in group_data:
                print(f"  {group_name}: 平均熵={group_data['avg_entropy']:.3f}, "
                      f"平均置信度={group_data['avg_confidence']:.3f}")
    
    print(f"\n📁 详细结果保存在: {config.output_dir}")
    
    # 清理
    os.remove("detailed_image.jpg")


def example_custom_analysis():
    """自定义分析示例"""
    print("\n⚙️ 自定义跨层分析示例")
    print("=" * 50)
    
    # 创建测试图像
    test_image = Image.new('RGB', (224, 224), color='green')
    test_image.save("custom_image.jpg")
    
    # 自定义配置 - 专注于特定层
    config = CrossLayerAnalysisConfig(
        model_path="google/paligemma2-3b-pt-224",
        max_new_tokens=8,
        target_layers=[0, 8, 16, 25],  # 只分析几个关键层
        similarity_metrics=["cosine", "kl_divergence"],
        top_k_trajectory=3,
        output_dir="./example_custom_results",
        enable_visualization=True
    )
    
    # 创建分析器
    analyzer = CrossLayerLogitLensAnalyzer(config)
    
    # 执行分析
    results = analyzer.analyze_cross_layer_generation(
        image_path="custom_image.jpg",
        prompt="What is the main color?"
    )
    
    # 自定义结果处理
    print("\n🎯 自定义分析结果:")
    
    # 全局模式分析
    global_patterns = results['cross_layer_analysis']['global_patterns']
    
    if 'critical_layers_across_steps' in global_patterns:
        critical_layers = global_patterns['critical_layers_across_steps']
        print(f"跨步骤关键层: {[layer['layer'] for layer in critical_layers[:3]]}")
    
    if 'prediction_convergence_layers' in global_patterns:
        convergence = global_patterns['prediction_convergence_layers']
        if convergence:
            print(f"预测收敛层: {convergence['average_convergence_layer']:.1f}")
    
    # 层行为模式
    layer_patterns = results['summary']['layer_behavior_patterns']
    print("\n📊 层行为对比:")
    for layer_type, pattern in layer_patterns.items():
        print(f"  {layer_type}: {pattern}")
    
    print(f"\n📁 自定义结果保存在: {config.output_dir}")
    
    # 清理
    os.remove("custom_image.jpg")


def main():
    """主函数 - 运行所有示例"""
    print("🚀 跨层分析系统使用示例")
    print("=" * 60)
    
    try:
        # 运行示例
        example_basic_analysis()
        example_detailed_analysis()
        example_custom_analysis()
        
        print("\n" + "=" * 60)
        print("🎉 所有示例运行完成!")
        print("=" * 60)
        
        print("\n💡 使用提示:")
        print("1. 调整 target_layers 参数来控制分析的层数")
        print("2. 使用 max_new_tokens 控制生成长度")
        print("3. 启用 enable_visualization 查看可视化结果")
        print("4. 查看输出目录中的 JSON 文件获取详细数据")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
