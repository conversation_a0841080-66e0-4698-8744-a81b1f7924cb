# PaliGemma2 跨层Logit Lens分析系统

🎯 **Layer-to-Layer Information Flow Analysis** - 深度分析模型层间信息传递和演化模式

## 🌟 核心功能

### 分析维度
- **预测演化追踪**: 追踪特定token预测概率在各层的演化轨迹
- **层间相似度分析**: 分析相邻层之间的表示相似度和信息增益
- **组件贡献流**: 分析Attention和MLP贡献在层间的变化模式
- **层组模式识别**: 将模型层分组并分析不同组的行为模式

### 核心洞察
1. **信息传递模式**: 信息如何在层间传递和转换？
2. **关键决策层**: 哪些层是"关键决策层"？
3. **预测分布演化**: 预测分布如何逐层演化？
4. **跳跃式传递**: 层间是否存在"跳跃式"的信息传递？
5. **Token类型差异**: 不同类型的token在层间的处理模式差异

## 🚀 快速开始

### 基本使用

```bash
# 基本分析
python cross_layer_logit_lens_analyzer.py \
    --image_path "path/to/your/image.jpg" \
    --prompt "Describe this image" \
    --output_dir "./results"

# 分析所有层
python cross_layer_logit_lens_analyzer.py \
    --image_path "path/to/your/image.jpg" \
    --prompt "What do you see?" \
    --analyze_all_layers \
    --output_dir "./full_analysis"

# 自定义层分析
python cross_layer_logit_lens_analyzer.py \
    --image_path "path/to/your/image.jpg" \
    --prompt "Describe the scene" \
    --target_layers "0,5,10,15,20,25,26" \
    --max_new_tokens 20
```

### Python API使用

```python
from cross_layer_logit_lens_analyzer import CrossLayerAnalysisConfig, CrossLayerLogitLensAnalyzer

# 创建配置
config = CrossLayerAnalysisConfig(
    model_path="google/paligemma2-3b-pt-224",
    max_new_tokens=15,
    target_layers=[0, 5, 10, 15, 20, 25, 26],
    output_dir="./analysis_results"
)

# 创建分析器
analyzer = CrossLayerLogitLensAnalyzer(config)

# 执行分析
results = analyzer.analyze_cross_layer_generation(
    image_path="your_image.jpg",
    prompt="Describe this image"
)

# 查看结果
print("关键发现:", results['summary']['key_findings'])
```

## 📊 输出结果

### 文件结构
```
analysis_results/
├── cross_layer_analysis_results.json    # 完整分析结果
├── analysis_summary.json                # 分析摘要
├── prediction_trajectory_heatmap.png    # 预测轨迹热力图
└── layer_similarity_heatmap.png         # 层间相似度热力图
```

### 结果解读

#### 1. 预测轨迹分析
- **target_token_trajectory**: 目标token在各层的概率演化
- **probability_jumps**: 概率突变点识别
- **critical_layers**: 关键决策层识别

#### 2. 层间相似度分析
- **similarity_matrix**: 相邻层的余弦相似度、L2距离、KL散度
- **information_gains**: 每层的信息增益
- **representation_shifts**: 表示突变检测

#### 3. 层组模式分析
- **low_layers**: 低层行为特征（视觉处理）
- **mid_layers**: 中层行为特征（特征整合）
- **high_layers**: 高层行为特征（语言生成）

## ⚙️ 配置选项

### 核心参数
- `model_path`: 模型路径 (默认: "google/paligemma2-3b-pt-224")
- `max_new_tokens`: 最大生成token数 (默认: 15)
- `target_layers`: 要分析的层 (默认: 所有27层)
- `device`: 计算设备 (默认: "cuda:0")

### 分析控制
- `analyze_layer_pairs`: 是否分析相邻层对 (默认: True)
- `track_prediction_trajectory`: 是否追踪预测轨迹 (默认: True)
- `measure_representation_distance`: 是否测量表示距离 (默认: True)

### 可视化选项
- `enable_visualization`: 启用可视化 (默认: True)
- `create_trajectory_heatmap`: 创建轨迹热力图 (默认: True)
- `create_flow_diagram`: 创建信息流图 (默认: True)

## 🧪 测试

运行测试脚本验证系统功能：

```bash
python test_cross_layer_analyzer.py
```

测试包括：
- 数据结构验证
- 配置选项测试
- 基本功能测试

## 📈 性能优化

### 计算优化
- 使用`target_layers`参数只分析关键层
- 减少`max_new_tokens`以加快分析速度
- 选择合适的`dtype`平衡精度和速度

### 内存优化
- 分析大模型时使用`bf16`或`fp16`
- 避免同时分析所有层和所有位置
- 及时清理中间结果

## 🔬 技术细节

### 核心算法

#### 1. 预测轨迹追踪
```python
def track_prediction_trajectory(hidden_states):
    # 1. 获取最终预测token作为追踪目标
    # 2. 计算该token在各层的概率
    # 3. 识别概率突变点和关键层
```

#### 2. 层间相似度分析
```python
def analyze_layer_similarity(hidden_states):
    # 1. 计算相邻层的余弦相似度
    # 2. 计算预测分布的KL散度
    # 3. 识别表示突变层
```

#### 3. 层组模式识别
```python
def analyze_layer_groups(hidden_states):
    # 1. 将层分为低/中/高三组
    # 2. 分析每组的熵、置信度、稳定性
    # 3. 识别组间转换模式
```

## 🤝 扩展开发

### 添加新的分析维度
1. 继承相应的分析器基类
2. 实现核心分析方法
3. 在主分析器中集成新功能

### 自定义可视化
1. 扩展`CrossLayerVisualizationManager`类
2. 添加新的绘图方法
3. 在配置中添加相应开关

## 📝 引用

如果您在研究中使用了此系统，请引用：

```
PaliGemma2 跨层Logit Lens分析系统
Layer-to-Layer Information Flow Analysis for Vision-Language Models
```

## 🐛 问题反馈

如遇到问题，请检查：
1. 模型路径是否正确
2. 设备内存是否充足
3. 依赖包是否完整安装
4. 输入图像格式是否支持

---

**作者**: Assistant  
**版本**: v1.0  
**日期**: 2025-01-14
