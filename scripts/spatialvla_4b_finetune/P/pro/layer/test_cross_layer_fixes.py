#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试跨层分析修复的脚本
"""

import os
import sys
import logging
import torch

# 设置日志级别为DEBUG以查看更多信息
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入修复后的分析器
from cross_layer_logit_lens_analyzer import CrossLayerAnalysisConfig, CrossLayerLogitLensAnalyzer

def test_cross_layer_analysis():
    """测试跨层分析的修复"""
    
    # 创建测试配置 - 使用更少的层进行快速测试
    config = CrossLayerAnalysisConfig(
        model_path="google/paligemma2-3b-pt-224",
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        dtype="bf16",
        max_new_tokens=5,  # 减少生成步骤以便快速测试
        target_layers=[0, 1, 2, 5, 10, 15, 20, 24, 25, 26],  # 选择代表性层
        output_dir="./test_cross_layer_results",
        enable_visualization=True
    )
    
    # 测试图像和提示
    image_path = '/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images/real_image_20250610_170727.png'
    prompt = 'describe the image'
    
    logger.info("🧪 开始测试跨层分析修复...")
    logger.info(f"📸 测试图像: {image_path}")
    logger.info(f"💬 测试提示: {prompt}")
    
    try:
        # 创建分析器
        analyzer = CrossLayerLogitLensAnalyzer(config)
        
        # 执行分析
        results = analyzer.analyze_cross_layer_generation(image_path, prompt)
        
        # 检查结果
        logger.info("✅ 分析完成，检查结果...")
        
        # 检查生成结果
        gen_results = results['generation_results']
        logger.info(f"📊 生成步骤: {gen_results['generation_steps']}")
        logger.info(f"📝 生成文本: '{gen_results['final_generated_text']}'")
        
        # 检查跨层分析结果
        cross_analysis = results['cross_layer_analysis']
        
        # 检查预测轨迹
        if cross_analysis['prediction_trajectories']:
            first_trajectory = cross_analysis['prediction_trajectories'][0]
            logger.info("🎯 第一步预测轨迹检查:")
            
            target_trajectory = first_trajectory.get('target_token_trajectory', {})
            if target_trajectory:
                min_prob = min(target_trajectory.values())
                max_prob = max(target_trajectory.values())
                logger.info(f"   📈 概率范围: {min_prob:.6f} - {max_prob:.6f}")
                
                # 检查是否所有概率都是极端值
                if min_prob == 0.0 and max_prob == 1.0:
                    logger.warning("⚠️  检测到极端概率值，可能仍存在问题")
                else:
                    logger.info("✅ 概率值看起来正常")
                    
                # 显示每层的概率
                logger.info("   📊 各层概率:")
                for layer_idx in sorted(target_trajectory.keys()):
                    prob = target_trajectory[layer_idx]
                    logger.info(f"      Layer {layer_idx}: {prob:.6f}")
            
            # 检查关键层
            critical_layers = first_trajectory.get('critical_layers', [])
            logger.info(f"🎯 关键层: {critical_layers}")
            
            # 检查概率突变
            prob_jumps = first_trajectory.get('probability_jumps', [])
            logger.info(f"⚡ 概率突变点: {len(prob_jumps)} 个")
            for jump in prob_jumps[:3]:
                logger.info(f"   Layer {jump['layer']}: {jump['change']:+.6f}")
        
        # 检查层间相似度
        if cross_analysis['layer_similarities']:
            first_similarity = cross_analysis['layer_similarities'][0]
            sim_matrix = first_similarity.get('similarity_matrix', {})
            if sim_matrix:
                similarities = [v['cosine_similarity'] for v in sim_matrix.values()]
                avg_sim = sum(similarities) / len(similarities)
                logger.info(f"🔗 平均层间相似度: {avg_sim:.6f}")
                
                # 检查是否有异常相似度值
                extreme_sims = [s for s in similarities if s < 0.01 or s > 0.99]
                if extreme_sims:
                    logger.warning(f"⚠️  检测到 {len(extreme_sims)} 个极端相似度值")
                else:
                    logger.info("✅ 相似度值看起来正常")
        
        # 检查层组分析
        if cross_analysis['layer_group_patterns']:
            first_group = cross_analysis['layer_group_patterns'][0]
            logger.info("🧠 层组分析:")
            for group_name in ['low_layers', 'mid_layers', 'high_layers']:
                if group_name in first_group:
                    group_data = first_group[group_name]
                    entropy = group_data.get('avg_entropy', 0)
                    confidence = group_data.get('avg_confidence', 0)
                    logger.info(f"   {group_name}: 熵={entropy:.6f}, 置信度={confidence:.6f}")
        
        logger.info("🎉 测试完成！")
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_cross_layer_analysis()
    if success:
        print("\n✅ 测试通过 - 修复似乎有效")
    else:
        print("\n❌ 测试失败 - 需要进一步调试")
