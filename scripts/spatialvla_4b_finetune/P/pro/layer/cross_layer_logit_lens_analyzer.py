#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Cross-Layer Logit Lens Analysis System
================================================

🎯 跨层分析PLAN：Layer-to-Layer Information Flow Analysis

### 核心目标与分析维度
1. 信息如何在层间传递和转换？
2. 哪些层是"关键决策层"？
3. 预测分布如何逐层演化？
4. 层间是否存在"跳跃式"的信息传递？
5. 不同类型的token（vision/prompt/generated）在层间的处理模式是否不同？

### 分析维度
- prediction_evolution: 预测分布的逐层变化
- representation_similarity: 相邻层表示的相似度
- information_gain: 每层新增的信息量
- decision_criticality: 每层对最终决策的关键程度
- component_contribution_flow: Attention/MLP贡献的层间变化

作者: Assistant
日期: 2025-01-14
版本: 跨层分析系统 v1.0
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn.functional as F
from dataclasses import dataclass, field
from typing import Dict, List, Any
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
from PIL import Image
import argparse
import logging
import seaborn as sns

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入transformers
try:
    from transformers import (
        AutoProcessor,
        PaliGemmaForConditionalGeneration,
        set_seed
    )
    logger.info("成功导入Transformers组件。")
except ImportError as e:
    logger.error(f"导入Transformers失败: {e}")
    sys.exit(1)

import warnings
warnings.filterwarnings("ignore")


class DetailedOutputManager:
    """详细输出管理器 - 负责生成详尽的终端输出和自然语言描述"""

    def __init__(self, config: 'CrossLayerAnalysisConfig'):
        self.config = config

    def print_step_analysis(self, step: int, step_data: Dict, trajectory_data: Dict,
                           similarity_data: Dict, group_data: Dict, processor):
        """打印每个step的详细自然语言分析"""
        print(f"\n{'='*80}")
        print(f"🔍 STEP {step} 详细分析报告")
        print(f"{'='*80}")

        # 基本信息
        token_text = step_data.get('predicted_token_text', 'Unknown')
        token_id = step_data.get('predicted_token_id', 'Unknown')
        seq_len = step_data.get('sequence_length', 'Unknown')

        print(f"📝 生成Token: '{token_text}' (ID: {token_id})")
        print(f"📏 当前序列长度: {seq_len}")

        # 预测轨迹分析
        self._print_trajectory_analysis(trajectory_data, processor)

        # 层间相似度分析
        self._print_similarity_analysis(similarity_data)

        # 层组行为分析
        self._print_group_analysis(group_data)

        print(f"{'='*80}\n")

    def _print_trajectory_analysis(self, trajectory_data: Dict, processor):
        """打印预测轨迹分析"""
        print(f"\n🎯 预测轨迹分析:")
        print(f"   {'─'*50}")

        if 'target_token_trajectory' in trajectory_data:
            trajectory = trajectory_data['target_token_trajectory']

            # 按置信度排序所有层
            sorted_layers = sorted(trajectory.items(), key=lambda x: x[1], reverse=True)

            # 找到概率最高和最低的层
            max_layer, max_prob = sorted_layers[0]
            min_layer, min_prob = sorted_layers[-1]

            print(f"   🔺 最高置信度: Layer {max_layer} ({max_prob:.3f})")
            print(f"   🔻 最低置信度: Layer {min_layer} ({min_prob:.3f})")
            print(f"   📈 置信度提升: {max_prob - min_prob:.3f}")

            # 显示Top5置信度层
            print(f"   🏆 Top5置信度层:")
            for i, (layer, prob) in enumerate(sorted_layers[:5]):
                rank_emoji = ["🥇", "🥈", "🥉", "🏅", "🎖️"][i]
                print(f"      {rank_emoji} Layer {layer}: {prob:.3f}")

        # 关键层分析
        if 'critical_layers' in trajectory_data and trajectory_data['critical_layers']:
            critical_layers = trajectory_data['critical_layers']
            print(f"   🎯 关键决策层: {critical_layers}")
            print(f"   💡 解释: 这些层对最终预测产生了显著影响")
        else:
            print(f"   ℹ️  本步骤未检测到关键决策层")

        # 概率突变分析
        if 'probability_jumps' in trajectory_data and trajectory_data['probability_jumps']:
            jumps = trajectory_data['probability_jumps']
            print(f"   ⚡ 概率突变点: {len(jumps)} 个")
            for jump in jumps[:3]:  # 只显示前3个
                direction = "↗️" if jump['change'] > 0 else "↘️"
                print(f"      {direction} Layer {jump['layer']}: {jump['change']:+.3f}")
        else:
            print(f"   📊 预测概率平稳演化，无显著突变")

    def _print_similarity_analysis(self, similarity_data: Dict):
        """打印层间相似度分析"""
        print(f"\n🔗 层间相似度分析:")
        print(f"   {'─'*50}")

        if 'similarity_matrix' in similarity_data:
            sim_matrix = similarity_data['similarity_matrix']

            # 找到最相似和最不相似的层对
            if sim_matrix:
                similarities = [(k, v['cosine_similarity']) for k, v in sim_matrix.items()]
                most_similar = max(similarities, key=lambda x: x[1])
                least_similar = min(similarities, key=lambda x: x[1])

                print(f"   🤝 最相似层对: {most_similar[0]} (相似度: {most_similar[1]:.3f})")
                print(f"   🔄 最不相似层对: {least_similar[0]} (相似度: {least_similar[1]:.3f})")

                # 平均相似度
                avg_similarity = sum(s[1] for s in similarities) / len(similarities)
                print(f"   📊 平均层间相似度: {avg_similarity:.3f}")

        # 表示突变分析
        if 'representation_shifts' in similarity_data and similarity_data['representation_shifts']:
            shifts = similarity_data['representation_shifts']
            print(f"   ⚠️  检测到 {len(shifts)} 个表示突变:")
            for shift in shifts[:2]:  # 只显示前2个
                print(f"      🔀 Layer {shift['from_layer']} → {shift['to_layer']} (严重度: {shift['severity']:.3f})")
        else:
            print(f"   ✅ 层间表示平滑过渡，无突变")

    def _print_group_analysis(self, group_data: Dict):
        """打印层组行为分析"""
        print(f"\n🧠 层组行为分析:")
        print(f"   {'─'*50}")

        groups = ['low_layers', 'mid_layers', 'high_layers']
        group_names = {'low_layers': '低层(视觉处理)', 'mid_layers': '中层(特征整合)', 'high_layers': '高层(语言生成)'}

        for group in groups:
            if group in group_data:
                data = group_data[group]
                name = group_names[group]
                entropy = data.get('avg_entropy', 0)
                confidence = data.get('avg_confidence', 0)
                stability = data.get('prediction_stability', 0)

                print(f"   📊 {name}:")
                print(f"      • 平均熵: {entropy:.3f} (越低越确定)")
                print(f"      • 平均置信度: {confidence:.3f}")
                print(f"      • 预测稳定性: {stability:.3f}")

                # 解释
                if entropy < 1.0:
                    print(f"      💡 该层组预测非常确定")
                elif entropy > 3.0:
                    print(f"      💡 该层组预测较为分散")

        # 组间转换分析
        if 'transitions' in group_data:
            transitions = group_data['transitions']
            print(f"   🔄 组间转换模式:")
            for trans_name, trans_data in transitions.items():
                entropy_change = trans_data.get('entropy_change', 0)
                direction = "↗️" if entropy_change > 0 else "↘️"
                print(f"      {direction} {trans_name}: 熵变化 {entropy_change:+.3f}")

    def print_final_summary(self, results: Dict):
        """打印最终总结"""
        print(f"\n{'🎉'*20} 跨层分析完成 {'🎉'*20}")
        print(f"{'='*80}")

        gen_results = results['generation_results']
        cross_analysis = results['cross_layer_analysis']
        summary = results['summary']

        # 生成统计
        print(f"📊 生成统计:")
        print(f"   • 生成步骤: {gen_results['generation_steps']}")
        print(f"   • 最终文本: '{gen_results['final_generated_text']}'")
        print(f"   • 分析层数: {len(self.config.target_layers)}")

        # 关键发现
        print(f"\n🔍 关键发现:")
        for finding in summary['key_findings']:
            print(f"   • {finding}")

        # 层行为模式
        print(f"\n🧠 层行为模式:")
        for layer_group, pattern in summary['layer_behavior_patterns'].items():
            print(f"   • {layer_group}: {pattern}")

        # 建议
        print(f"\n💡 优化建议:")
        for recommendation in summary['recommendations']:
            print(f"   • {recommendation}")

        print(f"{'='*80}")


class InteractiveHTMLGenerator:
    """交互式HTML生成器"""

    def __init__(self, config: 'CrossLayerAnalysisConfig'):
        self.config = config

    def generate_interactive_html(self, results: Dict, output_path: str):
        """生成交互式HTML报告"""
        logger.info("🌐 生成交互式HTML报告...")

        html_content = self._create_html_template(results)

        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"✅ 交互式HTML报告已保存: {output_path}")

    def _create_html_template(self, results: Dict) -> str:
        """创建HTML模板"""
        gen_results = results['generation_results']
        cross_analysis = results['cross_layer_analysis']
        summary = results['summary']

        # 准备数据
        trajectory_data = self._prepare_trajectory_data(cross_analysis)
        similarity_data = self._prepare_similarity_data(cross_analysis)

        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma2 跨层分析报告</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/plotly.js-dist@2.26.0/plotly.min.js"></script>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
        }}
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }}
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
        }}
        .header p {{
            margin: 10px 0 0 0;
            opacity: 0.9;
        }}
        .nav-tabs {{
            display: flex;
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }}
        .nav-tab {{
            flex: 1;
            padding: 15px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            transition: all 0.3s;
        }}
        .nav-tab:hover {{
            background-color: #e9ecef;
        }}
        .nav-tab.active {{
            background-color: #007bff;
            color: white;
        }}
        .tab-content {{
            display: none;
            padding: 30px;
        }}
        .tab-content.active {{
            display: block;
        }}
        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }}
        .summary-card {{
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }}
        .summary-card h3 {{
            margin: 0 0 10px 0;
            color: #495057;
        }}
        .chart-container {{
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }}
        .step-selector {{
            margin: 20px 0;
            text-align: center;
        }}
        .step-selector select {{
            padding: 10px;
            font-size: 16px;
            border-radius: 5px;
            border: 1px solid #ccc;
        }}
        .findings-list {{
            list-style: none;
            padding: 0;
        }}
        .findings-list li {{
            padding: 10px;
            margin: 5px 0;
            background: #e3f2fd;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }}
        .layer-info {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }}
        .layer-card {{
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 PaliGemma2 跨层分析报告</h1>
            <p>深度分析模型层间信息传递和演化模式</p>
            <p>生成文本: "{gen_results['final_generated_text']}" | 分析步骤: {gen_results['generation_steps']} | 分析层数: {len(self.config.target_layers)}</p>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('overview')">📊 总览</button>
            <button class="nav-tab" onclick="showTab('trajectory')">🎯 预测轨迹</button>
            <button class="nav-tab" onclick="showTab('similarity')">🔗 层间相似度</button>
            <button class="nav-tab" onclick="showTab('layers')">🧠 层组分析</button>
            <button class="nav-tab" onclick="showTab('details')">📋 详细数据</button>
        </div>

        <div id="overview" class="tab-content active">
            <h2>📊 分析总览</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3>🎯 关键发现</h3>
                    <ul class="findings-list">
                        {self._format_findings(summary['key_findings'])}
                    </ul>
                </div>
                <div class="summary-card">
                    <h3>🧠 层行为模式</h3>
                    {self._format_layer_patterns(summary['layer_behavior_patterns'])}
                </div>
                <div class="summary-card">
                    <h3>💡 优化建议</h3>
                    <ul class="findings-list">
                        {self._format_recommendations(summary['recommendations'])}
                    </ul>
                </div>
            </div>
        </div>

        <div id="trajectory" class="tab-content">
            <h2>🎯 预测轨迹分析</h2>
            <div class="step-selector">
                <label for="stepSelect">选择生成步骤: </label>
                <select id="stepSelect" onchange="updateTrajectoryChart()">
                    {self._create_step_options(gen_results['generation_steps'])}
                </select>
            </div>
            <div class="chart-container">
                <canvas id="trajectoryChart"></canvas>
            </div>
        </div>

        <div id="similarity" class="tab-content">
            <h2>🔗 层间相似度分析</h2>
            <div class="chart-container">
                <div id="similarityHeatmap"></div>
            </div>
        </div>

        <div id="layers" class="tab-content">
            <h2>🧠 层组分析</h2>
            <div class="layer-info">
                {self._create_layer_group_cards(cross_analysis)}
            </div>
        </div>

        <div id="details" class="tab-content">
            <h2>📋 详细数据</h2>
            <div class="chart-container">
                <h3>原始分析数据</h3>
                <pre id="rawData" style="background: #f8f9fa; padding: 20px; border-radius: 5px; overflow-x: auto;">
{json.dumps(summary, indent=2, ensure_ascii=False)}
                </pre>
            </div>
        </div>
    </div>

    <script>
        // 数据
        const trajectoryData = {trajectory_data};
        const similarityData = {similarity_data};

        // 标签页切换
        function showTab(tabName) {{
            // 隐藏所有标签页
            document.querySelectorAll('.tab-content').forEach(tab => {{
                tab.classList.remove('active');
            }});
            document.querySelectorAll('.nav-tab').forEach(tab => {{
                tab.classList.remove('active');
            }});

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // 初始化图表
            if (tabName === 'trajectory') {{
                initTrajectoryChart();
            }} else if (tabName === 'similarity') {{
                initSimilarityHeatmap();
            }}
        }}

        // 预测轨迹图表
        let trajectoryChart = null;

        function initTrajectoryChart() {{
            if (trajectoryChart) return;

            const ctx = document.getElementById('trajectoryChart').getContext('2d');
            trajectoryChart = new Chart(ctx, {{
                type: 'line',
                data: {{
                    labels: [],
                    datasets: [{{
                        label: '目标Token概率',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }}]
                }},
                options: {{
                    responsive: true,
                    plugins: {{
                        title: {{
                            display: true,
                            text: '预测概率在各层的演化'
                        }}
                    }},
                    scales: {{
                        y: {{
                            beginAtZero: true,
                            max: 1,
                            title: {{
                                display: true,
                                text: '概率'
                            }}
                        }},
                        x: {{
                            title: {{
                                display: true,
                                text: '层索引'
                            }}
                        }}
                    }}
                }}
            }});

            updateTrajectoryChart();
        }}

        function updateTrajectoryChart() {{
            if (!trajectoryChart) return;

            const stepIndex = document.getElementById('stepSelect').value;
            const stepData = trajectoryData[stepIndex];

            if (stepData) {{
                const layers = Object.keys(stepData).map(Number).sort((a, b) => a - b);
                const probabilities = layers.map(layer => stepData[layer]);

                trajectoryChart.data.labels = layers;
                trajectoryChart.data.datasets[0].data = probabilities;
                trajectoryChart.update();
            }}
        }}

        // 相似度热力图
        function initSimilarityHeatmap() {{
            const data = [{{
                z: similarityData.matrix,
                x: similarityData.layers,
                y: similarityData.layers,
                type: 'heatmap',
                colorscale: 'Viridis'
            }}];

            const layout = {{
                title: '层间余弦相似度矩阵',
                xaxis: {{ title: '层索引' }},
                yaxis: {{ title: '层索引' }}
            }};

            Plotly.newPlot('similarityHeatmap', data, layout);
        }}

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {{
            // 默认显示总览
        }});
    </script>
</body>
</html>
        """

        return html

    def _prepare_trajectory_data(self, cross_analysis: Dict) -> str:
        """准备预测轨迹数据"""
        trajectory_data = {}

        if 'prediction_trajectories' in cross_analysis:
            for i, trajectory in enumerate(cross_analysis['prediction_trajectories']):
                if 'target_token_trajectory' in trajectory:
                    trajectory_data[i] = trajectory['target_token_trajectory']

        return json.dumps(trajectory_data)

    def _prepare_similarity_data(self, cross_analysis: Dict) -> str:
        """准备相似度数据"""
        similarity_data = {'matrix': [], 'layers': []}

        if 'layer_similarities' in cross_analysis and cross_analysis['layer_similarities']:
            first_similarity = cross_analysis['layer_similarities'][0]
            if 'similarity_matrix' in first_similarity:
                sim_matrix = first_similarity['similarity_matrix']

                # 提取层索引
                layers = sorted(set([int(k.split('->')[0]) for k in sim_matrix.keys()] +
                                  [int(k.split('->')[1]) for k in sim_matrix.keys()]))

                # 构建矩阵
                matrix = []
                for i, layer_i in enumerate(layers):
                    row = []
                    for j, layer_j in enumerate(layers):
                        if i == j:
                            row.append(1.0)
                        else:
                            key = f"{layer_i}->{layer_j}"
                            if key in sim_matrix:
                                row.append(sim_matrix[key]['cosine_similarity'])
                            else:
                                key = f"{layer_j}->{layer_i}"
                                if key in sim_matrix:
                                    row.append(sim_matrix[key]['cosine_similarity'])
                                else:
                                    row.append(0.0)
                    matrix.append(row)

                similarity_data = {'matrix': matrix, 'layers': layers}

        return json.dumps(similarity_data)

    def _format_findings(self, findings: List[str]) -> str:
        """格式化关键发现"""
        return '\n'.join([f'<li>{finding}</li>' for finding in findings])

    def _format_recommendations(self, recommendations: List[str]) -> str:
        """格式化建议"""
        return '\n'.join([f'<li>{rec}</li>' for rec in recommendations])

    def _format_layer_patterns(self, patterns: Dict) -> str:
        """格式化层行为模式"""
        html = ""
        for layer_group, pattern in patterns.items():
            html += f"<p><strong>{layer_group}:</strong> {pattern}</p>"
        return html

    def _create_step_options(self, num_steps: int) -> str:
        """创建步骤选择选项"""
        options = ""
        for i in range(num_steps):
            options += f'<option value="{i}">Step {i}</option>'
        return options

    def _create_layer_group_cards(self, cross_analysis: Dict) -> str:
        """创建层组卡片"""
        if not cross_analysis.get('layer_group_patterns'):
            return "<p>暂无层组分析数据</p>"

        first_pattern = cross_analysis['layer_group_patterns'][0]
        cards = ""

        group_names = {
            'low_layers': '低层 (视觉处理)',
            'mid_layers': '中层 (特征整合)',
            'high_layers': '高层 (语言生成)'
        }

        for group_key, group_name in group_names.items():
            if group_key in first_pattern:
                data = first_pattern[group_key]
                cards += f"""
                <div class="layer-card">
                    <h4>{group_name}</h4>
                    <p><strong>平均熵:</strong> {data.get('avg_entropy', 0):.3f}</p>
                    <p><strong>平均置信度:</strong> {data.get('avg_confidence', 0):.3f}</p>
                    <p><strong>预测稳定性:</strong> {data.get('prediction_stability', 0):.3f}</p>
                    <p><strong>层数:</strong> {data.get('layer_count', 0)}</p>
                </div>
                """

        return cards


@dataclass
class CrossLayerAnalysisConfig:
    """跨层分析配置"""
    # 继承原有配置
    model_path: str = "google/paligemma2-3b-pt-224"
    device: str = "cuda:0" if torch.cuda.is_available() else "cpu"
    dtype: str = "bf16"
    
    # 生成配置
    max_new_tokens: int = 15
    do_sample: bool = False
    temperature: float = 1.0
    
    # 跨层分析配置
    target_layers: List[int] = field(default_factory=lambda: list(range(27)))  # 分析所有层
    analyze_layer_pairs: bool = True           # 分析相邻层对
    analyze_layer_groups: bool = True          # 分析层组（低/中/高层）
    track_prediction_trajectory: bool = True   # 追踪预测轨迹
    measure_representation_distance: bool = True # 测量表示距离
    
    # 跨层度量配置
    similarity_metrics: List[str] = field(default_factory=lambda: ["cosine", "l2", "kl_divergence"])
    top_k_trajectory: int = 10  # 追踪top-k预测的轨迹
    
    # 位置分析配置
    analyze_all_positions: bool = False  # 简化：只分析最后位置
    vision_patch_sample_rate: float = 0.1  # vision patch采样率
    text_positions_limit: int = 15  # 最大分析的text位置数
    
    # 输出配置
    output_dir: str = "./cross_layer_analysis_results"
    save_detailed_analysis: bool = True
    
    # 可视化配置
    enable_visualization: bool = True
    create_flow_diagram: bool = True
    create_trajectory_heatmap: bool = True
    create_interactive_html: bool = True


class CrossLayerDataCollector:
    """跨层数据收集器 - 负责收集生成过程中的所有层状态"""
    
    def __init__(self, config: CrossLayerAnalysisConfig):
        self.config = config
        self.collected_data = {}
        
    def collect_generation_data(self, model, processor, image_path: str, prompt: str) -> Dict[str, Any]:
        """收集生成过程中的所有数据"""
        logger.info("🔄 开始收集跨层生成数据...")

        # 准备初始输入
        image = Image.open(image_path).convert('RGB')
        initial_inputs = processor(text=prompt, images=image, return_tensors="pt")

        # 移动到设备
        device = torch.device(self.config.device)
        for key, value in initial_inputs.items():
            if torch.is_tensor(value):
                initial_inputs[key] = value.to(device)

        generation_history = []
        generated_token_ids = []
        current_inputs = initial_inputs

        # 逐步生成并收集数据
        for step in range(self.config.max_new_tokens):
            logger.info(f"🔄 收集Step {step}数据...")

            with torch.no_grad():
                # 获取所有层的隐藏状态
                step_outputs = model(
                    **current_inputs,
                    output_hidden_states=True,
                    return_dict=True
                )

                # 提取隐藏状态
                if hasattr(step_outputs, 'language_model_outputs'):
                    hidden_states = step_outputs.language_model_outputs.hidden_states
                else:
                    hidden_states = step_outputs.hidden_states

                # 获取下一个token
                next_token_logits = step_outputs.logits[0, -1, :]
                next_token_id = next_token_logits.argmax().item()

                # 检查是否结束
                if next_token_id == processor.tokenizer.eos_token_id:
                    logger.info(f"🏁 遇到EOS token，生成结束于Step {step}")
                    break

                # 解码token
                try:
                    next_token_text = processor.tokenizer.decode([next_token_id])
                except:
                    next_token_text = f"<TOKEN_{next_token_id}>"

                # 🔧 修复：只存储target_layers指定的层隐藏状态
                target_layer_hidden_states = {}

                # 🔧 添加调试信息
                if step == 0:  # 只在第一步打印调试信息
                    logger.info(f"🔍 调试信息: 总共有 {len(hidden_states)} 层隐藏状态")
                    logger.info(f"🔍 目标层: {self.config.target_layers}")
                    if hidden_states:
                        logger.info(f"🔍 隐藏状态形状: {hidden_states[0].shape}")

                for layer_idx, layer_hidden in enumerate(hidden_states):
                    if layer_idx in self.config.target_layers:
                        # 🔧 确保隐藏状态的数据类型一致性
                        target_layer_hidden_states[layer_idx] = layer_hidden.clone().detach().float()

                # 记录步骤信息
                step_info = {
                    'step': step,
                    'sequence_length': current_inputs['input_ids'].shape[1],
                    'predicted_token_id': next_token_id,
                    'predicted_token_text': next_token_text,
                    'target_layer_hidden_states': target_layer_hidden_states,  # 🔧 修改字段名
                    'final_logits': next_token_logits.cpu()
                }

                generation_history.append(step_info)
                generated_token_ids.append(next_token_id)

                # 准备下一步输入
                new_token_tensor = torch.tensor([[next_token_id]], device=device)
                updated_input_ids = torch.cat([current_inputs['input_ids'], new_token_tensor], dim=1)
                new_attention = torch.ones((1, 1), device=device, dtype=current_inputs['attention_mask'].dtype)
                updated_attention_mask = torch.cat([current_inputs['attention_mask'], new_attention], dim=1)

                current_inputs = {
                    'input_ids': updated_input_ids,
                    'attention_mask': updated_attention_mask,
                    'pixel_values': current_inputs['pixel_values']
                }

        final_generated_text = processor.tokenizer.decode(generated_token_ids, skip_special_tokens=True)

        return {
            'initial_prompt': prompt,
            'final_generated_text': final_generated_text,
            'generation_steps': len(generation_history),
            'generation_history': generation_history,
            'initial_sequence_length': initial_inputs['input_ids'].shape[1]
        }


class PredictionTrajectoryAnalyzer:
    """预测轨迹分析器 - 算法1：预测轨迹追踪"""
    
    def __init__(self, config: CrossLayerAnalysisConfig):
        self.config = config
    
    def track_prediction_trajectory(self, step_hidden_states: Dict[int, torch.Tensor],
                                  norm_layer, lm_head) -> Dict:
        """
        追踪特定token预测概率在各层的演化轨迹

        算法步骤：
        1. 对每层的隐藏状态应用lm_head获取预测分布
        2. 追踪目标token（如最终预测的token）在各层的概率
        3. 识别概率突变点和关键层
        """

        trajectory_data = {
            'layer_probabilities': {},  # 每层的概率分布
            'target_token_trajectory': {},  # 目标token概率轨迹
            'top_k_trajectories': {},  # top-k token的轨迹
            'probability_jumps': [],  # 概率突变点
            'critical_layers': []  # 关键决策层
        }

        # 🔧 修复：获取最终预测的token作为追踪目标
        final_layer_idx = max(step_hidden_states.keys())
        final_hidden = step_hidden_states[final_layer_idx]
        final_logits = self.get_logits_from_hidden(final_hidden, norm_layer, lm_head)
        target_token_id = final_logits.argmax().item()

        # 🔧 新增：获取top-k token用于多轨迹追踪
        top_k_tokens = final_logits.topk(min(5, final_logits.size(0))).indices.tolist()

        prev_prob = 0
        layer_count = 0
        for layer_idx in sorted(step_hidden_states.keys()):
            # 获取该层预测
            hidden_state = step_hidden_states[layer_idx]
            logits = self.get_logits_from_hidden(hidden_state, norm_layer, lm_head)

            # 🔧 添加数值检查
            if torch.isnan(logits).any() or torch.isinf(logits).any():
                logger.warning(f"Layer {layer_idx}: 检测到异常logits值")
                continue

            probs = F.softmax(logits, dim=-1)

            # 🔧 添加概率检查
            if torch.isnan(probs).any() or torch.isinf(probs).any():
                logger.warning(f"Layer {layer_idx}: 检测到异常概率值")
                continue

            # 记录目标token概率
            target_prob = probs[target_token_id].item()

            # 🔧 添加调试信息（仅前几层）
            if layer_count < 3:
                logger.debug(f"Layer {layer_idx}: target_prob={target_prob:.6f}, max_prob={probs.max().item():.6f}")
            layer_count += 1
            trajectory_data['target_token_trajectory'][layer_idx] = target_prob

            # 🔧 新增：记录top-k token的概率
            if layer_idx not in trajectory_data['top_k_trajectories']:
                trajectory_data['top_k_trajectories'][layer_idx] = {}
            for token_id in top_k_tokens:
                trajectory_data['top_k_trajectories'][layer_idx][token_id] = probs[token_id].item()

            # 检测概率突变（超过阈值的变化）
            prob_change = target_prob - prev_prob
            if abs(prob_change) > 0.01:  # 🔧 进一步降低阈值到1%，更敏感地检测变化
                trajectory_data['probability_jumps'].append({
                    'layer': layer_idx,
                    'change': prob_change,
                    'from_prob': prev_prob,
                    'to_prob': target_prob
                })

            # 识别关键层（概率显著提升）
            if prob_change > 0.02:  # 🔧 进一步降低阈值到2%，捕获更多关键层
                trajectory_data['critical_layers'].append(layer_idx)

            prev_prob = target_prob

        return trajectory_data
    
    def get_logits_from_hidden(self, hidden_state: torch.Tensor, norm_layer, lm_head) -> torch.Tensor:
        """从隐藏状态获取logits"""
        # 取最后一个位置的隐藏状态
        last_hidden = hidden_state[0, -1, :]  # [hidden_size]

        # 🔧 修复：确保数据类型一致性，避免数值精度问题
        last_hidden = last_hidden.float()

        # 应用layer norm
        normalized_hidden = norm_layer(last_hidden.unsqueeze(0)).squeeze(0)

        # 🔧 修复：确保lm_head输入的数据类型正确
        normalized_hidden = normalized_hidden.to(lm_head.weight.dtype)

        # 计算logits
        logits = lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)

        # 🔧 修复：添加数值稳定性检查
        if torch.isnan(logits).any() or torch.isinf(logits).any():
            logger.warning(f"检测到NaN或Inf值在logits中，使用零向量替代")
            logits = torch.zeros_like(logits)

        return logits


class LayerSimilarityAnalyzer:
    """层间相似度分析器 - 算法2：层间表示相似度分析"""

    def __init__(self, config: CrossLayerAnalysisConfig):
        self.config = config

    def analyze_layer_similarity(self, hidden_states_dict: Dict[int, torch.Tensor],
                                norm_layer, lm_head) -> Dict:
        """
        分析层间的表示相似度

        算法步骤：
        1. 计算所有层对的余弦相似度（不仅仅是相邻层）
        2. 计算层间的信息增益（KL散度）
        3. 识别表示突变的层
        """

        similarity_matrix = {}
        information_gains = {}
        representation_shifts = []

        sorted_layers = sorted(hidden_states_dict.keys())

        # 🔧 修复：计算所有层对的相似度，不仅仅是相邻层
        for i, curr_layer in enumerate(sorted_layers):
            for j, other_layer in enumerate(sorted_layers):
                if i >= j:  # 避免重复计算，只计算上三角
                    continue

                curr_hidden = hidden_states_dict[curr_layer]
                other_hidden = hidden_states_dict[other_layer]

                # 1. 余弦相似度
                cosine_sim = F.cosine_similarity(
                    curr_hidden.flatten(),
                    other_hidden.flatten(),
                    dim=0
                ).item()

                # 2. L2距离
                l2_distance = torch.norm(curr_hidden - other_hidden).item()

                # 3. 预测分布的KL散度
                curr_probs = F.softmax(self._get_logits_from_hidden(curr_hidden, norm_layer, lm_head), dim=-1)
                other_probs = F.softmax(self._get_logits_from_hidden(other_hidden, norm_layer, lm_head), dim=-1)
                kl_div = F.kl_div(
                    torch.log(curr_probs + 1e-10),
                    other_probs,
                    reduction='sum'
                ).item()

                similarity_matrix[f"{curr_layer}->{other_layer}"] = {
                    'cosine_similarity': cosine_sim,
                    'l2_distance': l2_distance,
                    'kl_divergence': kl_div
                }

                # 信息增益：预测熵的变化
                curr_entropy = -torch.sum(curr_probs * torch.log(curr_probs + 1e-10)).item()
                other_entropy = -torch.sum(other_probs * torch.log(other_probs + 1e-10)).item()
                information_gains[f"{curr_layer}->{other_layer}"] = other_entropy - curr_entropy

                # 检测表示突变（只对相邻层检测）
                if abs(curr_layer - other_layer) == 1:  # 相邻层
                    if cosine_sim < 0.8 or kl_div > 1.0:  # 阈值可调
                        representation_shifts.append({
                            'from_layer': curr_layer,
                            'to_layer': other_layer,
                            'severity': 1 - cosine_sim
                        })

        return {
            'similarity_matrix': similarity_matrix,
            'information_gains': information_gains,
            'representation_shifts': representation_shifts
        }

    def _get_logits_from_hidden(self, hidden_state: torch.Tensor, norm_layer, lm_head) -> torch.Tensor:
        """从隐藏状态获取logits"""
        last_hidden = hidden_state[0, -1, :]  # [hidden_size]

        # 🔧 修复：确保数据类型一致性
        last_hidden = last_hidden.float()
        normalized_hidden = norm_layer(last_hidden.unsqueeze(0)).squeeze(0)
        normalized_hidden = normalized_hidden.to(lm_head.weight.dtype)
        logits = lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)

        # 数值稳定性检查
        if torch.isnan(logits).any() or torch.isinf(logits).any():
            logits = torch.zeros_like(logits)

        return logits


class ComponentFlowAnalyzer:
    """组件贡献流分析器 - 算法3：组件贡献流分析"""

    def __init__(self, config: CrossLayerAnalysisConfig):
        self.config = config

    def analyze_component_flow(self, component_states_by_layer: Dict[int, Dict],
                              norm_layer, lm_head) -> Dict:
        """
        分析Attention和MLP贡献在层间的变化模式

        算法步骤：
        1. 计算每层的Attention vs MLP贡献比例
        2. 识别组件主导性的转换点
        3. 分析组件协同模式
        """

        component_flow = {
            'attention_dominance_curve': [],
            'mlp_dominance_curve': [],
            'dominance_transitions': [],
            'synergy_patterns': []
        }

        prev_dominant = None

        for layer_idx in sorted(component_states_by_layer.keys()):
            layer_states = component_states_by_layer[layer_idx]

            # 获取组件预测置信度
            attn_conf = 0
            mlp_conf = 0

            if 'post_attention' in layer_states:
                attn_logits = self._get_logits_from_hidden(layer_states['post_attention'], norm_layer, lm_head)
                attn_probs = F.softmax(attn_logits, dim=-1)
                attn_conf = attn_probs.max().item()

            if 'post_mlp' in layer_states:
                mlp_logits = self._get_logits_from_hidden(layer_states['post_mlp'], norm_layer, lm_head)
                mlp_probs = F.softmax(mlp_logits, dim=-1)
                mlp_conf = mlp_probs.max().item()

            # 记录主导性
            component_flow['attention_dominance_curve'].append({
                'layer': layer_idx,
                'confidence': attn_conf
            })
            component_flow['mlp_dominance_curve'].append({
                'layer': layer_idx,
                'confidence': mlp_conf
            })

            # 检测主导性转换
            current_dominant = 'attention' if attn_conf > mlp_conf else 'mlp'
            if prev_dominant and current_dominant != prev_dominant:
                component_flow['dominance_transitions'].append({
                    'layer': layer_idx,
                    'from': prev_dominant,
                    'to': current_dominant,
                    'confidence_diff': abs(attn_conf - mlp_conf)
                })

            # 分析协同模式
            if abs(attn_conf - mlp_conf) < 0.05:  # 平衡状态
                component_flow['synergy_patterns'].append({
                    'layer': layer_idx,
                    'type': 'balanced',
                    'attn_conf': attn_conf,
                    'mlp_conf': mlp_conf
                })

            prev_dominant = current_dominant

        return component_flow

    def _get_logits_from_hidden(self, hidden_state: torch.Tensor, norm_layer, lm_head) -> torch.Tensor:
        """从隐藏状态获取logits"""
        last_hidden = hidden_state[0, -1, :]  # [hidden_size]

        # 🔧 修复：确保数据类型一致性
        last_hidden = last_hidden.float()
        normalized_hidden = norm_layer(last_hidden.unsqueeze(0)).squeeze(0)
        normalized_hidden = normalized_hidden.to(lm_head.weight.dtype)
        logits = lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)

        # 数值稳定性检查
        if torch.isnan(logits).any() or torch.isinf(logits).any():
            logits = torch.zeros_like(logits)

        return logits


class LayerGroupAnalyzer:
    """层组模式分析器 - 算法4：层组模式识别"""

    def __init__(self, config: CrossLayerAnalysisConfig):
        self.config = config

    def analyze_layer_groups(self, all_hidden_states: Dict[int, torch.Tensor],
                           norm_layer, lm_head) -> Dict:
        """
        将模型层分组并分析不同组的行为模式

        算法步骤：
        1. 将层分为低层、中层、高层（基于实际的层索引）
        2. 分析每组的特征
        3. 识别组间转换模式
        """

        # 🔧 修复：基于实际的层索引进行分组，而不是基于数组长度
        actual_layer_indices = sorted(all_hidden_states.keys())
        num_actual_layers = len(actual_layer_indices)

        if num_actual_layers == 0:
            return {'low_layers': {}, 'mid_layers': {}, 'high_layers': {}, 'transitions': {}}

        # 🔧 修复：基于实际层索引的值进行分组
        max_layer_idx = max(actual_layer_indices)
        min_layer_idx = min(actual_layer_indices)

        # 将层按实际索引值分为三组
        low_threshold = min_layer_idx + (max_layer_idx - min_layer_idx) // 3
        high_threshold = min_layer_idx + 2 * (max_layer_idx - min_layer_idx) // 3

        low_layers = [idx for idx in actual_layer_indices if idx <= low_threshold]
        mid_layers = [idx for idx in actual_layer_indices if low_threshold < idx <= high_threshold]
        high_layers = [idx for idx in actual_layer_indices if idx > high_threshold]

        # 确保每组至少有一层
        if not low_layers and actual_layer_indices:
            low_layers = [actual_layer_indices[0]]
        if not mid_layers and len(actual_layer_indices) > 2:
            mid_layers = actual_layer_indices[1:2] if len(actual_layer_indices) > 1 else []
        if not high_layers and actual_layer_indices:
            high_layers = [actual_layer_indices[-1]]

        group_analysis = {
            'low_layers': self._analyze_group(all_hidden_states, low_layers, 'low', norm_layer, lm_head),
            'mid_layers': self._analyze_group(all_hidden_states, mid_layers, 'mid', norm_layer, lm_head),
            'high_layers': self._analyze_group(all_hidden_states, high_layers, 'high', norm_layer, lm_head),
            'transitions': {}
        }

        # 分析组间转换
        for transition in [('low', 'mid'), ('mid', 'high')]:
            from_group, to_group = transition
            from_analysis = group_analysis[f'{from_group}_layers']
            to_analysis = group_analysis[f'{to_group}_layers']

            # 🔧 修复：检查分析结果是否有效
            if (from_analysis and to_analysis and
                'avg_entropy' in from_analysis and 'avg_entropy' in to_analysis):
                group_analysis['transitions'][f'{from_group}_to_{to_group}'] = {
                    'entropy_change': to_analysis['avg_entropy'] - from_analysis['avg_entropy'],
                    'confidence_change': to_analysis['avg_confidence'] - from_analysis['avg_confidence'],
                    'prediction_stability_change': to_analysis['prediction_stability'] - from_analysis['prediction_stability']
                }

        return group_analysis

    def _analyze_group(self, hidden_states: Dict, layer_indices: List[int], group_name: str,
                      norm_layer, lm_head) -> Dict:
        """分析层组的特征"""

        entropies = []
        confidences = []
        top_predictions = []

        for layer_idx in layer_indices:
            if layer_idx not in hidden_states:
                continue

            hidden = hidden_states[layer_idx]
            logits = self._get_logits_from_hidden(hidden, norm_layer, lm_head)
            probs = F.softmax(logits, dim=-1)

            # 计算熵
            entropy = -torch.sum(probs * torch.log(probs + 1e-10)).item()
            entropies.append(entropy)

            # 计算置信度
            confidence = probs.max().item()
            confidences.append(confidence)

            # 记录top预测
            top_token = logits.argmax().item()
            top_predictions.append(top_token)

        # 计算预测稳定性（相邻层预测相同的比例）
        stability = sum(1 for i in range(len(top_predictions)-1)
                       if top_predictions[i] == top_predictions[i+1]) / max(len(top_predictions)-1, 1)

        return {
            'group_name': group_name,
            'avg_entropy': np.mean(entropies) if entropies else 0,
            'avg_confidence': np.mean(confidences) if confidences else 0,
            'prediction_stability': stability,
            'entropy_variance': np.var(entropies) if entropies else 0,
            'layer_count': len(layer_indices)
        }

    def _get_logits_from_hidden(self, hidden_state: torch.Tensor, norm_layer, lm_head) -> torch.Tensor:
        """从隐藏状态获取logits"""
        last_hidden = hidden_state[0, -1, :]  # [hidden_size]

        # 🔧 修复：确保数据类型一致性
        last_hidden = last_hidden.float()
        normalized_hidden = norm_layer(last_hidden.unsqueeze(0)).squeeze(0)
        normalized_hidden = normalized_hidden.to(lm_head.weight.dtype)
        logits = lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)

        # 数值稳定性检查
        if torch.isnan(logits).any() or torch.isinf(logits).any():
            logits = torch.zeros_like(logits)

        return logits


class CrossLayerVisualizationManager:
    """跨层可视化管理器"""

    def __init__(self, config: CrossLayerAnalysisConfig):
        self.config = config
        self.output_dir = config.output_dir

    def create_prediction_trajectory_heatmap(self, trajectories: List[Dict], output_path: str):
        """创建预测轨迹热力图"""
        logger.info("🎨 创建预测轨迹热力图...")

        if not trajectories:
            logger.warning("没有轨迹数据，跳过热力图生成")
            return

        # 构建热力图矩阵
        num_steps = len(trajectories)
        num_layers = len(self.config.target_layers)

        heatmap_matrix = np.zeros((num_steps, num_layers))

        for step_idx, trajectory in enumerate(trajectories):
            for layer_idx_pos, layer_idx in enumerate(self.config.target_layers):
                if layer_idx in trajectory['target_token_trajectory']:
                    prob = trajectory['target_token_trajectory'][layer_idx]
                    heatmap_matrix[step_idx, layer_idx_pos] = prob

        # 创建热力图
        plt.figure(figsize=(12, 8))
        sns.heatmap(heatmap_matrix,
                   xticklabels=self.config.target_layers,
                   yticklabels=[f"Step {i}" for i in range(num_steps)],
                   cmap='viridis',
                   cbar_kws={'label': 'Target Token Probability'})

        plt.title('Prediction Trajectory Across Layers')
        plt.xlabel('Layer Index')
        plt.ylabel('Generation Step')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"✅ 预测轨迹热力图已保存: {output_path}")

    def create_layer_similarity_heatmap(self, similarity_data: Dict, output_path: str):
        """创建层间相似度热力图"""
        logger.info("🎨 创建层间相似度热力图...")

        if not similarity_data.get('similarity_matrix'):
            logger.warning("没有相似度数据，跳过热力图生成")
            return

        # 提取余弦相似度数据
        similarity_matrix = similarity_data['similarity_matrix']
        layers = sorted(set([int(k.split('->')[0]) for k in similarity_matrix.keys()] +
                           [int(k.split('->')[1]) for k in similarity_matrix.keys()]))

        # 🔧 修复：构建完整的相似度矩阵
        sim_matrix = np.eye(len(layers))  # 对角线为1

        for key, values in similarity_matrix.items():
            from_layer, to_layer = map(int, key.split('->'))
            from_idx = layers.index(from_layer)
            to_idx = layers.index(to_layer)
            sim_value = values['cosine_similarity']
            sim_matrix[from_idx, to_idx] = sim_value
            sim_matrix[to_idx, from_idx] = sim_value  # 对称

        # 🔧 改进：使用更好的颜色映射和范围
        plt.figure(figsize=(12, 10))
        sns.heatmap(sim_matrix,
                   xticklabels=layers,
                   yticklabels=layers,
                   cmap='viridis',  # 更好的颜色映射
                   vmin=0, vmax=1,  # 固定范围
                   annot=False,  # 不显示数值（太密集）
                   cbar_kws={'label': 'Cosine Similarity'})

        plt.title('Layer-to-Layer Representation Similarity Matrix')
        plt.xlabel('Layer Index')
        plt.ylabel('Layer Index')
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        logger.info(f"✅ 层间相似度热力图已保存: {output_path}")


class CrossLayerLogitLensAnalyzer:
    """跨层Logit Lens分析器 - 主分析类"""

    def __init__(self, config: CrossLayerAnalysisConfig):
        self.config = config
        self.device = torch.device(config.device)

        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)

        # 设置随机种子
        set_seed(1234)

        # 初始化组件
        self.data_collector = CrossLayerDataCollector(config)
        self.trajectory_analyzer = PredictionTrajectoryAnalyzer(config)
        self.similarity_analyzer = LayerSimilarityAnalyzer(config)
        self.component_analyzer = ComponentFlowAnalyzer(config)
        self.group_analyzer = LayerGroupAnalyzer(config)
        self.visualization_manager = CrossLayerVisualizationManager(config)
        self.output_manager = DetailedOutputManager(config)
        self.html_generator = InteractiveHTMLGenerator(config)

        # 初始化模型和处理器
        self._load_model()

        logger.info("跨层Logit Lens分析器初始化完成")

    def _load_model(self):
        """加载PaliGemma2模型和处理器"""
        logger.info("正在加载PaliGemma2模型...")

        try:
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.config.model_path)

            # 加载模型
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)

            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device
            )

            # 设置为评估模式
            self.model.eval()

            # 获取模型架构信息
            self.num_layers = len(self.model.language_model.model.layers)
            self.hidden_size = self.model.language_model.config.hidden_size
            self.vocab_size = self.model.language_model.config.vocab_size

            # 获取关键组件
            self.norm_layer = self.model.language_model.model.norm
            self.lm_head = self.model.language_model.lm_head

            logger.info(f"模型加载成功:")
            logger.info(f"  - 语言模型层数: {self.num_layers}")
            logger.info(f"  - 隐藏维度: {self.hidden_size}")
            logger.info(f"  - 词汇表大小: {self.vocab_size}")

        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise

    def analyze_cross_layer_generation(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """
        主分析方法 - 执行完整的跨层分析

        伪代码流程：
        1. 初始化数据收集结构
        2. 逐步生成，每步进行跨层分析
        3. 综合所有步骤的跨层模式
        4. 生成可视化和报告
        """
        logger.info("🎯 开始跨层Logit Lens分析...")
        logger.info(f"📸 图像: {image_path}")
        logger.info(f"💬 提示: {prompt}")

        # Phase 1: 数据收集
        logger.info("📊 Phase 1: 数据收集...")
        generation_results = self.data_collector.collect_generation_data(
            self.model, self.processor, image_path, prompt
        )

        # Phase 2: 跨层分析
        logger.info("🔍 Phase 2: 跨层分析...")
        cross_layer_analysis = {
            'prediction_trajectories': [],  # 每步的预测轨迹
            'layer_similarities': [],       # 每步的层间相似度
            'component_flows': [],          # 每步的组件流
            'layer_group_patterns': [],     # 每步的层组模式
            'global_patterns': {}           # 全局模式
        }

        for step_idx, step_data in enumerate(generation_results['generation_history']):
            logger.info(f"  🔍 分析Step {step_idx}...")

            # 2.1 预测轨迹分析
            trajectory = self.trajectory_analyzer.track_prediction_trajectory(
                step_data['target_layer_hidden_states'],  # 🔧 修改字段名
                self.norm_layer,
                self.lm_head
            )
            cross_layer_analysis['prediction_trajectories'].append(trajectory)

            # 2.2 层间相似度分析
            similarity = self.similarity_analyzer.analyze_layer_similarity(
                step_data['target_layer_hidden_states'],  # 🔧 修改字段名
                self.norm_layer,
                self.lm_head
            )
            cross_layer_analysis['layer_similarities'].append(similarity)

            # 2.3 层组模式分析
            group_pattern = self.group_analyzer.analyze_layer_groups(
                step_data['target_layer_hidden_states'],  # 🔧 修改字段名
                self.norm_layer,
                self.lm_head
            )
            cross_layer_analysis['layer_group_patterns'].append(group_pattern)

            # 🆕 详细输出每个step的分析结果
            self.output_manager.print_step_analysis(
                step_idx, step_data, trajectory, similarity, group_pattern, self.processor
            )

        # Phase 3: 全局模式识别
        logger.info("🌐 Phase 3: 全局模式识别...")
        cross_layer_analysis['global_patterns'] = self.identify_global_patterns(
            cross_layer_analysis
        )

        # Phase 4: 生成可视化
        logger.info("🎨 Phase 4: 生成可视化...")
        self._generate_visualizations(cross_layer_analysis)

        # Phase 5: 生成报告
        logger.info("📝 Phase 5: 生成报告...")
        final_results = {
            'generation_results': generation_results,
            'cross_layer_analysis': cross_layer_analysis,
            'summary': self.generate_analysis_summary(cross_layer_analysis),
            'config': self.config
        }

        # Phase 6: 生成交互式HTML
        if self.config.create_interactive_html:
            logger.info("🌐 Phase 6: 生成交互式HTML...")
            html_path = os.path.join(self.config.output_dir, "interactive_analysis_report.html")
            self.html_generator.generate_interactive_html(final_results, html_path)

        # 保存结果
        self._save_results(final_results)

        # 🆕 打印最终总结
        self.output_manager.print_final_summary(final_results)

        logger.info("🎉 跨层分析完成!")
        return final_results

    def identify_global_patterns(self, cross_layer_analysis: Dict) -> Dict:
        """识别全局跨层模式"""
        logger.info("🔍 识别全局跨层模式...")

        global_patterns = {
            'critical_layers_across_steps': [],
            'consistent_similarity_patterns': {},
            'prediction_convergence_layers': [],
            'layer_group_transitions': {}
        }

        # 1. 识别跨步骤的关键层
        all_critical_layers = []
        for trajectory in cross_layer_analysis['prediction_trajectories']:
            all_critical_layers.extend(trajectory['critical_layers'])

        # 统计关键层出现频率
        from collections import Counter
        critical_layer_counts = Counter(all_critical_layers)
        global_patterns['critical_layers_across_steps'] = [
            {'layer': layer, 'frequency': count}
            for layer, count in critical_layer_counts.most_common(5)
        ]

        # 2. 识别一致的相似度模式
        if cross_layer_analysis['layer_similarities']:
            # 计算平均相似度
            avg_similarities = {}
            for similarity_data in cross_layer_analysis['layer_similarities']:
                for layer_pair, metrics in similarity_data['similarity_matrix'].items():
                    if layer_pair not in avg_similarities:
                        avg_similarities[layer_pair] = []
                    avg_similarities[layer_pair].append(metrics['cosine_similarity'])

            # 找到最稳定的层对
            stable_pairs = []
            for layer_pair, similarities in avg_similarities.items():
                if len(similarities) > 1:
                    variance = np.var(similarities)
                    mean_sim = np.mean(similarities)
                    if variance < 0.01 and mean_sim > 0.8:  # 低方差，高相似度
                        stable_pairs.append({
                            'layer_pair': layer_pair,
                            'mean_similarity': mean_sim,
                            'variance': variance
                        })

            global_patterns['consistent_similarity_patterns'] = stable_pairs

        # 3. 识别预测收敛层
        convergence_layers = []
        for trajectory in cross_layer_analysis['prediction_trajectories']:
            # 找到概率首次超过阈值的层
            for layer_idx, prob in trajectory['target_token_trajectory'].items():
                if prob > 0.5:  # 50%置信度阈值
                    convergence_layers.append(layer_idx)
                    break

        if convergence_layers:
            avg_convergence_layer = np.mean(convergence_layers)
            global_patterns['prediction_convergence_layers'] = {
                'average_convergence_layer': avg_convergence_layer,
                'convergence_variance': np.var(convergence_layers),
                'all_convergence_layers': convergence_layers
            }

        return global_patterns

    def generate_analysis_summary(self, cross_layer_analysis: Dict) -> Dict:
        """生成分析摘要"""
        logger.info("📝 生成分析摘要...")

        summary = {
            'key_findings': [],
            'critical_layers': [],
            'layer_behavior_patterns': {},
            'recommendations': []
        }

        # 关键发现
        global_patterns = cross_layer_analysis.get('global_patterns', {})

        if global_patterns.get('critical_layers_across_steps'):
            most_critical = global_patterns['critical_layers_across_steps'][0]
            summary['key_findings'].append(
                f"Layer {most_critical['layer']} is consistently critical across {most_critical['frequency']} generation steps"
            )

        if global_patterns.get('prediction_convergence_layers'):
            avg_convergence = global_patterns['prediction_convergence_layers']['average_convergence_layer']
            summary['key_findings'].append(
                f"Predictions typically converge around layer {avg_convergence:.1f}"
            )

        # 层行为模式
        if cross_layer_analysis.get('layer_group_patterns'):
            first_group_pattern = cross_layer_analysis['layer_group_patterns'][0]
            summary['layer_behavior_patterns'] = {
                'low_layers': f"Avg entropy: {first_group_pattern['low_layers']['avg_entropy']:.3f}",
                'mid_layers': f"Avg entropy: {first_group_pattern['mid_layers']['avg_entropy']:.3f}",
                'high_layers': f"Avg entropy: {first_group_pattern['high_layers']['avg_entropy']:.3f}"
            }

        # 建议
        summary['recommendations'] = [
            "Focus analysis on identified critical layers for efficiency",
            "Monitor prediction convergence patterns for early stopping",
            "Consider layer group behaviors for model optimization"
        ]

        return summary

    def _generate_visualizations(self, cross_layer_analysis: Dict):
        """生成可视化"""
        logger.info("🎨 生成可视化...")

        if not self.config.enable_visualization:
            logger.info("可视化已禁用，跳过")
            return

        # 1. 预测轨迹热力图
        if cross_layer_analysis['prediction_trajectories']:
            trajectory_path = os.path.join(self.config.output_dir, "prediction_trajectory_heatmap.png")
            self.visualization_manager.create_prediction_trajectory_heatmap(
                cross_layer_analysis['prediction_trajectories'],
                trajectory_path
            )

        # 2. 层间相似度热力图
        if cross_layer_analysis['layer_similarities']:
            # 使用第一步的相似度数据作为示例
            similarity_path = os.path.join(self.config.output_dir, "layer_similarity_heatmap.png")
            self.visualization_manager.create_layer_similarity_heatmap(
                cross_layer_analysis['layer_similarities'][0],
                similarity_path
            )

    def _save_results(self, results: Dict):
        """保存分析结果"""
        logger.info("💾 保存分析结果...")

        # 保存完整结果
        results_path = os.path.join(self.config.output_dir, "cross_layer_analysis_results.json")

        # 转换不可序列化的对象
        serializable_results = self._make_serializable(results)

        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        logger.info(f"✅ 结果已保存: {results_path}")

        # 保存摘要
        summary_path = os.path.join(self.config.output_dir, "analysis_summary.json")
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(results['summary'], f, indent=2, ensure_ascii=False)

        logger.info(f"✅ 摘要已保存: {summary_path}")

    def _make_serializable(self, obj):
        """将对象转换为可序列化格式"""
        if isinstance(obj, torch.Tensor):
            # 处理BFloat16等特殊数据类型
            try:
                return obj.cpu().float().numpy().tolist()
            except:
                return obj.cpu().numpy().astype(np.float32).tolist()
        elif isinstance(obj, np.ndarray):
            # 确保numpy数组是可序列化的类型
            try:
                return obj.astype(np.float32).tolist()
            except:
                return obj.tolist()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        elif isinstance(obj, (np.float32, np.float64, np.int32, np.int64)):
            return float(obj) if 'float' in str(type(obj)) else int(obj)
        else:
            return obj


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description="PaliGemma2 跨层Logit Lens分析")

    # 基本参数
    parser.add_argument("--image_path", type=str, default='/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images/real_image_20250610_170727.png', help="输入图像路径")
    parser.add_argument("--prompt", type=str, default='describe the image', help="输入提示文本")
    parser.add_argument("--output_dir", type=str, default="./cross_layer_analysis_results", help="输出目录")

    # 模型参数
    parser.add_argument("--model_path", type=str, default="google/paligemma2-3b-pt-224", help="模型路径")
    parser.add_argument("--device", type=str, default="cuda:0", help="设备")
    parser.add_argument("--dtype", type=str, default="bf16", choices=["bf16", "fp16", "fp32"], help="数据类型")

    # 分析参数
    parser.add_argument("--max_new_tokens", type=int, default=15, help="最大生成token数")
    parser.add_argument("--target_layers", type=str, default="0,1,2,3,5,10,15,20,24,25,26", help="目标层（逗号分隔）")
    parser.add_argument("--top_k_trajectory", type=int, default=10, help="追踪top-k预测")

    # 功能开关
    parser.add_argument("--disable_visualization", action="store_true", help="禁用可视化")
    parser.add_argument("--analyze_all_layers", action="store_true", help="分析所有层")

    args = parser.parse_args()

    # 解析目标层
    if args.analyze_all_layers:
        target_layers = list(range(27))  # PaliGemma2-3B有27层
    else:
        target_layers = [int(x.strip()) for x in args.target_layers.split(',')]

    # 创建配置
    config = CrossLayerAnalysisConfig(
        model_path=args.model_path,
        device=args.device,
        dtype=args.dtype,
        max_new_tokens=args.max_new_tokens,
        target_layers=target_layers,
        top_k_trajectory=args.top_k_trajectory,
        output_dir=args.output_dir,
        enable_visualization=not args.disable_visualization
    )

    # 创建分析器
    analyzer = CrossLayerLogitLensAnalyzer(config)

    # 执行分析
    try:
        results = analyzer.analyze_cross_layer_generation(args.image_path, args.prompt)

        # 打印摘要
        print("\n" + "="*60)
        print("🎉 跨层分析完成!")
        print("="*60)

        summary = results['summary']
        print("\n📊 关键发现:")
        for finding in summary['key_findings']:
            print(f"  • {finding}")

        print("\n🧠 层行为模式:")
        for layer_group, pattern in summary['layer_behavior_patterns'].items():
            print(f"  • {layer_group}: {pattern}")

        print("\n💡 建议:")
        for recommendation in summary['recommendations']:
            print(f"  • {recommendation}")

        print(f"\n📁 详细结果已保存到: {args.output_dir}")
        print("="*60)

    except Exception as e:
        logger.error(f"分析失败: {e}")
        raise


if __name__ == "__main__":
    main()