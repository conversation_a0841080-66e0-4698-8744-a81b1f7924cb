#!/usr/bin/env python3
"""
测试层分组逻辑的修复
"""

def test_layer_grouping():
    """测试层分组逻辑"""
    
    # 模拟target_layers
    target_layers = [0, 5, 10, 15, 20, 25]
    all_hidden_states = {i: f"layer_{i}_data" for i in target_layers}
    
    print("🔍 测试层分组逻辑修复")
    print("="*50)
    
    # 原始错误的分组逻辑
    print("❌ 原始错误逻辑:")
    num_layers = len(all_hidden_states)  # 6
    old_low = list(range(0, num_layers // 3))  # [0, 1]
    old_mid = list(range(num_layers // 3, 2 * num_layers // 3))  # [2, 3]  
    old_high = list(range(2 * num_layers // 3, num_layers))  # [4, 5]
    
    print(f"  数组长度: {num_layers}")
    print(f"  低层组索引: {old_low} -> 实际层: {[i for i in old_low if i < len(target_layers)]}")
    print(f"  中层组索引: {old_mid} -> 实际层: {[target_layers[i] for i in old_mid if i < len(target_layers)]}")
    print(f"  高层组索引: {old_high} -> 实际层: {[target_layers[i] for i in old_high if i < len(target_layers)]}")
    
    # 修复后的分组逻辑
    print("\n✅ 修复后的逻辑:")
    actual_layer_indices = sorted(all_hidden_states.keys())
    max_layer_idx = max(actual_layer_indices)
    min_layer_idx = min(actual_layer_indices)
    
    low_threshold = min_layer_idx + (max_layer_idx - min_layer_idx) // 3
    high_threshold = min_layer_idx + 2 * (max_layer_idx - min_layer_idx) // 3
    
    new_low = [idx for idx in actual_layer_indices if idx <= low_threshold]
    new_mid = [idx for idx in actual_layer_indices if low_threshold < idx <= high_threshold]
    new_high = [idx for idx in actual_layer_indices if idx > high_threshold]
    
    print(f"  实际层索引: {actual_layer_indices}")
    print(f"  最小层: {min_layer_idx}, 最大层: {max_layer_idx}")
    print(f"  低层阈值: {low_threshold}, 高层阈值: {high_threshold}")
    print(f"  低层组: {new_low}")
    print(f"  中层组: {new_mid}")
    print(f"  高层组: {new_high}")
    
    print("\n🎯 分组结果对比:")
    print(f"  低层: {old_low} -> {new_low}")
    print(f"  中层: {old_mid} -> {new_mid}")
    print(f"  高层: {old_high} -> {new_high}")
    
    # 验证分组合理性
    print("\n✅ 验证:")
    print(f"  每组都有层: 低层{len(new_low)>0}, 中层{len(new_mid)>0}, 高层{len(new_high)>0}")
    print(f"  覆盖所有层: {len(new_low + new_mid + new_high) == len(target_layers)}")
    print(f"  无重叠: {len(set(new_low + new_mid + new_high)) == len(target_layers)}")

if __name__ == "__main__":
    test_layer_grouping()
