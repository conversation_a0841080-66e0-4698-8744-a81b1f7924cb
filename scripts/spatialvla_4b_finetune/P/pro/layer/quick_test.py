#!/usr/bin/env python3
"""
快速测试修复后的层组分析
"""

import sys
import os
sys.path.append('.')

from cross_layer_logit_lens_analyzer import CrossLayerLogitLensAnalyzer, CrossLayerAnalysisConfig

def quick_test():
    """快速测试"""
    print("🚀 快速测试修复后的层组分析")
    print("="*50)
    
    # 创建配置
    config = CrossLayerAnalysisConfig(
        model_path="google/paligemma2-3b-pt-224",
        target_layers=[0, 5, 10, 15, 20, 25],
        max_new_tokens=2,  # 很短的生成
        output_dir="./quick_test_results"
    )
    
    try:
        # 创建分析器
        print("📝 创建分析器...")
        analyzer = CrossLayerLogitLensAnalyzer(config)
        
        # 执行分析
        print("🔍 执行分析...")
        image_path = "../../../P2_Dataset/quick_demo_results/demo_20250615_190302/visual_debug/frame_0034_fixed_mapping_detection.png"
        
        if not os.path.exists(image_path):
            print(f"❌ 图像文件不存在: {image_path}")
            return
            
        results = analyzer.analyze_cross_layer_generation(image_path, "What is this?")
        
        # 检查层组分析结果
        print("\n🧠 层组分析结果:")
        if results['cross_layer_analysis']['layer_group_patterns']:
            first_pattern = results['cross_layer_analysis']['layer_group_patterns'][0]
            
            for group_name in ['low_layers', 'mid_layers', 'high_layers']:
                if group_name in first_pattern:
                    group_data = first_pattern[group_name]
                    print(f"  {group_name}:")
                    print(f"    平均熵: {group_data.get('avg_entropy', 'N/A'):.3f}")
                    print(f"    平均置信度: {group_data.get('avg_confidence', 'N/A'):.3f}")
                    print(f"    预测稳定性: {group_data.get('prediction_stability', 'N/A'):.3f}")
                    print(f"    层数: {group_data.get('layer_count', 'N/A')}")
        
        print("\n✅ 测试完成!")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    quick_test()
