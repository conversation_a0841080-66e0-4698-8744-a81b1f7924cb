#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强跨层分析系统测试脚本
====================

测试修复了残差连接和层归一化问题的增强版跨层分析器

作者: Assistant
日期: 2025-01-14
"""

import os
import sys
import logging
from PIL import Image

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cross_layer_logit_lens_analyzer import CrossLayerAnalysisConfig, CrossLayerLogitLensAnalyzer

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_image(image_path: str):
    """创建一个测试图像"""
    if not os.path.exists(image_path):
        # 创建一个简单的测试图像
        test_image = Image.new('RGB', (224, 224), color='blue')
        test_image.save(image_path)
        logger.info(f"创建测试图像: {image_path}")
    return image_path


def test_enhanced_functionality():
    """测试增强功能"""
    logger.info("🧪 开始增强功能测试...")
    
    # 创建增强配置
    config = CrossLayerAnalysisConfig(
        model_path="google/paligemma2-3b-pt-224",
        device="cuda:0" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu",
        dtype="bf16",
        max_new_tokens=3,  # 减少token数量以加快测试
        target_layers=[0, 5, 10, 15, 20, 25],  # 测试关键层
        output_dir="./test_enhanced_results",
        enable_visualization=True,
        create_interactive_html=True  # 🆕 启用HTML生成
    )
    
    # 创建测试图像
    test_image_path = "test_enhanced_image.jpg"
    create_test_image(test_image_path)
    
    # 测试提示
    test_prompt = "What color is this?"
    
    try:
        # 创建分析器
        logger.info("初始化增强分析器...")
        analyzer = CrossLayerLogitLensAnalyzer(config)
        
        # 执行分析
        logger.info("执行增强跨层分析...")
        results = analyzer.analyze_cross_layer_generation(test_image_path, test_prompt)
        
        # 验证增强功能
        assert 'generation_results' in results
        assert 'cross_layer_analysis' in results
        assert 'summary' in results
        
        # 🆕 验证增强的分析数据
        cross_analysis = results['cross_layer_analysis']
        
        # 检查attention轨迹
        if 'attention_trajectories' in cross_analysis:
            logger.info("✅ Attention轨迹分析功能正常")
        
        # 检查残差分析
        if 'residual_analyses' in cross_analysis:
            logger.info("✅ 残差连接分析功能正常")
        
        # 检查增强的相似度分析
        if cross_analysis['layer_similarities']:
            first_similarity = cross_analysis['layer_similarities'][0]
            if 'norm_effects' in first_similarity:
                logger.info("✅ Norm层效应分析功能正常")
            if 'residual_effects' in first_similarity:
                logger.info("✅ 残差效应分析功能正常")
        
        # 检查增强的层组分析
        if cross_analysis['layer_group_patterns']:
            first_group = cross_analysis['layer_group_patterns'][0]
            if 'avg_norm_effect' in first_group['low_layers']:
                logger.info("✅ 增强层组分析功能正常")
        
        logger.info("✅ 增强功能测试通过!")
        
        # 打印增强摘要
        print("\n" + "="*60)
        print("🎉 增强测试完成!")
        print("="*60)
        
        summary = results['summary']
        print("\n📊 关键发现:")
        for finding in summary['key_findings']:
            print(f"  • {finding}")
        
        print(f"\n📁 结果保存在: {config.output_dir}")
        
        # 🆕 检查HTML文件是否生成
        html_path = os.path.join(config.output_dir, "interactive_analysis_report.html")
        if os.path.exists(html_path):
            print(f"🌐 交互式HTML报告: {html_path}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 增强测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)


def test_norm_layer_effects():
    """测试norm层效应分析"""
    logger.info("🧪 测试norm层效应分析...")
    
    config = CrossLayerAnalysisConfig(
        max_new_tokens=2,
        target_layers=[0, 13, 26],  # 首、中、末层
        output_dir="./test_norm_effects",
        enable_visualization=False
    )
    
    test_image_path = "test_norm_image.jpg"
    create_test_image(test_image_path)
    
    try:
        analyzer = CrossLayerLogitLensAnalyzer(config)
        results = analyzer.analyze_cross_layer_generation(test_image_path, "Describe this")
        
        # 检查norm层效应数据
        cross_analysis = results['cross_layer_analysis']
        if cross_analysis['layer_similarities']:
            similarity_data = cross_analysis['layer_similarities'][0]
            if 'norm_effects' in similarity_data:
                norm_effects = similarity_data['norm_effects']
                logger.info(f"✅ 检测到 {len(norm_effects)} 个层对的norm效应")
                
                # 打印一些norm效应数据
                for layer_pair, effect_data in list(norm_effects.items())[:2]:
                    logger.info(f"   {layer_pair}: norm变化 = {effect_data['norm_change']:.4f}")
        
        logger.info("✅ Norm层效应测试通过!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Norm层效应测试失败: {e}")
        return False
    
    finally:
        if os.path.exists(test_image_path):
            os.remove(test_image_path)


def test_residual_analysis():
    """测试残差连接分析"""
    logger.info("🧪 测试残差连接分析...")
    
    config = CrossLayerAnalysisConfig(
        max_new_tokens=2,
        target_layers=list(range(0, 27, 5)),  # 每5层取一个
        output_dir="./test_residual_analysis",
        enable_visualization=False
    )
    
    test_image_path = "test_residual_image.jpg"
    create_test_image(test_image_path)
    
    try:
        analyzer = CrossLayerLogitLensAnalyzer(config)
        results = analyzer.analyze_cross_layer_generation(test_image_path, "What is this?")
        
        # 检查残差分析数据
        generation_history = results['generation_results']['generation_history']
        if generation_history:
            first_step = generation_history[0]
            if 'residual_analysis' in first_step:
                residual_data = first_step['residual_analysis']
                logger.info(f"✅ 检测到残差分析数据: {list(residual_data.keys())}")
                
                # 打印一些残差数据
                if 'layer_to_layer_changes' in residual_data:
                    changes = residual_data['layer_to_layer_changes']
                    logger.info(f"   层间变化数量: {len(changes)}")
        
        logger.info("✅ 残差连接分析测试通过!")
        return True
        
    except Exception as e:
        logger.error(f"❌ 残差连接分析测试失败: {e}")
        return False
    
    finally:
        if os.path.exists(test_image_path):
            os.remove(test_image_path)


def main():
    """主测试函数"""
    print("🧪 增强跨层分析系统测试")
    print("=" * 60)
    
    test_results = []
    
    # 运行测试
    tests = [
        ("增强功能测试", test_enhanced_functionality),
        ("Norm层效应测试", test_norm_layer_effects),
        ("残差连接分析测试", test_residual_analysis),
    ]
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n🧪 运行 {test_name}...")
            result = test_func()
            if result is None:
                result = True
            test_results.append((test_name, result))
            logger.info(f"✅ {test_name} 完成")
        except Exception as e:
            logger.error(f"❌ {test_name} 失败: {e}")
            test_results.append((test_name, False))
    
    # 打印测试结果
    print("\n" + "=" * 60)
    print("🧪 增强测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有增强测试通过!")
        print("\n💡 改进要点:")
        print("  ✅ 修复了残差连接的累积效应分析")
        print("  ✅ 修复了层归一化的分布偏移问题")
        print("  ✅ 使用官方的output_attentions提取attention权重")
        print("  ✅ 增加了详尽的终端输出")
        print("  ✅ 生成交互式HTML报告")
        return 0
    else:
        print("⚠️ 部分增强测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
