#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
跨层分析系统测试脚本
==================

测试跨层Logit Lens分析器的基本功能

作者: Assistant
日期: 2025-01-14
"""

import os
import sys
import logging
from PIL import Image
import numpy as np

# 添加路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from cross_layer_logit_lens_analyzer import CrossLayerAnalysisConfig, CrossLayerLogitLensAnalyzer

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_image(image_path: str):
    """创建一个测试图像"""
    if not os.path.exists(image_path):
        # 创建一个简单的测试图像
        test_image = Image.new('RGB', (224, 224), color='red')
        test_image.save(image_path)
        logger.info(f"创建测试图像: {image_path}")
    return image_path


def test_basic_functionality():
    """测试基本功能"""
    logger.info("🧪 开始基本功能测试...")
    
    # 创建测试配置
    config = CrossLayerAnalysisConfig(
        model_path="google/paligemma2-3b-pt-224",
        device="cuda:0" if os.system("nvidia-smi > /dev/null 2>&1") == 0 else "cpu",
        dtype="bf16",
        max_new_tokens=5,  # 减少token数量以加快测试
        target_layers=[0, 5, 10, 15, 20, 25],  # 测试关键层
        output_dir="./test_cross_layer_results",
        enable_visualization=True
    )
    
    # 创建测试图像
    test_image_path = "test_image.jpg"
    create_test_image(test_image_path)
    
    # 测试提示
    test_prompt = "Describe this image"
    
    try:
        # 创建分析器
        logger.info("初始化分析器...")
        analyzer = CrossLayerLogitLensAnalyzer(config)
        
        # 执行分析
        logger.info("执行跨层分析...")
        results = analyzer.analyze_cross_layer_generation(test_image_path, test_prompt)
        
        # 验证结果
        assert 'generation_results' in results
        assert 'cross_layer_analysis' in results
        assert 'summary' in results
        
        logger.info("✅ 基本功能测试通过!")
        
        # 打印摘要
        print("\n" + "="*50)
        print("🎉 测试完成!")
        print("="*50)
        
        summary = results['summary']
        print("\n📊 关键发现:")
        for finding in summary['key_findings']:
            print(f"  • {finding}")
        
        print(f"\n📁 结果保存在: {config.output_dir}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        return False
    
    finally:
        # 清理测试文件
        if os.path.exists(test_image_path):
            os.remove(test_image_path)


def test_configuration_options():
    """测试配置选项"""
    logger.info("🧪 测试配置选项...")
    
    # 测试不同配置
    configs = [
        {
            'name': '最小配置',
            'config': CrossLayerAnalysisConfig(
                max_new_tokens=3,
                target_layers=[0, 13, 26],
                enable_visualization=False
            )
        },
        {
            'name': '完整配置',
            'config': CrossLayerAnalysisConfig(
                max_new_tokens=5,
                target_layers=list(range(0, 27, 3)),  # 每3层取一个
                analyze_layer_pairs=True,
                track_prediction_trajectory=True,
                enable_visualization=True
            )
        }
    ]
    
    for test_case in configs:
        logger.info(f"测试 {test_case['name']}...")
        
        # 验证配置有效性
        config = test_case['config']
        assert config.max_new_tokens > 0
        assert len(config.target_layers) > 0
        assert all(0 <= layer < 27 for layer in config.target_layers)
        
        logger.info(f"✅ {test_case['name']} 配置有效")
    
    logger.info("✅ 配置选项测试通过!")


def test_data_structures():
    """测试数据结构"""
    logger.info("🧪 测试数据结构...")
    
    # 测试配置数据类
    config = CrossLayerAnalysisConfig()
    
    # 验证默认值
    assert config.model_path == "google/paligemma2-3b-pt-224"
    assert config.max_new_tokens == 15
    assert len(config.target_layers) == 27  # 所有层
    assert config.top_k_trajectory == 10
    
    # 测试自定义值
    custom_config = CrossLayerAnalysisConfig(
        max_new_tokens=20,
        target_layers=[1, 2, 3],
        top_k_trajectory=5
    )
    
    assert custom_config.max_new_tokens == 20
    assert custom_config.target_layers == [1, 2, 3]
    assert custom_config.top_k_trajectory == 5
    
    logger.info("✅ 数据结构测试通过!")


def main():
    """主测试函数"""
    print("🧪 跨层分析系统测试")
    print("=" * 50)
    
    test_results = []
    
    # 运行测试
    tests = [
        ("数据结构测试", test_data_structures),
        ("配置选项测试", test_configuration_options),
        ("基本功能测试", test_basic_functionality),
    ]
    
    for test_name, test_func in tests:
        try:
            logger.info(f"\n🧪 运行 {test_name}...")
            result = test_func()
            # 如果函数没有返回值，默认为True（成功）
            if result is None:
                result = True
            test_results.append((test_name, result))
            logger.info(f"✅ {test_name} 完成")
        except Exception as e:
            logger.error(f"❌ {test_name} 失败: {e}")
            test_results.append((test_name, False))
    
    # 打印测试结果
    print("\n" + "=" * 50)
    print("🧪 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        return 0
    else:
        print("⚠️ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
