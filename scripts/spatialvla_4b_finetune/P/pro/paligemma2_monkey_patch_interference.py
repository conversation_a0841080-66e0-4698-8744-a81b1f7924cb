#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 MonkeyPatch Interference Analysis - 侵入式组件干扰分析
================================================================

🐒 【创新版】基于MonkeyPatch的侵入式模型干扰分析系统

### 🎯 核心特性
- **完全控制数据流**：可以修改、替换或跳过任意组件
- **实时干扰实验**：动态开关Attention/MLP/Residual组件
- **组件消融分析**：精确测量各组件对生成质量的影响
- **灵活的干预**：在任意位置注入控制逻辑

### 🚨 关键原则
1. **不修改原始模型参数** - 只在运行时替换方法
2. **完全可逆** - 可以随时恢复到原始状态
3. **精确控制** - 可以控制每层每个组件的行为
4. **丰富的实验模式** - 支持多种干扰和消融实验

### 🔬 实验类型
1. **组件消融实验** - 关闭特定组件看影响
2. **逐层重要性分析** - 测量每层的重要性
3. **动态控制实验** - 根据生成内容调整组件
4. **组件交互分析** - 研究组件间的相互作用
5. **渐进式降级** - 模拟模型能力的逐步丧失

### 🧮 数学原理
基于真实的Gemma2DecoderLayer架构：
```
# 正常流程
residual = hidden_states
hidden_states = input_layernorm(hidden_states)
attn_out, _, _ = self_attn(hidden_states, ...)
attn_out = post_attention_layernorm(attn_out)
hidden_states = residual + attn_out  # <- 可控制

residual = hidden_states  
hidden_states = pre_feedforward_layernorm(hidden_states)
mlp_out = mlp(hidden_states)
mlp_out = post_feedforward_layernorm(mlp_out)
hidden_states = residual + mlp_out  # <- 可控制
```

### 🎛️ 控制变量
- **组件开关**：attention_enabled, mlp_enabled, residual_enabled
- **缩放因子**：attention_scale, mlp_scale  
- **噪声注入**：noise_level, noise_type
- **干预模式**：normal, ablate, isolate, modify, progressive

作者: Assistant
日期: 2025-01-14
版本: MonkeyPatch侵入式干扰分析版
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn.functional as F
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any, Callable, Union
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
from PIL import Image
from tqdm import tqdm
import argparse
import logging
from collections import defaultdict
import pickle
import copy
import random
import cv2
import time
import warnings
import base64

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入transformers
try:
    from transformers import (
        AutoProcessor, AutoModelForImageTextToText, 
        PaliGemmaForConditionalGeneration,
        set_seed
    )
    logger.info("成功导入Transformers组件。")
except ImportError as e:
    logger.error(f"导入Transformers失败: {e}")
    sys.exit(1)

warnings.filterwarnings("ignore")


@dataclass
class MonkeyPatchConfig:
    """MonkeyPatch干扰分析配置"""
    
    # 模型配置
    model_path: str = "google/paligemma2-3b-pt-224"
    device: str = "cuda:0" if torch.cuda.is_available() else "cpu"
    dtype: str = "bf16"
    
    # 生成配置
    max_new_tokens: int = 15
    do_sample: bool = False
    temperature: float = 1.0
    
    # 干扰配置
    target_layers: List[int] = field(default_factory=lambda: [0, 5, 10, 15, 20, 25, 26])
    
    # 组件控制配置
    attention_enabled: List[bool] = field(default_factory=lambda: [True] * 27)
    mlp_enabled: List[bool] = field(default_factory=lambda: [True] * 27)
    residual_enabled: List[bool] = field(default_factory=lambda: [True] * 27)
    
    # 缩放因子
    attention_scale: float = 1.0
    mlp_scale: float = 1.0
    
    # 噪声配置
    noise_level: float = 0.0
    noise_type: str = "gaussian"  # gaussian, uniform, salt_pepper
    
    # 干预模式
    intervention_mode: str = "normal"  # normal, ablate, isolate, modify, progressive
    
    # 分析配置
    top_k_tokens: int = 5
    record_intermediate_states: bool = True
    
    # 实验配置
    experiment_types: List[str] = field(default_factory=lambda: ["baseline", "attention_only", "mlp_only"])
    
    # 输出配置
    output_dir: str = "./paligemma2_monkey_patch_results"
    save_all_states: bool = True
    
    # 可视化配置
    enable_visualization: bool = True
    create_comparison_plots: bool = True
    create_interactive_html: bool = True


@dataclass
class InterferenceState:
    """干扰状态记录"""
    step: int
    layer_idx: int
    component_type: str  # 'attention', 'mlp', 'residual'
    original_tensor: torch.Tensor
    modified_tensor: torch.Tensor
    intervention_applied: str
    metadata: Dict[str, Any] = field(default_factory=dict)


class MonkeyPatchManager:
    """
    🐒 MonkeyPatch管理器 - 侵入式组件控制核心
    
    负责：
    1. 动态替换DecoderLayer的forward方法
    2. 实现精确的组件控制（Attention/MLP/Residual）
    3. 记录所有干扰状态和中间结果
    4. 提供安全的恢复机制
    """
    
    def __init__(self, config: MonkeyPatchConfig):
        self.config = config
        self.original_methods = {}  # 存储原始方法
        self.component_states = {}  # 存储组件状态
        self.interference_log = []  # 干扰日志
        self.current_step = 0
        self.is_patched = False
        
        # 控制标志
        self.control_flags = {
            'use_attention': config.attention_enabled.copy(),
            'use_mlp': config.mlp_enabled.copy(),
            'use_residual': config.residual_enabled.copy(),
            'record_states': config.record_intermediate_states,
            'attention_scale': config.attention_scale,
            'mlp_scale': config.mlp_scale,
            'noise_level': config.noise_level,
            'intervention_mode': config.intervention_mode
        }
        
        logger.info("🐒 MonkeyPatch管理器初始化完成")
    
    def apply_patches(self, model: PaliGemmaForConditionalGeneration):
        """
        🎯 应用MonkeyPatch到模型
        
        核心操作：
        1. 保存原始方法
        2. 替换每个目标层的forward方法
        3. 设置控制逻辑
        """
        logger.info("🔧 开始应用MonkeyPatch...")
        
        if self.is_patched:
            logger.warning("⚠️ 模型已经被patch，先恢复原始状态")
            self.restore_original_methods()
        
        # 获取目标层
        target_layers = self.config.target_layers
        language_model_layers = model.language_model.model.layers
        
        for layer_idx in target_layers:
            if layer_idx < len(language_model_layers):
                layer = language_model_layers[layer_idx]
                
                # 保存原始forward方法
                original_forward = layer.forward
                self.original_methods[layer_idx] = original_forward
                
                # 创建新的patched forward方法
                patched_forward = self._create_patched_forward(layer, layer_idx, original_forward)
                
                # 替换方法
                layer.forward = patched_forward
                
                logger.info(f"✅ 已patch层 {layer_idx}")
        
        self.is_patched = True
        logger.info(f"🎉 MonkeyPatch应用完成，共patch了 {len(target_layers)} 层")
    
    def _create_patched_forward(self, layer, layer_idx: int, original_forward: Callable):
        """
        🏗️ 创建patched的forward方法 - 核心逻辑
        
        实现完全的组件控制：
        1. Attention Block控制
        2. MLP Block控制  
        3. Residual Connection控制
        4. 中间状态记录
        5. 干扰效果记录
        """
        
        def patched_forward(
            hidden_states: torch.Tensor,
            attention_mask: Optional[torch.Tensor] = None,
            position_ids: Optional[torch.LongTensor] = None,
            past_key_value: Optional[Tuple[torch.Tensor]] = None,
            output_attentions: Optional[bool] = False,
            use_cache: Optional[bool] = False,
            cache_position: Optional[torch.LongTensor] = None,
            **kwargs,
        ) -> Tuple[torch.FloatTensor, ...]:
            """
            🎛️ 完全可控的forward方法
            
            基于Gemma2DecoderLayer的真实架构，实现精确控制
            """
            
            # 🔍 输入状态记录
            original_input = hidden_states.clone().detach() if self.control_flags['record_states'] else None
            
            # 🎯 === ATTENTION BLOCK 控制 ===
            if self.control_flags['use_attention'][layer_idx]:
                # 正常执行Attention
                attention_output = self._execute_attention_block(
                    layer, hidden_states, attention_mask, position_ids, 
                    past_key_value, output_attentions, use_cache, cache_position, 
                    layer_idx, **kwargs
                )
                post_attention_state = attention_output['hidden_states']
                attention_weights = attention_output.get('attention_weights', None)
                present_key_value = attention_output.get('present_key_value', past_key_value)
            else:
                # 🚫 跳过Attention：直接传递输入
                logger.debug(f"🚫 跳过层 {layer_idx} 的Attention")
                post_attention_state = hidden_states
                attention_weights = None
                present_key_value = past_key_value
                
                # 记录干扰
                self._record_interference(
                    layer_idx, 'attention', hidden_states, post_attention_state, 'skipped'
                )
            
            # 🔍 记录Post-Attention状态
            if self.control_flags['record_states']:
                self._record_component_state(layer_idx, 'post_attention', post_attention_state)
            
            # 🎯 === MLP BLOCK 控制 ===
            if self.control_flags['use_mlp'][layer_idx]:
                # 正常执行MLP
                mlp_output = self._execute_mlp_block(
                    layer, post_attention_state, layer_idx
                )
                final_hidden_states = mlp_output['hidden_states']
            else:
                # 🚫 跳过MLP：直接传递post_attention_state
                logger.debug(f"🚫 跳过层 {layer_idx} 的MLP")
                final_hidden_states = post_attention_state
                
                # 记录干扰
                self._record_interference(
                    layer_idx, 'mlp', post_attention_state, final_hidden_states, 'skipped'
                )
            
            # 🔍 记录最终状态
            if self.control_flags['record_states']:
                self._record_component_state(layer_idx, 'final_output', final_hidden_states)
                
                # 记录输入输出对比
                if original_input is not None:
                    self._record_component_state(layer_idx, 'input_output_comparison', {
                        'input': original_input,
                        'output': final_hidden_states,
                        'change_magnitude': torch.norm(final_hidden_states - original_input).item()
                    })
            
            # 🎯 构建输出格式（兼容原始forward）
            outputs = (final_hidden_states,)
            
            if output_attentions:
                outputs += (attention_weights,)
            
            if use_cache:
                outputs += (present_key_value,)
            
            return outputs
        
        return patched_forward
    
    def _execute_attention_block(self, layer, hidden_states: torch.Tensor, 
                                attention_mask, position_ids, past_key_value, 
                                output_attentions, use_cache, cache_position, 
                                layer_idx: int, **kwargs) -> Dict[str, Any]:
        """
        🧠 执行Attention Block - 完全遵循Gemma2架构
        
        Gemma2的Attention架构：
        1. input_layernorm (Pre-norm)
        2. self_attn 
        3. post_attention_layernorm (Post-norm)
        4. residual connection
        """
        
        # 1. 保存residual
        residual = hidden_states
        
        # 2. Pre-norm
        hidden_states = layer.input_layernorm(hidden_states)
        
        # 3. Self-attention
        attn_outputs = layer.self_attn(
            hidden_states,
            attention_mask=attention_mask,
            position_ids=position_ids,
            past_key_value=past_key_value,
            output_attentions=output_attentions,
            use_cache=use_cache,
            cache_position=cache_position,
            **kwargs
        )
        
        # 解析attention输出
        attention_output = attn_outputs[0]
        attention_weights = attn_outputs[1] if output_attentions else None
        present_key_value = attn_outputs[2] if use_cache else None
        
        # 4. Post-norm
        attention_output = layer.post_attention_layernorm(attention_output)
        
        # 🎛️ 应用attention缩放
        if self.control_flags['attention_scale'] != 1.0:
            attention_output = attention_output * self.control_flags['attention_scale']
            logger.debug(f"🎛️ 层 {layer_idx} Attention缩放: {self.control_flags['attention_scale']}")
        
        # 🎛️ 添加噪声
        if self.control_flags['noise_level'] > 0:
            noise = self._generate_noise(attention_output.shape, self.control_flags['noise_level'])
            attention_output = attention_output + noise
            logger.debug(f"🎛️ 层 {layer_idx} Attention添加噪声: {self.control_flags['noise_level']}")
        
        # 5. Residual connection控制
        if self.control_flags['use_residual'][layer_idx]:
            hidden_states = residual + attention_output
        else:
            # 🚫 跳过残差连接
            hidden_states = attention_output
            logger.debug(f"🚫 跳过层 {layer_idx} Attention的残差连接")
            
            # 记录干扰
            self._record_interference(
                layer_idx, 'attention_residual', residual + attention_output, 
                hidden_states, 'residual_skipped'
            )
        
        return {
            'hidden_states': hidden_states,
            'attention_weights': attention_weights,
            'present_key_value': present_key_value,
            'raw_attention_output': attention_output,
            'residual': residual
        }
    
    def _execute_mlp_block(self, layer, hidden_states: torch.Tensor, layer_idx: int) -> Dict[str, Any]:
        """
        🧮 执行MLP Block - 完全遵循Gemma2架构
        
        Gemma2的MLP架构：
        1. pre_feedforward_layernorm (Pre-norm)
        2. mlp
        3. post_feedforward_layernorm (Post-norm)  
        4. residual connection
        """
        
        # 1. 保存residual
        residual = hidden_states
        
        # 2. Pre-norm
        hidden_states = layer.pre_feedforward_layernorm(hidden_states)
        
        # 3. MLP
        mlp_output = layer.mlp(hidden_states)
        
        # 4. Post-norm
        mlp_output = layer.post_feedforward_layernorm(mlp_output)
        
        # 🎛️ 应用MLP缩放
        if self.control_flags['mlp_scale'] != 1.0:
            mlp_output = mlp_output * self.control_flags['mlp_scale']
            logger.debug(f"🎛️ 层 {layer_idx} MLP缩放: {self.control_flags['mlp_scale']}")
        
        # 🎛️ 添加噪声
        if self.control_flags['noise_level'] > 0:
            noise = self._generate_noise(mlp_output.shape, self.control_flags['noise_level'])
            mlp_output = mlp_output + noise
            logger.debug(f"🎛️ 层 {layer_idx} MLP添加噪声: {self.control_flags['noise_level']}")
        
        # 5. Residual connection控制
        if self.control_flags['use_residual'][layer_idx]:
            hidden_states = residual + mlp_output
        else:
            # 🚫 跳过残差连接
            hidden_states = mlp_output
            logger.debug(f"🚫 跳过层 {layer_idx} MLP的残差连接")
            
            # 记录干扰
            self._record_interference(
                layer_idx, 'mlp_residual', residual + mlp_output, 
                hidden_states, 'residual_skipped'
            )
        
        return {
            'hidden_states': hidden_states,
            'raw_mlp_output': mlp_output,
            'residual': residual
        }
    
    def _generate_noise(self, shape: torch.Size, noise_level: float) -> torch.Tensor:
        """生成指定类型的噪声"""
        device = next(iter(self.original_methods.values())).__self__.input_layernorm.weight.device
        
        if self.config.noise_type == "gaussian":
            return torch.randn(shape, device=device) * noise_level
        elif self.config.noise_type == "uniform":
            return (torch.rand(shape, device=device) - 0.5) * 2 * noise_level
        elif self.config.noise_type == "salt_pepper":
            noise = torch.zeros(shape, device=device)
            mask = torch.rand(shape, device=device) < noise_level
            noise[mask] = torch.randn(mask.sum(), device=device)
            return noise
        else:
            return torch.zeros(shape, device=device)
    
    def _record_interference(self, layer_idx: int, component_type: str, 
                           original: torch.Tensor, modified: torch.Tensor, 
                           intervention: str):
        """记录干扰效果"""
        if not self.control_flags['record_states']:
            return
            
        interference_state = InterferenceState(
            step=self.current_step,
            layer_idx=layer_idx,
            component_type=component_type,
            original_tensor=original.clone().detach(),
            modified_tensor=modified.clone().detach(),
            intervention_applied=intervention,
            metadata={
                'magnitude_change': torch.norm(modified - original).item(),
                'mean_change': (modified - original).mean().item(),
                'std_change': (modified - original).std().item()
            }
        )
        
        self.interference_log.append(interference_state)
    
    def _record_component_state(self, layer_idx: int, state_type: str, tensor_data: Any):
        """记录组件状态"""
        if not self.control_flags['record_states']:
            return
            
        step_key = f"step_{self.current_step}"
        layer_key = f"layer_{layer_idx}"
        
        if step_key not in self.component_states:
            self.component_states[step_key] = {}
        if layer_key not in self.component_states[step_key]:
            self.component_states[step_key][layer_key] = {}
        
        if isinstance(tensor_data, torch.Tensor):
            self.component_states[step_key][layer_key][state_type] = tensor_data.clone().detach()
        else:
            self.component_states[step_key][layer_key][state_type] = tensor_data
    
    def update_control_flags(self, new_flags: Dict[str, Any]):
        """动态更新控制标志"""
        self.control_flags.update(new_flags)
        logger.info(f"🎛️ 更新控制标志: {new_flags}")
    
    def set_step(self, step: int):
        """设置当前步骤"""
        self.current_step = step
    
    def restore_original_methods(self):
        """恢复原始方法"""
        if not self.is_patched:
            logger.warning("⚠️ 模型未被patch，无需恢复")
            return
            
        logger.info("🔄 开始恢复原始方法...")
        
        # 这里需要获取model实例，但由于方法设计问题，暂时无法直接获取
        # 在实际使用中，应该在apply_patches时保存model引用
        logger.warning("⚠️ 恢复原始方法需要在具体使用时实现")
        
        self.is_patched = False
        logger.info("✅ 原始方法恢复完成")
    
    def get_component_states(self, step: Optional[int] = None) -> Dict[str, Any]:
        """获取组件状态"""
        if step is not None:
            return self.component_states.get(f"step_{step}", {})
        else:
            return self.component_states
    
    def get_interference_log(self) -> List[InterferenceState]:
        """获取干扰日志"""
        return self.interference_log
    
    def clear_states(self):
        """清空状态记录"""
        self.component_states.clear()
        self.interference_log.clear()
        self.current_step = 0
        logger.info("🧹 状态记录已清空")


class InvasiveLogitLensAnalyzer:
    """
    🔬 侵入式LogitLens分析器 - 基于MonkeyPatch的精确组件分析
    
    功能：
    1. 在干扰状态下进行logit lens分析
    2. 测量组件关闭对预测能力的影响
    3. 分析生成质量的变化
    4. 提供详细的组件贡献分析
    """
    
    def __init__(self, config: MonkeyPatchConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        
        # 设置随机种子
        set_seed(1234)
        
        # 初始化MonkeyPatch管理器
        self.patch_manager = MonkeyPatchManager(config)
        
        # 初始化模型和处理器
        self._load_model()
        
        # 结果存储
        self.experiment_results = {}
        
        logger.info("🔬 侵入式LogitLens分析器初始化完成")
    
    def _load_model(self):
        """加载PaliGemma2模型和处理器"""
        logger.info("正在加载PaliGemma2模型...")
        
        try:
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.config.model_path)
            
            # 加载模型
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)
            
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device
            )
            
            # 设置为评估模式
            self.model.eval()
            
            # 获取模型架构信息
            self.num_layers = len(self.model.language_model.model.layers)
            self.hidden_size = self.model.language_model.config.hidden_size
            self.vocab_size = self.model.language_model.config.vocab_size
            
            # 获取关键组件
            self.norm_layer = self.model.language_model.model.norm
            self.lm_head = self.model.language_model.lm_head
            
            logger.info(f"模型加载成功:")
            logger.info(f"  - 语言模型层数: {self.num_layers}")
            logger.info(f"  - 隐藏维度: {self.hidden_size}")
            logger.info(f"  - 词汇表大小: {self.vocab_size}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def analyze_interference_predictions(self, hidden_state: torch.Tensor, 
                                       layer_idx: int, component_type: str, 
                                       step: int) -> Dict[str, Any]:
        """
        分析在干扰状态下的预测能力
        
        与正常logit lens不同，这里分析的是被干扰后的状态
        """
        # 只分析最后位置（预测下一个token的位置）
        last_position_hidden = hidden_state[0, -1, :] if hidden_state.dim() == 3 else hidden_state[-1, :]
        
        # 应用norm层和lm_head
        with torch.no_grad():
            normalized_hidden = self.norm_layer(last_position_hidden.unsqueeze(0)).squeeze(0)
            logits = self.lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)
            probs = F.softmax(logits, dim=-1)
        
        # 获取top-k预测
        top_probs, top_indices = torch.topk(probs, self.config.top_k_tokens)
        
        predictions = []
        for i in range(self.config.top_k_tokens):
            token_id = top_indices[i].item()
            prob = top_probs[i].item()
            
            try:
                token_text = self.processor.tokenizer.decode([token_id])
                clean_token = token_text.replace('\n', '\\n').replace('\r', '\\r').strip()[:20]
            except:
                clean_token = f"<TOKEN_{token_id}>"
            
            predictions.append({
                'rank': i + 1,
                'token_id': token_id,
                'token_text': clean_token,
                'probability': prob,
                'logit_value': logits[token_id].item()
            })
        
        # 计算分布特征
        entropy = -torch.sum(probs * torch.log(probs + 1e-10)).item()
        perplexity = torch.exp(-torch.sum(probs * torch.log(probs + 1e-10))).item()
        
        return {
            'step': step,
            'layer_idx': layer_idx,
            'component_type': component_type,
            'predictions': predictions,
            'entropy': entropy,
            'perplexity': perplexity,
            'top1_confidence': predictions[0]['probability'],
            'hidden_stats': {
                'mean': last_position_hidden.mean().item(),
                'std': last_position_hidden.std().item(),
                'norm': last_position_hidden.norm().item(),
                'max': last_position_hidden.max().item(),
                'min': last_position_hidden.min().item()
            }
        }
    
    def run_interference_experiment(self, image_path: str, prompt: str, 
                                  experiment_type: str) -> Dict[str, Any]:
        """
        🧪 运行干扰实验 - 核心方法
        
        支持的实验类型：
        - baseline: 正常生成（无干扰）
        - attention_only: 只使用Attention
        - mlp_only: 只使用MLP  
        - no_residual: 关闭残差连接
        - layer_ablation: 逐层消融
        - progressive_degradation: 渐进式降级
        - noise_injection: 噪声注入
        """
        logger.info(f"🧪 开始干扰实验: {experiment_type}")
        logger.info(f"📸 图像: {image_path}")
        logger.info(f"💬 提示: {prompt}")
        
        # 设置实验配置
        self._setup_experiment(experiment_type)
        
        # 应用MonkeyPatch
        self.patch_manager.apply_patches(self.model)
        
        try:
            # 准备输入
            image = Image.open(image_path).convert('RGB')
            initial_inputs = self.processor(text=prompt, images=image, return_tensors="pt")
            
            # 移动到设备
            for key, value in initial_inputs.items():
                if torch.is_tensor(value):
                    initial_inputs[key] = value.to(self.device)
            
            # 执行干扰下的自回归生成
            experiment_results = self._run_controlled_generation(
                initial_inputs, experiment_type, image_path, prompt
            )
            
            # 分析结果
            analysis_results = self._analyze_experiment_results(experiment_results, experiment_type)
            
            return {
                'experiment_type': experiment_type,
                'generation_results': experiment_results,
                'analysis': analysis_results,
                'config': {
                    'image_path': image_path,
                    'prompt': prompt,
                    'model_path': self.config.model_path,
                    'target_layers': self.config.target_layers
                }
            }
            
        finally:
            # 恢复原始方法
            self._restore_model_safely()
    
    def _setup_experiment(self, experiment_type: str):
        """设置不同类型的实验"""
        logger.info(f"🎛️ 设置实验: {experiment_type}")
        
        if experiment_type == "baseline":
            # 基线：正常生成，不做任何干扰
            pass
            
        elif experiment_type == "attention_only":
            # 只使用Attention，关闭所有MLP
            self.patch_manager.update_control_flags({
                'use_mlp': [False] * self.num_layers
            })
            logger.info("🚫 已关闭所有MLP组件")
            
        elif experiment_type == "mlp_only":
            # 只使用MLP，关闭所有Attention
            self.patch_manager.update_control_flags({
                'use_attention': [False] * self.num_layers
            })
            logger.info("🚫 已关闭所有Attention组件")
            
        elif experiment_type == "no_residual":
            # 关闭所有残差连接
            self.patch_manager.update_control_flags({
                'use_residual': [False] * self.num_layers
            })
            logger.info("🚫 已关闭所有残差连接")
            
        elif experiment_type == "layer_ablation":
            # 逐层消融：关闭后半部分层
            ablation_layers = [False] * (self.num_layers // 2) + [True] * (self.num_layers - self.num_layers // 2)
            self.patch_manager.update_control_flags({
                'use_attention': ablation_layers,
                'use_mlp': ablation_layers
            })
            logger.info(f"🚫 已消融前 {self.num_layers // 2} 层")
            
        elif experiment_type == "progressive_degradation":
            # 渐进式降级：会在生成过程中动态调整
            self.patch_manager.update_control_flags({
                'intervention_mode': 'progressive'
            })
            logger.info("🔄 设置为渐进式降级模式")
            
        elif experiment_type == "noise_injection":
            # 注入噪声
            self.patch_manager.update_control_flags({
                'noise_level': 0.1
            })
            logger.info("🎛️ 设置噪声注入: 0.1")
            
        elif experiment_type == "attention_scale_test":
            # 测试attention缩放
            self.patch_manager.update_control_flags({
                'attention_scale': 0.5
            })
            logger.info("🎛️ 设置Attention缩放: 0.5")
            
        elif experiment_type == "mlp_scale_test":
            # 测试MLP缩放
            self.patch_manager.update_control_flags({
                'mlp_scale': 0.5
            })
            logger.info("🎛️ 设置MLP缩放: 0.5")
        
        else:
            logger.warning(f"⚠️ 未知实验类型: {experiment_type}，使用基线配置")
    
    def _run_controlled_generation(self, initial_inputs: Dict[str, torch.Tensor], 
                                 experiment_type: str, image_path: str, prompt: str) -> Dict[str, Any]:
        """运行受控生成过程"""
        logger.info("🔄 开始受控生成...")
        
        # 初始化
        current_inputs = initial_inputs
        generated_token_ids = []
        generation_history = []
        
        for step in range(self.config.max_new_tokens):
            logger.info(f"🔄 Step {step}: 开始生成...")
            
            # 设置当前步骤
            self.patch_manager.set_step(step)
            
            # 动态调整控制（对于progressive模式）
            if experiment_type == "progressive_degradation":
                self._apply_progressive_degradation(step)
            
            step_start_time = time.time()
            
            with torch.no_grad():
                # 前向传播（在MonkeyPatch控制下）
                step_outputs = self.model(
                    **current_inputs,
                    output_hidden_states=False,  # 我们通过patch获取状态
                    return_dict=True
                )
                
                # 获取预测的下一个token
                next_token_logits = step_outputs.logits[0, -1, :]
                next_token_id = next_token_logits.argmax().item()
                
                # 检查是否结束
                if next_token_id == self.processor.tokenizer.eos_token_id:
                    logger.info(f"🏁 遇到EOS token，生成结束于Step {step}")
                    break
                
                # 解码token
                try:
                    next_token_text = self.processor.tokenizer.decode([next_token_id])
                    logger.info(f"🎯 Step {step} 预测token: '{next_token_text}' (ID: {next_token_id})")
                except:
                    next_token_text = f"<TOKEN_{next_token_id}>"
                
                # 分析当前步骤的组件状态
                step_analysis = self._analyze_step_components(step, current_inputs['input_ids'].shape[1])
                
                # 记录步骤信息
                step_info = {
                    'step': step,
                    'sequence_length': current_inputs['input_ids'].shape[1],
                    'predicted_token_id': next_token_id,
                    'predicted_token_text': next_token_text,
                    'generated_so_far': self.processor.tokenizer.decode(generated_token_ids, skip_special_tokens=True),
                    'final_prediction_logits': next_token_logits.cpu().float().numpy(),  # 修复bfloat16问题
                    'step_analysis': step_analysis,
                    'interference_log': [state for state in self.patch_manager.get_interference_log() if state.step == step],
                    'step_duration': time.time() - step_start_time
                }
                
                generation_history.append(step_info)
                generated_token_ids.append(next_token_id)
                
                # 更新输入
                new_token_tensor = torch.tensor([[next_token_id]], device=self.device)
                updated_input_ids = torch.cat([current_inputs['input_ids'], new_token_tensor], dim=1)
                
                new_attention = torch.ones((1, 1), device=self.device, dtype=current_inputs['attention_mask'].dtype)
                updated_attention_mask = torch.cat([current_inputs['attention_mask'], new_attention], dim=1)
                
                current_inputs = {
                    'input_ids': updated_input_ids,
                    'attention_mask': updated_attention_mask,
                    'pixel_values': current_inputs['pixel_values']
                }
                
                logger.info(f"⏱️ Step {step} 耗时: {step_info['step_duration']:.3f}s")
        
        # 生成最终结果
        final_generated_text = self.processor.tokenizer.decode(generated_token_ids, skip_special_tokens=True)
        
        return {
            'initial_prompt': prompt,
            'final_generated_text': final_generated_text,
            'generation_steps': len(generation_history),
            'total_tokens_generated': len(generated_token_ids),
            'generation_history': generation_history,
            'component_states': self.patch_manager.get_component_states(),
            'complete_interference_log': self.patch_manager.get_interference_log()
        }
    
    def _apply_progressive_degradation(self, step: int):
        """应用渐进式降级"""
        # 随着步骤增加，逐渐关闭更多组件
        degradation_rate = min(step * 0.1, 0.8)  # 最多降级80%
        
        layers_to_degrade = int(self.num_layers * degradation_rate)
        
        if layers_to_degrade > 0:
            # 关闭最后几层的MLP
            mlp_flags = [True] * self.num_layers
            for i in range(max(0, self.num_layers - layers_to_degrade), self.num_layers):
                mlp_flags[i] = False
            
            self.patch_manager.update_control_flags({
                'use_mlp': mlp_flags
            })
            
            logger.info(f"🔄 Step {step}: 渐进式降级，关闭了 {layers_to_degrade} 层的MLP")
    
    def _analyze_step_components(self, step: int, sequence_length: int) -> Dict[str, Any]:
        """分析当前步骤的组件状态"""
        step_states = self.patch_manager.get_component_states(step)
        
        if not step_states:
            return {'error': 'No component states recorded'}
        
        analysis = {
            'analyzed_layers': list(step_states.keys()),
            'sequence_length': sequence_length,
            'component_predictions': {},
            'summary': {
                'total_layers_analyzed': len(step_states),
                'components_with_states': 0
            }
        }
        
        # 分析每层的组件状态
        for layer_key, layer_data in step_states.items():
            layer_idx = int(layer_key.split('_')[1])
            layer_analysis = {}
            
            # 分析post_attention状态
            if 'post_attention' in layer_data:
                post_attention_analysis = self.analyze_interference_predictions(
                    layer_data['post_attention'], layer_idx, 'post_attention', step
                )
                layer_analysis['post_attention'] = post_attention_analysis
            
            # 分析final_output状态
            if 'final_output' in layer_data:
                final_output_analysis = self.analyze_interference_predictions(
                    layer_data['final_output'], layer_idx, 'final_output', step
                )
                layer_analysis['final_output'] = final_output_analysis
            
            # 如果同时有post_attention和final_output，计算MLP的贡献
            if 'post_attention' in layer_analysis and 'final_output' in layer_analysis:
                mlp_contribution = self._calculate_mlp_contribution(
                    layer_analysis['post_attention'], 
                    layer_analysis['final_output']
                )
                layer_analysis['mlp_contribution'] = mlp_contribution
            
            if layer_analysis:
                analysis['component_predictions'][layer_idx] = layer_analysis
                analysis['summary']['components_with_states'] += 1
        
        return analysis
    
    def _calculate_mlp_contribution(self, post_attention: Dict, final_output: Dict) -> Dict[str, Any]:
        """计算MLP对预测的贡献"""
        attn_confidence = post_attention['top1_confidence']
        final_confidence = final_output['top1_confidence']
        
        attn_entropy = post_attention['entropy']
        final_entropy = final_output['entropy']
        
        return {
            'confidence_improvement': final_confidence - attn_confidence,
            'entropy_change': final_entropy - attn_entropy,
            'relative_improvement': (final_confidence - attn_confidence) / (attn_confidence + 1e-8),
            'quality_score': final_confidence / (final_entropy + 1e-8),
            'attention_token': post_attention['predictions'][0]['token_text'],
            'final_token': final_output['predictions'][0]['token_text'],
            'prediction_changed': post_attention['predictions'][0]['token_id'] != final_output['predictions'][0]['token_id']
        }
    
    def _analyze_experiment_results(self, experiment_results: Dict[str, Any], 
                                  experiment_type: str) -> Dict[str, Any]:
        """分析实验结果"""
        logger.info(f"📊 分析实验结果: {experiment_type}")
        
        generation_history = experiment_results['generation_history']
        
        # 基本统计
        analysis = {
            'experiment_type': experiment_type,
            'basic_stats': {
                'total_steps': len(generation_history),
                'tokens_generated': experiment_results['total_tokens_generated'],
                'final_text': experiment_results['final_generated_text'],
                'text_length': len(experiment_results['final_generated_text']),
                'average_step_duration': np.mean([step['step_duration'] for step in generation_history])
            },
            'prediction_quality': {},
            'component_analysis': {},
            'interference_impact': {}
        }
        
        # 预测质量分析
        confidences = []
        entropies = []
        perplexities = []
        
        for step_info in generation_history:
            step_analysis = step_info.get('step_analysis', {})
            component_predictions = step_analysis.get('component_predictions', {})
            
            # 收集最终输出的预测质量
            for layer_idx, layer_data in component_predictions.items():
                if 'final_output' in layer_data:
                    final_pred = layer_data['final_output']
                    confidences.append(final_pred['top1_confidence'])
                    entropies.append(final_pred['entropy'])
                    perplexities.append(final_pred['perplexity'])
        
        if confidences:
            analysis['prediction_quality'] = {
                'average_confidence': np.mean(confidences),
                'confidence_std': np.std(confidences),
                'confidence_trend': 'increasing' if len(confidences) > 1 and confidences[-1] > confidences[0] else 'decreasing',
                'average_entropy': np.mean(entropies),
                'entropy_std': np.std(entropies),
                'average_perplexity': np.mean(perplexities),
                'perplexity_std': np.std(perplexities)
            }
        
        # 干扰影响分析
        interference_log = experiment_results['complete_interference_log']
        if interference_log:
            interference_stats = defaultdict(list)
            
            for state in interference_log:
                interference_stats[state.component_type].append(state.metadata['magnitude_change'])
            
            analysis['interference_impact'] = {
                component_type: {
                    'count': len(changes),
                    'average_magnitude': np.mean(changes),
                    'max_magnitude': np.max(changes),
                    'min_magnitude': np.min(changes)
                }
                for component_type, changes in interference_stats.items()
            }
        
        # 组件分析
        if experiment_type in ['attention_only', 'mlp_only']:
            analysis['component_analysis']['isolated_component'] = experiment_type.replace('_only', '')
            analysis['component_analysis']['performance_degradation'] = self._estimate_performance_degradation(analysis)
        
        return analysis
    
    def _estimate_performance_degradation(self, analysis: Dict[str, Any]) -> float:
        """估算性能退化程度"""
        # 简单的性能退化估算，基于置信度和熵
        pred_quality = analysis.get('prediction_quality', {})
        
        if not pred_quality:
            return 0.0
        
        # 基线假设：正常情况下confidence应该>0.7，entropy应该<3.0
        baseline_confidence = 0.7
        baseline_entropy = 3.0
        
        actual_confidence = pred_quality.get('average_confidence', 0.5)
        actual_entropy = pred_quality.get('average_entropy', 5.0)
        
        confidence_degradation = max(0, (baseline_confidence - actual_confidence) / baseline_confidence)
        entropy_degradation = max(0, (actual_entropy - baseline_entropy) / baseline_entropy)
        
        return (confidence_degradation + entropy_degradation) / 2
    
    def _restore_model_safely(self):
        """安全恢复模型"""
        try:
            # 恢复所有patch的层
            for layer_idx, original_forward in self.patch_manager.original_methods.items():
                if layer_idx < len(self.model.language_model.model.layers):
                    layer = self.model.language_model.model.layers[layer_idx]
                    layer.forward = original_forward
                    logger.debug(f"✅ 恢复层 {layer_idx} 的原始方法")
            
            self.patch_manager.is_patched = False
            logger.info("🔄 模型安全恢复完成")
            
        except Exception as e:
            logger.error(f"❌ 模型恢复失败: {e}")
            raise 


class InvasiveExperimentFramework:
    """
    🧪 侵入式实验框架 - 批量实验管理和对比分析
    
    功能：
    1. 批量运行多种干扰实验
    2. 对比不同实验结果
    3. 生成综合分析报告
    4. 创建可视化对比图表
    """
    
    def __init__(self, config: MonkeyPatchConfig):
        self.config = config
        self.analyzer = InvasiveLogitLensAnalyzer(config)
        self.experiment_results = {}
        
        logger.info("🧪 侵入式实验框架初始化完成")
    
    def run_comprehensive_experiments(self, image_path: str, prompt: str, 
                                    experiment_types: List[str] = None) -> Dict[str, Any]:
        """
        🔬 运行综合实验 - 批量执行多种干扰实验
        
        默认实验类型：
        - baseline: 基线
        - attention_only: 仅Attention
        - mlp_only: 仅MLP
        - no_residual: 无残差连接
        - layer_ablation: 层消融
        - progressive_degradation: 渐进式降级
        - noise_injection: 噪声注入
        """
        if experiment_types is None:
            experiment_types = [
                "baseline",
                "attention_only", 
                "mlp_only",
                "no_residual",
                "layer_ablation",
                "progressive_degradation",
                "noise_injection"
            ]
        
        logger.info(f"🔬 开始综合实验，共 {len(experiment_types)} 种类型")
        logger.info(f"📋 实验类型: {experiment_types}")
        
        comprehensive_results = {
            'experiment_metadata': {
                'image_path': image_path,
                'prompt': prompt,
                'experiment_types': experiment_types,
                'model_path': self.config.model_path,
                'timestamp': datetime.now().strftime("%Y%m%d_%H%M%S")
            },
            'individual_results': {},
            'comparative_analysis': {},
            'summary': {}
        }
        
        # 逐个运行实验
        for i, experiment_type in enumerate(experiment_types):
            logger.info(f"\n🧪 === 实验 {i+1}/{len(experiment_types)}: {experiment_type} ===")
            
            try:
                # 清空之前的状态
                self.analyzer.patch_manager.clear_states()
                
                # 运行单个实验
                experiment_result = self.analyzer.run_interference_experiment(
                    image_path, prompt, experiment_type
                )
                
                comprehensive_results['individual_results'][experiment_type] = experiment_result
                logger.info(f"✅ 实验 {experiment_type} 完成")
                
            except Exception as e:
                logger.error(f"❌ 实验 {experiment_type} 失败: {e}")
                comprehensive_results['individual_results'][experiment_type] = {
                    'error': str(e),
                    'experiment_type': experiment_type
                }
        
        # 进行对比分析
        logger.info("📊 开始对比分析...")
        comprehensive_results['comparative_analysis'] = self._perform_comparative_analysis(
            comprehensive_results['individual_results']
        )
        
        # 生成摘要
        comprehensive_results['summary'] = self._generate_experiment_summary(
            comprehensive_results
        )
        
        # 保存结果
        self.experiment_results = comprehensive_results
        
        logger.info("🎉 综合实验完成！")
        return comprehensive_results
    
    def _perform_comparative_analysis(self, individual_results: Dict[str, Any]) -> Dict[str, Any]:
        """对比分析不同实验的结果"""
        logger.info("📊 执行对比分析...")
        
        comparative_analysis = {
            'text_generation_comparison': {},
            'prediction_quality_comparison': {},
            'performance_impact': {},
            'component_importance': {},
            'generation_diversity': {}
        }
        
        # 1. 文本生成对比
        baseline_text = None
        for exp_type, result in individual_results.items():
            if 'error' in result:
                continue
                
            generated_text = result['generation_results']['final_generated_text']
            
            if exp_type == 'baseline':
                baseline_text = generated_text
            
            comparative_analysis['text_generation_comparison'][exp_type] = {
                'generated_text': generated_text,
                'text_length': len(generated_text),
                'word_count': len(generated_text.split()),
                'unique_words': len(set(generated_text.lower().split()))
            }
        
        # 2. 预测质量对比
        for exp_type, result in individual_results.items():
            if 'error' in result:
                continue
                
            analysis = result.get('analysis', {})
            pred_quality = analysis.get('prediction_quality', {})
            
            if pred_quality:
                comparative_analysis['prediction_quality_comparison'][exp_type] = {
                    'average_confidence': pred_quality.get('average_confidence', 0),
                    'average_entropy': pred_quality.get('average_entropy', 0),
                    'average_perplexity': pred_quality.get('average_perplexity', 0),
                    'confidence_std': pred_quality.get('confidence_std', 0)
                }
        
        # 3. 性能影响分析
        if baseline_text:
            for exp_type, result in individual_results.items():
                if 'error' in result or exp_type == 'baseline':
                    continue
                    
                generated_text = result['generation_results']['final_generated_text']
                
                # 计算与基线的差异
                text_similarity = self._calculate_text_similarity(baseline_text, generated_text)
                length_ratio = len(generated_text) / len(baseline_text) if baseline_text else 0
                
                # 获取性能退化估计
                analysis = result.get('analysis', {})
                degradation = analysis.get('component_analysis', {}).get('performance_degradation', 0)
                
                comparative_analysis['performance_impact'][exp_type] = {
                    'text_similarity_to_baseline': text_similarity,
                    'length_ratio_to_baseline': length_ratio,
                    'estimated_degradation': degradation,
                    'quality_score': 1.0 - degradation  # 简单的质量评分
                }
        
        # 4. 组件重要性分析
        component_scores = {}
        for exp_type, result in individual_results.items():
            if 'error' in result:
                continue
                
            if exp_type in ['attention_only', 'mlp_only']:
                pred_quality = result.get('analysis', {}).get('prediction_quality', {})
                confidence = pred_quality.get('average_confidence', 0)
                
                component_name = exp_type.replace('_only', '')
                component_scores[component_name] = confidence
        
        if component_scores:
            comparative_analysis['component_importance'] = component_scores
        
        # 5. 生成多样性分析
        texts = []
        for exp_type, result in individual_results.items():
            if 'error' not in result:
                texts.append(result['generation_results']['final_generated_text'])
        
        if texts:
            comparative_analysis['generation_diversity'] = {
                'total_experiments': len(texts),
                'unique_texts': len(set(texts)),
                'diversity_ratio': len(set(texts)) / len(texts),
                'average_length': np.mean([len(text) for text in texts]),
                'length_std': np.std([len(text) for text in texts])
            }
        
        return comparative_analysis
    
    def _calculate_text_similarity(self, text1: str, text2: str) -> float:
        """计算两个文本的相似性（简单版本）"""
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 and not words2:
            return 1.0
        if not words1 or not words2:
            return 0.0
        
        intersection = words1.intersection(words2)
        union = words1.union(words2)
        
        return len(intersection) / len(union) if union else 0.0
    
    def _generate_experiment_summary(self, comprehensive_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成实验摘要"""
        individual_results = comprehensive_results['individual_results']
        comparative_analysis = comprehensive_results['comparative_analysis']
        
        summary = {
            'total_experiments': len(individual_results),
            'successful_experiments': len([r for r in individual_results.values() if 'error' not in r]),
            'failed_experiments': len([r for r in individual_results.values() if 'error' in r]),
            'best_performing_experiment': None,
            'most_degraded_experiment': None,
            'key_findings': []
        }
        
        # 找到最佳和最差实验
        performance_impact = comparative_analysis.get('performance_impact', {})
        if performance_impact:
            # 最佳：质量分数最高
            best_exp = max(performance_impact.items(), key=lambda x: x[1].get('quality_score', 0))
            summary['best_performing_experiment'] = {
                'experiment_type': best_exp[0],
                'quality_score': best_exp[1].get('quality_score', 0)
            }
            
            # 最差：退化最严重
            worst_exp = max(performance_impact.items(), key=lambda x: x[1].get('estimated_degradation', 0))
            summary['most_degraded_experiment'] = {
                'experiment_type': worst_exp[0],
                'degradation': worst_exp[1].get('estimated_degradation', 0)
            }
        
        # 关键发现
        component_importance = comparative_analysis.get('component_importance', {})
        if component_importance:
            if 'attention' in component_importance and 'mlp' in component_importance:
                attn_score = component_importance['attention']
                mlp_score = component_importance['mlp']
                
                if attn_score > mlp_score:
                    summary['key_findings'].append(f"Attention组件更重要 (置信度: {attn_score:.3f} vs {mlp_score:.3f})")
                else:
                    summary['key_findings'].append(f"MLP组件更重要 (置信度: {mlp_score:.3f} vs {attn_score:.3f})")
        
        diversity = comparative_analysis.get('generation_diversity', {})
        if diversity:
            diversity_ratio = diversity.get('diversity_ratio', 0)
            if diversity_ratio < 0.5:
                summary['key_findings'].append(f"生成文本多样性较低 (多样性比例: {diversity_ratio:.2f})")
            else:
                summary['key_findings'].append(f"生成文本多样性较高 (多样性比例: {diversity_ratio:.2f})")
        
        return summary
    
    def create_comprehensive_visualization(self, save_path: str):
        """创建综合可视化"""
        if not self.experiment_results:
            logger.warning("⚠️ 没有实验结果可以可视化")
            return
        
        logger.info("🎨 创建综合可视化...")
        
        try:
            # 创建多子图
            logger.info("📊 创建图表布局...")
            fig, axes = plt.subplots(2, 3, figsize=(20, 12))
            
            # 1. 文本长度对比
            logger.info("📏 绘制文本长度对比...")
            self._plot_text_length_comparison(axes[0, 0])
            
            # 2. 预测质量对比
            logger.info("📈 绘制预测质量对比...")
            self._plot_prediction_quality_comparison(axes[0, 1])
            
            # 3. 组件重要性
            logger.info("🔧 绘制组件重要性...")
            self._plot_component_importance(axes[0, 2])
            
            # 4. 性能影响
            logger.info("⚡ 绘制性能影响...")
            self._plot_performance_impact(axes[1, 0])
            
            # 5. 生成多样性
            logger.info("🎯 绘制生成多样性...")
            self._plot_generation_diversity(axes[1, 1])
            
            # 6. 实验成功率
            logger.info("✅ 绘制实验成功率...")  
            self._plot_experiment_success_rate(axes[1, 2])
            
            logger.info("💾 保存图表...")
            plt.tight_layout()
            plt.savefig(save_path, dpi=150, bbox_inches='tight')  # 降低DPI加速保存
            plt.close()
            
            logger.info(f"✅ 综合可视化保存: {save_path}")
            
        except Exception as e:
            logger.error(f"❌ 可视化创建失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 创建一个简单的错误图表
            try:
                fig, ax = plt.subplots(figsize=(10, 6))
                ax.text(0.5, 0.5, f'可视化创建失败:\n{str(e)}', 
                       transform=ax.transAxes, ha='center', va='center', fontsize=12)
                ax.set_title('可视化错误')
                plt.savefig(save_path.replace('.png', '_error.png'), dpi=100)
                plt.close()
                logger.info(f"📋 错误报告保存: {save_path.replace('.png', '_error.png')}")
            except:
                pass
    
    def _plot_text_length_comparison(self, ax):
        """绘制文本长度对比"""
        text_comparison = self.experiment_results['comparative_analysis']['text_generation_comparison']
        
        experiments = list(text_comparison.keys())
        lengths = [text_comparison[exp]['text_length'] for exp in experiments]
        
        bars = ax.bar(experiments, lengths, alpha=0.7)
        ax.set_title('Generated Text Length Comparison')
        ax.set_ylabel('Text Length (characters)')
        ax.tick_params(axis='x', rotation=45)
        
        # 高亮基线
        if 'baseline' in experiments:
            baseline_idx = experiments.index('baseline')
            bars[baseline_idx].set_color('red')
            bars[baseline_idx].set_alpha(1.0)
    
    def _plot_prediction_quality_comparison(self, ax):
        """绘制预测质量对比"""
        quality_comparison = self.experiment_results['comparative_analysis']['prediction_quality_comparison']
        
        experiments = list(quality_comparison.keys())
        confidences = [quality_comparison[exp]['average_confidence'] for exp in experiments]
        entropies = [quality_comparison[exp]['average_entropy'] for exp in experiments]
        
        x = np.arange(len(experiments))
        width = 0.35
        
        ax.bar(x - width/2, confidences, width, label='Confidence', alpha=0.7)
        ax.bar(x + width/2, [e/10 for e in entropies], width, label='Entropy/10', alpha=0.7)
        
        ax.set_title('Prediction Quality Comparison')
        ax.set_ylabel('Score')
        ax.set_xticks(x)
        ax.set_xticklabels(experiments, rotation=45)
        ax.legend()
    
    def _plot_component_importance(self, ax):
        """绘制组件重要性"""
        component_importance = self.experiment_results['comparative_analysis'].get('component_importance', {})
        
        if component_importance:
            components = list(component_importance.keys())
            scores = list(component_importance.values())
            
            colors = ['blue' if comp == 'attention' else 'orange' for comp in components]
            bars = ax.bar(components, scores, color=colors, alpha=0.7)
            
            ax.set_title('Component Importance')
            ax.set_ylabel('Performance Score')
            
            # 添加数值标签
            for bar, score in zip(bars, scores):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                       f'{score:.3f}', ha='center', va='bottom')
        else:
            ax.text(0.5, 0.5, 'No Component Data', transform=ax.transAxes, 
                   ha='center', va='center', fontsize=14)
            ax.set_title('Component Importance')
    
    def _plot_performance_impact(self, ax):
        """绘制性能影响"""
        performance_impact = self.experiment_results['comparative_analysis'].get('performance_impact', {})
        
        if performance_impact:
            experiments = list(performance_impact.keys())
            quality_scores = [performance_impact[exp]['quality_score'] for exp in experiments]
            
            colors = ['green' if score > 0.8 else 'orange' if score > 0.5 else 'red' for score in quality_scores]
            bars = ax.bar(experiments, quality_scores, color=colors, alpha=0.7)
            
            ax.set_title('Performance Impact')
            ax.set_ylabel('Quality Score')
            ax.tick_params(axis='x', rotation=45)
            ax.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='Degradation Threshold')
            ax.legend()
        else:
            ax.text(0.5, 0.5, 'No Performance Data', transform=ax.transAxes, 
                   ha='center', va='center', fontsize=14)
            ax.set_title('Performance Impact')
    
    def _plot_generation_diversity(self, ax):
        """绘制生成多样性"""
        diversity = self.experiment_results['comparative_analysis'].get('generation_diversity', {})
        
        if diversity:
            metrics = ['Total Experiments', 'Unique Texts', 'Diversity Ratio']
            values = [
                diversity['total_experiments'],
                diversity['unique_texts'],
                diversity['diversity_ratio'] * 10  # 缩放到可视化范围
            ]
            
            bars = ax.bar(metrics, values, alpha=0.7, color=['blue', 'green', 'purple'])
            ax.set_title('Generation Diversity')
            ax.set_ylabel('Count / Ratio*10')
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                display_value = value if bar.get_x() < 2 else value / 10  # 还原diversity ratio
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                       f'{display_value:.2f}', ha='center', va='bottom')
        else:
            ax.text(0.5, 0.5, 'No Diversity Data', transform=ax.transAxes, 
                   ha='center', va='center', fontsize=14)
            ax.set_title('Generation Diversity')
    
    def _plot_experiment_success_rate(self, ax):
        """绘制实验成功率"""
        summary = self.experiment_results['summary']
        
        total = summary['total_experiments']
        successful = summary['successful_experiments']
        failed = summary['failed_experiments']
        
        labels = ['Successful', 'Failed']
        sizes = [successful, failed]
        colors = ['green', 'red']
        
        if total > 0:
            wedges, texts, autotexts = ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', alpha=0.7)
            ax.set_title(f'Experiment Success Rate\n(Total: {total})')
        else:
            ax.text(0.5, 0.5, 'No Experiments', transform=ax.transAxes, 
                   ha='center', va='center', fontsize=14)
            ax.set_title('Experiment Success Rate')
    
    def create_interactive_comparison_html(self, save_path: str):
        """创建交互式对比HTML"""
        logger.info("🌐 创建交互式对比HTML...")
        
        try:
            # 编码图像（如果有的话）
            logger.info("🖼️ 处理图像编码...")
            image_base64 = ""
            image_path = self.experiment_results['experiment_metadata'].get('image_path', '')
            if image_path and os.path.exists(image_path):
                try:
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                    image_base64 = base64.b64encode(image_data).decode('utf-8')
                    logger.info("✅ 图像编码完成")
                except Exception as e:
                    logger.warning(f"图像编码失败: {e}")
            
            # 序列化数据
            logger.info("📝 序列化实验数据...")
            try:
                results_json = json.dumps(self._make_serializable(self.experiment_results), indent=2)
                logger.info("✅ 数据序列化完成")
            except Exception as e:
                logger.error(f"数据序列化失败: {e}")
                # 创建简化的数据结构
                simplified_results = {
                    'summary': self.experiment_results.get('summary', {}),
                    'individual_results': {}
                }
                for exp_name, exp_data in self.experiment_results.get('individual_results', {}).items():
                    simplified_results['individual_results'][exp_name] = {
                        'experiment_type': exp_data.get('experiment_type', exp_name),
                        'generation_results': {
                            'final_generated_text': exp_data.get('generation_results', {}).get('final_generated_text', 'N/A')
                        },
                        'analysis': exp_data.get('analysis', {}),
                        'error': exp_data.get('error', None)
                    }
                results_json = json.dumps(simplified_results, indent=2)
                logger.info("✅ 简化数据序列化完成")
            
            logger.info("🏗️ 构建HTML内容...")
            html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma2 MonkeyPatch 干扰实验对比分析</title>
    <style>
        body {{ margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }}
        .container {{ max-width: 1800px; margin: 0 auto; }}
        .header {{ text-align: center; background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .experiment-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; }}
        .experiment-card {{ background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .experiment-title {{ font-size: 18px; font-weight: bold; margin-bottom: 15px; padding: 10px; border-radius: 5px; }}
        .baseline {{ background-color: #ffeb3b; }}
        .attention-only {{ background-color: #2196f3; color: white; }}
        .mlp-only {{ background-color: #ff9800; color: white; }}
        .degraded {{ background-color: #f44336; color: white; }}
        .normal {{ background-color: #4caf50; color: white; }}
        
        .metric-row {{ display: flex; justify-content: space-between; margin: 8px 0; }}
        .metric-label {{ font-weight: bold; }}
        .metric-value {{ color: #666; }}
        
        .generated-text {{ background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; font-family: monospace; }}
        .summary-panel {{ background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .key-findings {{ background: #fff3e0; padding: 15px; border-radius: 8px; margin: 10px 0; }}
        
        .comparison-controls {{ background: white; padding: 15px; border-radius: 10px; margin-bottom: 20px; }}
        .filter-btn {{ margin: 5px; padding: 8px 15px; border: none; border-radius: 20px; cursor: pointer; }}
        .filter-btn.active {{ background: #2196f3; color: white; }}
        .filter-btn:not(.active) {{ background: #e0e0e0; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🐒 PaliGemma2 MonkeyPatch 干扰实验对比分析</h1>
            <p><strong>模型:</strong> {self.experiment_results['experiment_metadata']['model_path']}</p>
            <p><strong>提示:</strong> "{self.experiment_results['experiment_metadata']['prompt']}"</p>
            <p><strong>时间:</strong> {self.experiment_results['experiment_metadata']['timestamp']}</p>
            {"<img src='data:image/jpeg;base64," + image_base64 + "' style='max-height: 200px; border-radius: 8px; margin: 10px;'>" if image_base64 else ""}
        </div>
        
        <div class="summary-panel">
            <h2>📊 实验摘要</h2>
            <div class="metric-row">
                <span class="metric-label">总实验数:</span>
                <span class="metric-value">{self.experiment_results['summary']['total_experiments']}</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">成功实验:</span>
                <span class="metric-value">{self.experiment_results['summary']['successful_experiments']}</span>
            </div>
            <div class="metric-row">
                <span class="metric-label">失败实验:</span>
                <span class="metric-value">{self.experiment_results['summary']['failed_experiments']}</span>
            </div>
            
            <div class="key-findings">
                <h3>🔍 关键发现</h3>
                <ul>
                    {"".join([f"<li>{finding}</li>" for finding in self.experiment_results['summary']['key_findings']])}
                </ul>
            </div>
        </div>
        
        <div class="comparison-controls">
            <h3>🎛️ 对比控制</h3>
            <button class="filter-btn active" onclick="toggleFilter('all')">显示全部</button>
            <button class="filter-btn" onclick="toggleFilter('successful')">仅成功</button>
            <button class="filter-btn" onclick="toggleFilter('component')">组件对比</button>
            <button class="filter-btn" onclick="toggleFilter('degradation')">性能影响</button>
        </div>
        
        <div class="experiment-grid" id="experimentGrid">
            <!-- 实验卡片将通过JavaScript生成 -->
        </div>
    </div>

<script>
    const experimentResults = {results_json};
    let currentFilter = 'all';
    
    function getExperimentClass(expType) {{
        if (expType === 'baseline') return 'baseline';
        if (expType === 'attention_only') return 'attention-only';
        if (expType === 'mlp_only') return 'mlp-only';
        if (expType.includes('degradation') || expType.includes('ablation')) return 'degraded';
        return 'normal';
    }}
    
    function createExperimentCard(expType, result) {{
        if (result.error) {{
            return `
                <div class="experiment-card">
                    <div class="experiment-title degraded">${{expType}} (失败)</div>
                    <p style="color: red;">错误: ${{result.error}}</p>
                </div>
            `;
        }}
        
        const analysis = result.analysis || {{}};
        const basicStats = analysis.basic_stats || {{}};
        const predQuality = analysis.prediction_quality || {{}};
        const generatedText = result.generation_results.final_generated_text || '';
        
        return `
            <div class="experiment-card" data-experiment="${{expType}}">
                <div class="experiment-title ${{getExperimentClass(expType)}}">${{expType}}</div>
                
                <div class="metric-row">
                    <span class="metric-label">生成步数:</span>
                    <span class="metric-value">${{basicStats.total_steps || 'N/A'}}</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">平均置信度:</span>
                    <span class="metric-value">${{(predQuality.average_confidence || 0).toFixed(3)}}</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">平均熵:</span>
                    <span class="metric-value">${{(predQuality.average_entropy || 0).toFixed(3)}}</span>
                </div>
                <div class="metric-row">
                    <span class="metric-label">文本长度:</span>
                    <span class="metric-value">${{basicStats.text_length || 0}} 字符</span>
                </div>
                
                <div class="generated-text">
                    <strong>生成文本:</strong><br>
                    "${{generatedText}}"
                </div>
            </div>
        `;
    }}
    
    function renderExperiments() {{
        const grid = document.getElementById('experimentGrid');
        const individualResults = experimentResults.individual_results || {{}};
        
        let html = '';
        for (const [expType, result] of Object.entries(individualResults)) {{
            if (shouldShowExperiment(expType, result)) {{
                html += createExperimentCard(expType, result);
            }}
        }}
        
        grid.innerHTML = html;
    }}
    
    function shouldShowExperiment(expType, result) {{
        if (currentFilter === 'all') return true;
        if (currentFilter === 'successful') return !result.error;
        if (currentFilter === 'component') return expType.includes('only') || expType === 'baseline';
        if (currentFilter === 'degradation') return expType.includes('degradation') || expType.includes('ablation') || expType.includes('noise');
        return true;
    }}
    
    function toggleFilter(filterType) {{
        currentFilter = filterType;
        
        // 更新按钮状态
        document.querySelectorAll('.filter-btn').forEach(btn => {{
            btn.classList.remove('active');
        }});
        event.target.classList.add('active');
        
        // 重新渲染
        renderExperiments();
    }}
    
    // 初始渲染
    renderExperiments();
</script>
</body>
</html>
            """
            
            # 保存HTML
            logger.info("💾 保存HTML文件...")
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            logger.info(f"✅ 交互式对比HTML保存: {save_path}")
            
        except Exception as e:
            logger.error(f"❌ HTML创建失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 创建简单的错误页面
            try:
                error_html = f"""
<!DOCTYPE html>
<html>
<head><title>HTML创建错误</title></head>
<body>
    <h1>HTML创建失败</h1>
    <p>错误: {str(e)}</p>
    <p>请检查日志了解详细信息。</p>
</body>
</html>
                """
                with open(save_path.replace('.html', '_error.html'), 'w', encoding='utf-8') as f:
                    f.write(error_html)
                logger.info(f"📋 错误HTML保存: {save_path.replace('.html', '_error.html')}")
            except:
                pass
    
    def save_results(self, sample_id: str):
        """保存实验结果"""
        if not self.experiment_results:
            logger.warning("⚠️ 没有实验结果可以保存")
            return
        
        # 保存JSON格式
        json_path = os.path.join(self.config.output_dir, f"comprehensive_experiments_{sample_id}.json")
        serializable_results = self._make_serializable(self.experiment_results)
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        # 保存pickle格式
        pickle_path = os.path.join(self.config.output_dir, f"comprehensive_experiments_{sample_id}.pkl")
        with open(pickle_path, 'wb') as f:
            pickle.dump(self.experiment_results, f)
        
        logger.info(f"📁 综合实验结果已保存: {json_path}, {pickle_path}")
    
    def _make_serializable(self, obj):
        """将包含tensor的对象转换为可序列化格式"""
        if isinstance(obj, torch.Tensor):
            return obj.cpu().tolist()
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self._make_serializable(item) for item in obj)
        elif hasattr(obj, '__dict__'):
            # 处理dataclass对象
            return self._make_serializable(obj.__dict__)
        else:
            return obj 


def demo_monkey_patch_analysis():
    """
    🎯 演示MonkeyPatch干扰分析的完整流程
    
    展示如何：
    1. 配置干扰参数
    2. 运行单个干扰实验
    3. 运行综合对比实验
    4. 生成可视化结果
    """
    print("🐒 开始PaliGemma2 MonkeyPatch干扰分析演示...")
    
    # 1. 配置实验参数
    config = MonkeyPatchConfig(
        model_path="google/paligemma2-3b-pt-224",
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        max_new_tokens=20,
        output_dir="./demo_monkey_patch_results",
        experiment_types=["baseline", "attention_only", "mlp_only", "no_residual"]
    )
    
    # 2. 设置示例输入
    # 你可以替换为你自己的图像和提示
    image_path = "/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images/real_image_20250610_170727.png"  # 替换为实际图像路径
    prompt = "Describe what you see in this image."
    
    # 检查图像是否存在
    if not os.path.exists(image_path):
        print(f"⚠️ 图像文件不存在: {image_path}")
        print("请提供有效的图像路径，或创建一个测试图像")
        
        # 创建一个简单的测试图像
        import PIL.Image
        test_image = PIL.Image.new('RGB', (224, 224), color='red')
        test_image.save(image_path)
        print(f"✅ 已创建测试图像: {image_path}")
    
    try:
        # 3. 运行单个实验演示
        print("\n📊 运行单个干扰实验演示...")
        analyzer = InvasiveLogitLensAnalyzer(config)
        
        single_result = analyzer.run_interference_experiment(
            image_path=image_path,
            prompt=prompt,
            experiment_type="attention_only"
        )
        
        print(f"✅ 单实验完成 - 生成文本: {single_result.get('generation_results', {}).get('final_generated_text', 'N/A')}")
        
        # 4. 运行综合实验框架
        print("\n🔬 运行综合干扰实验框架...")
        framework = InvasiveExperimentFramework(config)
        
        comprehensive_results = framework.run_comprehensive_experiments(
            image_path=image_path,
            prompt=prompt,
            experiment_types=["baseline", "attention_only", "mlp_only"]
        )
        
        # 5. 生成可视化结果
        print("\n📈 生成可视化结果...")
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存结果
        framework.save_results(f"demo_{timestamp}")
        
        # 创建可视化图表
        if config.create_comparison_plots:
            viz_path = os.path.join(config.output_dir, f"comprehensive_visualization_demo_{timestamp}.png")
            framework.create_comprehensive_visualization(viz_path)
            
        # 创建交互式HTML
        if config.create_interactive_html:
            html_path = os.path.join(config.output_dir, f"interactive_comparison_demo_{timestamp}.html")
            framework.create_interactive_comparison_html(html_path)
        
        # 6. 显示结果摘要
        print("\n📋 实验结果摘要:")
        summary = comprehensive_results.get('summary', {})
        print(f"  - 总实验数: {summary.get('total_experiments', 0)}")
        print(f"  - 成功实验: {summary.get('successful_experiments', 0)}")
        print(f"  - 失败实验: {summary.get('failed_experiments', 0)}")
        
        if 'key_findings' in summary:
            print("  - 关键发现:")
            for finding in summary['key_findings'][:3]:  # 显示前3个发现
                print(f"    • {finding}")
        
        print(f"\n✅ 演示完成！结果保存在: {config.output_dir}")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
        # 安全清理
        try:
            analyzer._restore_model_safely()
        except:
            pass


def create_quick_test():
    """
    🚀 快速测试函数 - 验证系统基本功能
    """
    print("🔧 运行快速功能测试...")
    
    # 测试配置创建
    config = MonkeyPatchConfig(max_new_tokens=5)
    print("✅ 配置创建成功")
    
    # 测试MonkeyPatch管理器
    manager = MonkeyPatchManager(config)
    print("✅ MonkeyPatch管理器初始化成功")
    
    # 测试分析器初始化
    try:
        analyzer = InvasiveLogitLensAnalyzer(config)
        print("✅ 分析器初始化成功")
    except Exception as e:
        print(f"⚠️ 分析器初始化失败（可能是模型加载问题）: {e}")
        return False
    
    print("🎉 快速测试通过！")
    return True


if __name__ == "__main__":
    print("=" * 80)
    print("🐒 PaliGemma2 MonkeyPatch 侵入式干扰分析系统")
    print("=" * 80)
    
    # 设置随机种子
    set_seed(42)
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description="PaliGemma2 MonkeyPatch干扰分析")
    parser.add_argument("--mode", type=str, default="demo", 
                       choices=["demo", "test", "custom"],
                       help="运行模式：demo（演示）, test（测试）, custom（自定义）")
    parser.add_argument("--image", type=str, default="/home/<USER>/claude/SpatialVLA/coke.png",
                       help="输入图像路径")
    parser.add_argument("--prompt", type=str, default="describe the image",
                       help="输入提示文本")
    parser.add_argument("--experiments", type=str, nargs="+", 
                       default=["baseline", "attention_only", "mlp_only"],
                       help="要运行的实验类型")
    parser.add_argument("--output_dir", type=str, default="/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/pro/paligemma2_monkey_patch_results",
                       help="输出目录")
    
    args = parser.parse_args()
    
    if args.mode == "test":
        # 快速测试模式
        success = create_quick_test()
        if success:
            print("\n🎯 所有基本功能测试通过！")
            print("💡 提示: 使用 --mode demo 运行完整演示")
        else:
            print("\n❌ 基本功能测试失败，请检查环境配置")
            
    elif args.mode == "demo":
        # 演示模式
        demo_monkey_patch_analysis()
        
    elif args.mode == "custom":
        # 自定义模式
        print(f"🎛️ 自定义模式启动...")
        print(f"  - 图像: {args.image}")
        print(f"  - 提示: {args.prompt}")
        print(f"  - 实验: {args.experiments}")
        print(f"  - 输出: {args.output_dir}")
        
        # 自定义配置
        config = MonkeyPatchConfig(
            output_dir=args.output_dir,
            experiment_types=args.experiments
        )
        
        # 运行自定义实验
        framework = InvasiveExperimentFramework(config)
        results = framework.run_comprehensive_experiments(
            image_path=args.image,
            prompt=args.prompt,
            experiment_types=args.experiments
        )
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        framework.save_results(f"custom_{timestamp}")
        print(f"✅ 自定义实验完成！结果保存在: {args.output_dir}")
    
    print("\n🎉 程序执行完成！")