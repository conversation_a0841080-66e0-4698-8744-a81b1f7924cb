#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Fine-Grained Logit Lens Analysis - Attention vs MLP
============================================================

🔬 【修正版】真正的细粒度分析：基于正确的Gemma2架构分析Attention和MLP组件贡献

### 🚨 架构修正：基于真实的Gemma2DecoderLayer

#### ❌ 之前版本的问题：
- 对Gemma2的4层normalization结构理解错误
- Monkey Patch未遵循真实的forward流程
- 缺少正确的residual connection处理

#### ✅ 修正版本（基于真实架构）：
```python
# 真实的Gemma2DecoderLayer.forward流程：

# === Attention Block ===
residual = hidden_states
hidden_states = input_layernorm(hidden_states)          # Pre-norm
hidden_states, _, _ = self_attn(hidden_states, ...)     # Self-attention
hidden_states = post_attention_layernorm(hidden_states) # Post-norm  
hidden_states = residual + hidden_states               # Residual
# ⭐ POST_ATTENTION_STATE（纯Attention贡献）

# === MLP Block ===
residual = hidden_states
hidden_states = pre_feedforward_layernorm(hidden_states) # Pre-norm
hidden_states = mlp(hidden_states)                      # MLP
hidden_states = post_feedforward_layernorm(hidden_states) # Post-norm
hidden_states = residual + hidden_states                # Residual  
# ⭐ POST_MLP_STATE（Attention + MLP完整贡献）
```

### 🎯 核心技术改进

#### 1. 正确的架构理解
- **4个Normalization层**：
  - `input_layernorm`：Attention前的Pre-norm
  - `post_attention_layernorm`：Attention后的Post-norm
  - `pre_feedforward_layernorm`：MLP前的Pre-norm  
  - `post_feedforward_layernorm`：MLP后的Post-norm

#### 2. 精准的中间状态捕获
- **Post-Attention State**：`residual + post_attention_layernorm(raw_attention_output)`
- **Post-MLP State**：`residual + post_feedforward_layernorm(mlp_output)`

#### 3. 真实的Residual Pattern
- 每个子模块都遵循 `x = residual + norm(sublayer(norm(x)))` 模式

### 🔬 分析维度

1. **Attention贡献**：Post-Attention状态的预测能力
2. **MLP贡献**：Post-MLP与Post-Attention状态的差异  
3. **完整预测**：Post-MLP状态的最终预测能力
4. **组件对比**：不同生成步骤中Attention vs MLP的相对重要性

### 📊 输出结果

- **组件对比图**：Attention vs MLP置信度演化
- **贡献度分析**：MLP对Attention结果的改善程度
- **交互式HTML**：逐步骤详细分析界面
- **统计报告**：组件优势分布和演化趋势

作者: Assistant  
日期: 2025-01-14
版本: 【修正版】基于真实Gemma2架构的细粒度分析
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn.functional as F
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any, Generator
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
from PIL import Image
from tqdm import tqdm
import argparse
import logging
from collections import defaultdict
import pickle
import copy
import random
import cv2
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入transformers
try:
    from transformers import (
        AutoProcessor, AutoModelForImageTextToText, 
        PaliGemmaForConditionalGeneration,
        set_seed
    )
    logger.info("成功导入Transformers组件。")
except ImportError as e:
    logger.error(f"导入Transformers失败: {e}")
    sys.exit(1)

import warnings
warnings.filterwarnings("ignore")


@dataclass
class FineGrainedLogitLensConfig:
    """细粒度Logit Lens分析配置"""
    
    # 模型配置
    model_path: str = "google/paligemma2-3b-pt-224"
    device: str = "cuda:0" if torch.cuda.is_available() else "cpu"
    dtype: str = "bf16"
    
    # 生成配置
    max_new_tokens: int = 15
    do_sample: bool = False
    temperature: float = 1.0
    
    # 细粒度分析配置
    target_layers: List[int] = field(default_factory=lambda: [0, 5, 10, 15, 20, 25, 26])
    top_k_tokens: int = 5
    
    # 组件分析配置
    analyze_attention: bool = True      # 分析Attention贡献
    analyze_mlp: bool = True           # 分析MLP贡献
    analyze_interaction: bool = True    # 分析交互效应
    
    # 位置分析配置
    analyze_all_positions: bool = False  # 简化：只分析最后位置
    position_sample_rate: float = 0.1   # 位置采样率
    
    # 输出配置
    output_dir: str = "./paligemma2_fine_grained_results"
    save_component_details: bool = True
    
    # 可视化配置
    enable_visualization: bool = True
    create_comparison_charts: bool = True
    create_interactive_html: bool = True


class ComponentHookManager:
    """
    🔧 安全Hook管理器 - 完全移除Monkey Patch
    
    ✅ 优势：
    - 不破坏模型计算流程
    - 安全可靠，易于调试
    - 性能优异
    - 100%保持原始模型行为
    
    🎯 核心思路：
    使用标准PyTorch Hook精确捕获关键组件的输出
    """
    
    def __init__(self):
        self.hooks = []
        self.captured_states = {}
        self.current_step = 0
        
    def reset_for_step(self, step: int):
        """为新的生成步骤重置状态"""
        self.current_step = step
        self.captured_states[step] = {}
        
    def register_component_hooks(self, model, target_layers: List[int]):
        """
        🎯 注册组件Hook - 安全方案
        
        策略：
        1. Hook住self_attn.o_proj的输出 → 获取原始attention输出
        2. Hook住MLP的输出 → 获取原始MLP输出（不加残差）
        3. Hook住整个DecoderLayer的输出 → 获取最终状态
        4. 通过数学计算得到各组件贡献
        """
        logger.info(f"🔗 注册安全Hook用于层 {target_layers}...")
        
        # 清除现有hooks
        self.clear_hooks()
        
        for layer_idx in target_layers:
            if layer_idx < len(model.language_model.model.layers):
                layer = model.language_model.model.layers[layer_idx]
                
                # 🎯 Hook 1: 捕获Self-Attention的原始输出
                def create_attention_hook(layer_idx):
                    def hook_fn(module, input, output):
                        # output是self_attn的输出: (hidden_states, attn_weights, present_key_value)
                        if isinstance(output, tuple):
                            attn_hidden_states = output[0]  # [batch, seq, hidden]
                        else:
                            attn_hidden_states = output
                            
                        # 存储原始attention输出
                        if self.current_step not in self.captured_states:
                            self.captured_states[self.current_step] = {}
                        if layer_idx not in self.captured_states[self.current_step]:
                            self.captured_states[self.current_step][layer_idx] = {}
                            
                        self.captured_states[self.current_step][layer_idx]['raw_attention_output'] = attn_hidden_states.clone().detach()
                    
                    return hook_fn
                
                # 注册到self_attn
                attn_hook = layer.self_attn.register_forward_hook(create_attention_hook(layer_idx))
                self.hooks.append(attn_hook)
                
                # 🎯 Hook 2: 捕获MLP的原始输出（新增）
                def create_mlp_hook(layer_idx):
                    def hook_fn(module, input, output):
                        # output是MLP的输出: 原始MLP输出（不加残差）
                        raw_mlp_output = output  # [batch, seq, hidden]
                        
                        if self.current_step not in self.captured_states:
                            self.captured_states[self.current_step] = {}
                        if layer_idx not in self.captured_states[self.current_step]:
                            self.captured_states[self.current_step][layer_idx] = {}
                            
                        self.captured_states[self.current_step][layer_idx]['raw_mlp_output'] = raw_mlp_output.clone().detach()
                    
                    return hook_fn
                
                # 注册到MLP模块
                mlp_hook = layer.mlp.register_forward_hook(create_mlp_hook(layer_idx))
                self.hooks.append(mlp_hook)
                
                # 🎯 Hook 3: 捕获整个层的输入
                def create_layer_input_hook(layer_idx):
                    def hook_fn(module, input):  # pre_hook只接收module和input
                        # input[0]是层的输入hidden_states
                        layer_input = input[0]  # [batch, seq, hidden]
                        
                        if self.current_step not in self.captured_states:
                            self.captured_states[self.current_step] = {}
                        if layer_idx not in self.captured_states[self.current_step]:
                            self.captured_states[self.current_step][layer_idx] = {}
                            
                        self.captured_states[self.current_step][layer_idx]['layer_input'] = layer_input.clone().detach()
                    
                    return hook_fn
                
                # 注册到整个DecoderLayer的输入
                input_hook = layer.register_forward_pre_hook(create_layer_input_hook(layer_idx))
                self.hooks.append(input_hook)
                
                # 🎯 Hook 4: 捕获MLP输入（Attention之后的状态）
                def create_mlp_input_hook(layer_idx):
                    def hook_fn(module, input):
                        # input[0]是pre_feedforward_layernorm的输入，即post_attention状态
                        mlp_input = input[0]  # [batch, seq, hidden]
                        
                        if self.current_step not in self.captured_states:
                            self.captured_states[self.current_step] = {}
                        if layer_idx not in self.captured_states[self.current_step]:
                            self.captured_states[self.current_step][layer_idx] = {}
                            
                        self.captured_states[self.current_step][layer_idx]['mlp_input_state'] = mlp_input.clone().detach()
                    
                    return hook_fn
                
                # 注册到pre_feedforward_layernorm的输入（这是post_attention状态）
                mlp_input_hook = layer.pre_feedforward_layernorm.register_forward_pre_hook(create_mlp_input_hook(layer_idx))
                self.hooks.append(mlp_input_hook)
                
                # 🎯 Hook 5: 捕获整个层的最终输出
                def create_layer_output_hook(layer_idx):
                    def hook_fn(module, input, output):
                        # output是层的输出: (hidden_states,) 或 (hidden_states, attn_weights, present_key_value)
                        if isinstance(output, tuple):
                            final_hidden_states = output[0]  # [batch, seq, hidden]
                        else:
                            final_hidden_states = output
                            
                        if self.current_step not in self.captured_states:
                            self.captured_states[self.current_step] = {}
                        if layer_idx not in self.captured_states[self.current_step]:
                            self.captured_states[self.current_step][layer_idx] = {}
                            
                        self.captured_states[self.current_step][layer_idx]['final_output'] = final_hidden_states.clone().detach()
                    
                    return hook_fn
                
                # 注册到整个DecoderLayer的输出
                output_hook = layer.register_forward_hook(create_layer_output_hook(layer_idx))
                self.hooks.append(output_hook)
                
        logger.info(f"✅ 成功注册 {len(self.hooks)} 个安全Hook")
    
    def compute_component_states(self, step: int, model) -> Dict[int, Dict[str, torch.Tensor]]:
        """
        🧮 基于Hook捕获的数据计算组件状态
        
        数学原理：
        - post_attention_state = layer_input + post_attention_layernorm(raw_attention_output)
        - post_mlp_state = final_output (已包含所有组件)
        - mlp_pure_state = post_feedforward_layernorm(raw_mlp_output) (纯MLP贡献)
        """
        if step not in self.captured_states:
            return {}
            
        step_data = self.captured_states[step]
        component_states = {}
        
        for layer_idx, layer_data in step_data.items():
            required_keys = ['raw_attention_output', 'layer_input', 'final_output', 'raw_mlp_output', 'mlp_input_state']
            if not all(key in layer_data for key in required_keys):
                logger.warning(f"⚠️ 层 {layer_idx} 数据不完整，缺少: {[k for k in required_keys if k not in layer_data]}，跳过")
                continue
                
            layer = model.language_model.model.layers[layer_idx]
            
            # 🎯 计算Post-Attention状态
            raw_attn_output = layer_data['raw_attention_output']
            layer_input = layer_data['layer_input']
            
            with torch.no_grad():
                # 应用post_attention_layernorm
                normalized_attn = layer.post_attention_layernorm(raw_attn_output)
                # 加上residual connection
                post_attention_state = layer_input + normalized_attn
            
            # 🎯 Post-MLP状态就是最终输出
            post_mlp_state = layer_data['final_output']
            
            # 🎯 计算纯MLP状态（新增）- 真实的MLP贡献
            raw_mlp_output = layer_data['raw_mlp_output']
            with torch.no_grad():
                # 应用post_feedforward_layernorm到原始MLP输出
                normalized_mlp = layer.post_feedforward_layernorm(raw_mlp_output)
                # 这是纯MLP的贡献（没有加residual，因为我们想看纯MLP的预测能力）
                mlp_pure_state = normalized_mlp
            
            component_states[layer_idx] = {
                'post_attention': post_attention_state,
                'post_mlp': post_mlp_state,
                'mlp_pure': mlp_pure_state,  # 新增：纯MLP状态
                'layer_input': layer_input,
                'raw_attention_output': raw_attn_output,
                'raw_mlp_output': raw_mlp_output,
                'mlp_input_state': layer_data['mlp_input_state']
            }
        
        return component_states
    
    def clear_hooks(self):
        """清除所有Hook"""
        for hook in self.hooks:
            hook.remove()
        self.hooks.clear()
    
    def get_captured_states(self, step: int) -> Dict[int, Dict[str, torch.Tensor]]:
        """获取指定步骤的捕获状态"""
        return self.captured_states.get(step, {})


class FineGrainedLogitLensAnalyzer:
    """细粒度Logit Lens分析器 - Attention vs MLP分析"""
    
    def __init__(self, config: FineGrainedLogitLensConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        
        # 设置随机种子
        set_seed(1234)
        
        # 初始化Hook管理器
        self.hook_manager = ComponentHookManager()
        
        # 初始化模型和处理器
        self._load_model()
        
        # 结果存储
        self.generation_history = []
        
        logger.info("细粒度Logit Lens分析器初始化完成")
    
    def _load_model(self):
        """加载PaliGemma2模型和处理器"""
        logger.info("正在加载PaliGemma2模型...")
        
        try:
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.config.model_path)
            
            # 加载模型
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)
            
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device
            )
            
            # 设置为评估模式
            self.model.eval()
            
            # 获取模型架构信息
            self.num_layers = len(self.model.language_model.model.layers)
            self.hidden_size = self.model.language_model.config.hidden_size
            self.vocab_size = self.model.language_model.config.vocab_size
            
            # 获取关键组件
            self.norm_layer = self.model.language_model.model.norm
            self.lm_head = self.model.language_model.lm_head
            
            logger.info(f"模型加载成功:")
            logger.info(f"  - 语言模型层数: {self.num_layers}")
            logger.info(f"  - 隐藏维度: {self.hidden_size}")
            logger.info(f"  - 词汇表大小: {self.vocab_size}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def analyze_fine_grained_generation(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """🔬 安全的细粒度自回归生成分析 - 核心方法"""
        logger.info("🔬 开始安全细粒度Logit Lens分析...")
        logger.info(f"📸 图像: {image_path}")
        logger.info(f"💬 提示: {prompt}")
        
        # 准备初始输入
        image = Image.open(image_path).convert('RGB')
        initial_inputs = self.processor(text=prompt, images=image, return_tensors="pt")
        
        # 移动到设备
        for key, value in initial_inputs.items():
            if torch.is_tensor(value):
                initial_inputs[key] = value.to(self.device)
        
        # 🎯 注册安全Hook系统（完全移除Monkey Patch）
        self.hook_manager.register_component_hooks(self.model, self.config.target_layers)
        
        # 存储生成历史
        self.generation_history = []
        generated_token_ids = []
        
        # 当前输入
        current_inputs = initial_inputs
        
        # 🔬 逐步生成并进行安全细粒度分析
        for step in range(self.config.max_new_tokens):
            logger.info(f"\n🔬 === Step {step}: 安全分析第{step+1}个token ===")
            
            # 设置当前步骤
            self.hook_manager.reset_for_step(step)
            
            step_start_time = time.time()
            
            with torch.no_grad():
                # 🎯 关键：使用正常的模型forward，不破坏计算流程
                step_outputs = self.model(
                    **current_inputs, 
                    output_hidden_states=False,  # 不需要，我们用Hook
                    return_dict=True
                )
                
                # 获取最终预测的下一个token
                next_token_logits = step_outputs.logits[0, -1, :]
                next_token_id = next_token_logits.argmax().item()
                
                # 检查是否结束生成
                if next_token_id == self.processor.tokenizer.eos_token_id:
                    logger.info(f"🏁 遇到EOS token，生成结束于Step {step}")
                    break
                
                # 解码下一个token
                try:
                    next_token_text = self.processor.tokenizer.decode([next_token_id])
                    logger.info(f"🎯 Step {step} 预测token: '{next_token_text}' (ID: {next_token_id})")
                except:
                    next_token_text = f"<TOKEN_{next_token_id}>"
                
                # 🔬 进行安全的细粒度组件分析
                component_analysis = self._analyze_components_step(step, current_inputs['input_ids'].shape[1])
                
                # 记录当前步信息
                step_info = {
                    'step': step,
                    'sequence_length': current_inputs['input_ids'].shape[1],
                    'predicted_token_id': next_token_id,
                    'predicted_token_text': next_token_text,
                    'generated_so_far': self.processor.tokenizer.decode(generated_token_ids, skip_special_tokens=True),
                    'component_analysis': component_analysis,
                    'final_prediction_logits': next_token_logits.cpu(),
                    'step_duration': time.time() - step_start_time
                }
                
                self.generation_history.append(step_info)
                generated_token_ids.append(next_token_id)
                
                # 准备下一步输入
                new_token_tensor = torch.tensor([[next_token_id]], device=self.device)
                updated_input_ids = torch.cat([current_inputs['input_ids'], new_token_tensor], dim=1)
                
                new_attention = torch.ones((1, 1), device=self.device, dtype=current_inputs['attention_mask'].dtype)
                updated_attention_mask = torch.cat([current_inputs['attention_mask'], new_attention], dim=1)
                
                current_inputs = {
                    'input_ids': updated_input_ids,
                    'attention_mask': updated_attention_mask,
                    'pixel_values': current_inputs['pixel_values']
                }
                
                logger.info(f"⏱️ Step {step} 耗时: {step_info['step_duration']:.3f}s")
        
        # 清除hooks
        self.hook_manager.clear_hooks()
        
        # 生成最终结果
        final_generated_text = self.processor.tokenizer.decode(generated_token_ids, skip_special_tokens=True)
        
        results = {
            'initial_prompt': prompt,
            'final_generated_text': final_generated_text,
            'generation_steps': len(self.generation_history),
            'total_tokens_generated': len(generated_token_ids),
            'generation_history': self.generation_history,
            'model_info': {
                'model_path': self.config.model_path,
                'num_layers': self.num_layers,
                'target_layers': self.config.target_layers
            }
        }
        
        logger.info(f"🎉 安全细粒度分析完成!")
        logger.info(f"   📊 分析了 {results['generation_steps']} 个生成步骤")
        logger.info(f"   📝 生成了 {results['total_tokens_generated']} 个tokens")
        logger.info(f"   📃 最终文本: '{final_generated_text}'")
        
        return results
    
    def _analyze_components_step(self, step: int, sequence_length: int) -> Dict[str, Any]:
        """分析当前步骤的组件贡献 - 使用安全Hook系统"""
        logger.info(f"🔬 Step {step}: 开始安全组件分析...")
        
        # 🎯 使用新的安全Hook系统获取组件状态
        component_states = self.hook_manager.compute_component_states(step, self.model)
        
        if not component_states:
            logger.warning(f"⚠️ Step {step}: 未获取到组件状态")
            return {}
        
        analysis_results = {
            'analyzed_layers': list(component_states.keys()),
            'sequence_length': sequence_length,
            'component_predictions': {},
            'component_comparisons': {},
            'summary': {
                'attention_dominance': 0,
                'mlp_dominance': 0,
                'balanced_layers': 0
            }
        }
        
        # 分析每一层的组件贡献
        for layer_idx, layer_states in component_states.items():
            logger.info(f"   🧩 分析层 {layer_idx} 组件...")
            
            layer_analysis = {}
            
            # 🎯 分析Post-Attention状态
            if 'post_attention' in layer_states and self.config.analyze_attention:
                post_attn_state = layer_states['post_attention']
                attn_predictions = self._analyze_hidden_state_predictions(
                    post_attn_state, layer_idx, 'attention'
                )
                layer_analysis['attention'] = attn_predictions
            
            # 🎯 分析Post-MLP状态（完整状态）
            if 'post_mlp' in layer_states and self.config.analyze_mlp:
                post_mlp_state = layer_states['post_mlp']
                full_predictions = self._analyze_hidden_state_predictions(
                    post_mlp_state, layer_idx, 'full'
                )
                layer_analysis['full'] = full_predictions
                
            # 🎯 分析纯MLP状态（真实MLP贡献）- 新增
            if 'mlp_pure' in layer_states and self.config.analyze_mlp:
                mlp_pure_state = layer_states['mlp_pure']
                mlp_pure_predictions = self._analyze_hidden_state_predictions(
                    mlp_pure_state, layer_idx, 'mlp_pure'
                )
                layer_analysis['mlp_pure'] = mlp_pure_predictions
            
            # 🎯 组件对比分析
            if 'attention' in layer_analysis and 'full' in layer_analysis:
                comparison = self._compare_component_predictions(
                    layer_analysis['attention'], 
                    layer_analysis['full']
                )
                layer_analysis['comparison'] = comparison
                
                # 更新统计
                if comparison['attention_stronger']:
                    analysis_results['summary']['attention_dominance'] += 1
                elif comparison['mlp_stronger']:
                    analysis_results['summary']['mlp_dominance'] += 1
                else:
                    analysis_results['summary']['balanced_layers'] += 1
            
            # 🎯 全组件对比分析（新增）
            if len(layer_analysis) >= 2:  # 至少有两个组件
                all_comparison = self._compare_all_components(layer_analysis)
                layer_analysis['all_comparison'] = all_comparison
            
            analysis_results['component_predictions'][layer_idx] = layer_analysis
        
        logger.info(f"✅ Step {step}: 安全组件分析完成")
        logger.info(f"   📊 统计: Attn优势={analysis_results['summary']['attention_dominance']}, "
                   f"MLP优势={analysis_results['summary']['mlp_dominance']}, "
                   f"平衡={analysis_results['summary']['balanced_layers']}")
        
        return analysis_results
    
    def _analyze_hidden_state_predictions(self, hidden_state: torch.Tensor, 
                                          layer_idx: int, component_type: str) -> Dict[str, Any]:
        """分析隐藏状态的预测能力"""
        # 只分析最后位置（预测下一个token的位置）
        last_position_hidden = hidden_state[0, -1, :]  # [hidden_size]
        
        # 应用norm层和lm_head
        normalized_hidden = self.norm_layer(last_position_hidden.unsqueeze(0)).squeeze(0)
        logits = self.lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)
        probs = F.softmax(logits, dim=-1)
        
        # 获取top-k预测
        top_probs, top_indices = torch.topk(probs, self.config.top_k_tokens)
        
        predictions = []
        for i in range(self.config.top_k_tokens):
            token_id = top_indices[i].item()
            prob = top_probs[i].item()
            
            try:
                token_text = self.processor.tokenizer.decode([token_id])
                clean_token = token_text.replace('\n', '\\n').replace('\r', '\\r').strip()[:20]
            except:
                clean_token = f"<TOKEN_{token_id}>"
            
            predictions.append({
                'rank': i + 1,
                'token_id': token_id,
                'token_text': clean_token,
                'probability': prob,
                'logit_value': logits[token_id].item()
            })
        
        return {
            'component_type': component_type,
            'layer_idx': layer_idx,
            'predictions': predictions,
            'hidden_stats': {
                'mean': last_position_hidden.mean().item(),
                'std': last_position_hidden.std().item(),
                'norm': last_position_hidden.norm().item()
            },
            'entropy': -torch.sum(probs * torch.log(probs + 1e-10)).item()
        }
    
    def _compare_component_predictions(self, attention_analysis: Dict, full_analysis: Dict) -> Dict[str, Any]:
        """比较不同组件的预测"""
        attn_top1_prob = attention_analysis['predictions'][0]['probability']
        full_top1_prob = full_analysis['predictions'][0]['probability']
        
        attn_entropy = attention_analysis['entropy']
        full_entropy = full_analysis['entropy']
        
        # 判断哪个组件贡献更大
        prob_improvement = full_top1_prob - attn_top1_prob
        entropy_change = full_entropy - attn_entropy
        
        return {
            'attention_top1_prob': attn_top1_prob,
            'full_top1_prob': full_top1_prob,
            'probability_improvement': prob_improvement,
            'attention_entropy': attn_entropy,
            'full_entropy': full_entropy,
            'entropy_change': entropy_change,
            'attention_stronger': prob_improvement < 0.01 and entropy_change > -0.1,  # attention已经很好
            'mlp_stronger': prob_improvement > 0.05,  # MLP显著改善
            'quality_score': full_top1_prob / (attn_entropy + 1e-6)  # 综合质量分数
        }
    
    def _compare_all_components(self, layer_analysis: Dict) -> Dict[str, Any]:
        """比较所有三个组件的预测 - 新增方法"""
        comparison = {}
        
        # 获取各组件的top1概率和熵
        if 'attention' in layer_analysis:
            attn_data = layer_analysis['attention']
            comparison['attention_top1_prob'] = attn_data['predictions'][0]['probability']
            comparison['attention_entropy'] = attn_data['entropy']
        
        if 'full' in layer_analysis:
            full_data = layer_analysis['full']
            comparison['full_top1_prob'] = full_data['predictions'][0]['probability']
            comparison['full_entropy'] = full_data['entropy']
            
        if 'mlp_pure' in layer_analysis:
            mlp_data = layer_analysis['mlp_pure']
            comparison['mlp_pure_top1_prob'] = mlp_data['predictions'][0]['probability']
            comparison['mlp_pure_entropy'] = mlp_data['entropy']
            
        # 计算改善度
        if 'attention_top1_prob' in comparison and 'full_top1_prob' in comparison:
            comparison['full_vs_attention_improvement'] = comparison['full_top1_prob'] - comparison['attention_top1_prob']
            
        if 'attention_top1_prob' in comparison and 'mlp_pure_top1_prob' in comparison:
            comparison['mlp_vs_attention_improvement'] = comparison['mlp_pure_top1_prob'] - comparison['attention_top1_prob']
            
        # 确定主导组件
        probs = []
        if 'attention_top1_prob' in comparison:
            probs.append(('attention', comparison['attention_top1_prob']))
        if 'full_top1_prob' in comparison:
            probs.append(('full', comparison['full_top1_prob']))
        if 'mlp_pure_top1_prob' in comparison:
            probs.append(('mlp_pure', comparison['mlp_pure_top1_prob']))
            
        if probs:
            dominant_component = max(probs, key=lambda x: x[1])
            comparison['dominant_component'] = dominant_component[0]
            comparison['dominant_probability'] = dominant_component[1]
            
        return comparison

    def create_fine_grained_visualization(self, results: Dict[str, Any], save_path: str, image_path: str):
        """创建细粒度分析的可视化"""
        if not self.config.enable_visualization:
            return
            
        logger.info("🎨 创建细粒度分析可视化...")
        
        # 1. 创建组件对比图
        self._create_component_comparison_plot(results, save_path)
        
        # 2. 创建演化趋势图
        self._create_evolution_trends_plot(results, save_path)
        
        # 3. 创建交互式HTML
        if self.config.create_interactive_html:
            self._create_interactive_fine_grained_html(results, save_path, image_path)
    
    def _create_component_comparison_plot(self, results: Dict[str, Any], save_path: str):
        """创建组件对比图 - Attention vs MLP vs Full (真实三测量值)"""
        fig, axes = plt.subplots(2, 2, figsize=(18, 14))
        
        # 准备数据
        steps = [step_info['step'] for step_info in results['generation_history']]
        layers = self.config.target_layers
        
        # 1. 三组件置信度对比 - 真实测量
        ax1 = axes[0, 0]
        for layer_idx in layers:
            attn_confidences = []
            full_confidences = []
            mlp_pure_confidences = []  # 新增：真实MLP测量
            
            for step_info in results['generation_history']:
                comp_analysis = step_info.get('component_analysis', {})
                layer_data = comp_analysis.get('component_predictions', {}).get(layer_idx, {})
                
                # Attention置信度
                attn_data = layer_data.get('attention', {})
                if attn_data and attn_data.get('predictions'):
                    attn_confidences.append(attn_data['predictions'][0]['probability'])
                else:
                    attn_confidences.append(0)
                
                # Full置信度
                full_data = layer_data.get('full', {})
                if full_data and full_data.get('predictions'):
                    full_confidences.append(full_data['predictions'][0]['probability'])
                else:
                    full_confidences.append(0)
                
                # 真实MLP Pure置信度
                mlp_pure_data = layer_data.get('mlp_pure', {})
                if mlp_pure_data and mlp_pure_data.get('predictions'):
                    mlp_pure_confidences.append(mlp_pure_data['predictions'][0]['probability'])
                else:
                    mlp_pure_confidences.append(0)
            
            # 绘制三条线
            if any(c > 0 for c in attn_confidences):
                ax1.plot(steps, attn_confidences, '--', alpha=0.7, label=f'Attention L{layer_idx}', color='blue')
            if any(c > 0 for c in full_confidences):
                ax1.plot(steps, full_confidences, '-', label=f'Full L{layer_idx}', color='green')
            if any(c > 0 for c in mlp_pure_confidences):
                ax1.plot(steps, mlp_pure_confidences, ':', alpha=0.8, label=f'MLP Pure L{layer_idx}', color='orange')
        
        ax1.set_xlabel('Generation Step')
        ax1.set_ylabel('Top Prediction Confidence')
        ax1.set_title('Three Real Components: Attention vs Full vs MLP Pure')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        
        # 2. 真实MLP vs Attention对比
        ax2 = axes[0, 1]
        for layer_idx in layers:
            mlp_vs_attn_diffs = []
            
            for step_info in results['generation_history']:
                comp_analysis = step_info.get('component_analysis', {})
                layer_data = comp_analysis.get('component_predictions', {}).get(layer_idx, {})
                all_comparison = layer_data.get('all_comparison', {})
                
                if all_comparison and 'mlp_vs_attention_improvement' in all_comparison:
                    mlp_vs_attn_diffs.append(all_comparison['mlp_vs_attention_improvement'])
                else:
                    mlp_vs_attn_diffs.append(0)
            
            if any(c != 0 for c in mlp_vs_attn_diffs):
                ax2.plot(steps, mlp_vs_attn_diffs, marker='o', label=f'Layer {layer_idx}')
        
        ax2.set_xlabel('Generation Step')
        ax2.set_ylabel('MLP Pure vs Attention Improvement')
        ax2.set_title('Real MLP Pure vs Attention Performance')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        
        # 3. 组件主导性分析
        ax3 = axes[1, 0]
        component_dominance = {
            'attention': [],
            'mlp_pure': [],
            'full': []
        }
        
        for step_info in results['generation_history']:
            comp_analysis = step_info.get('component_analysis', {})
            step_dominance = {'attention': 0, 'mlp_pure': 0, 'full': 0}
            
            for layer_idx, layer_data in comp_analysis.get('component_predictions', {}).items():
                all_comparison = layer_data.get('all_comparison', {})
                if all_comparison and 'dominant_component' in all_comparison:
                    dominant = all_comparison['dominant_component']
                    if dominant in step_dominance:
                        step_dominance[dominant] += 1
            
            component_dominance['attention'].append(step_dominance['attention'])
            component_dominance['mlp_pure'].append(step_dominance['mlp_pure'])
            component_dominance['full'].append(step_dominance['full'])
        
        ax3.bar(steps, component_dominance['attention'], alpha=0.7, label='Attention Dominant', color='blue')
        ax3.bar(steps, component_dominance['mlp_pure'], 
                bottom=component_dominance['attention'], 
                alpha=0.7, label='MLP Pure Dominant', color='orange')
        ax3.bar(steps, component_dominance['full'], 
                bottom=[a+m for a,m in zip(component_dominance['attention'], component_dominance['mlp_pure'])], 
                alpha=0.7, label='Full Dominant', color='green')
        
        ax3.set_xlabel('Generation Step')
        ax3.set_ylabel('Number of Layers')
        ax3.set_title('Real Component Dominance by Step')
        ax3.legend()
        
        # 4. 熵分析对比
        ax4 = axes[1, 1]
        for layer_idx in layers:
            attn_entropies = []
            mlp_pure_entropies = []
            full_entropies = []
            
            for step_info in results['generation_history']:
                comp_analysis = step_info.get('component_analysis', {})
                layer_data = comp_analysis.get('component_predictions', {}).get(layer_idx, {})
                all_comparison = layer_data.get('all_comparison', {})
                
                attn_entropy = all_comparison.get('attention_entropy', 0)
                mlp_entropy = all_comparison.get('mlp_pure_entropy', 0)
                full_entropy = all_comparison.get('full_entropy', 0)
                
                attn_entropies.append(attn_entropy)
                mlp_pure_entropies.append(mlp_entropy)
                full_entropies.append(full_entropy)
            
            if any(e > 0 for e in attn_entropies):
                ax4.plot(steps, attn_entropies, '--', alpha=0.7, label=f'Attention L{layer_idx}', color='blue')
            if any(e > 0 for e in mlp_pure_entropies):
                ax4.plot(steps, mlp_pure_entropies, ':', alpha=0.8, label=f'MLP Pure L{layer_idx}', color='orange')
            if any(e > 0 for e in full_entropies):
                ax4.plot(steps, full_entropies, '-', alpha=0.7, label=f'Full L{layer_idx}', color='green')
        
        ax4.set_xlabel('Generation Step')
        ax4.set_ylabel('Entropy')
        ax4.set_title('Information Content Comparison')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        comparison_path = save_path.replace('.png', '_real_three_components.png')
        plt.savefig(comparison_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"✅ 真实三组件对比图保存: {comparison_path}")
    
    def _create_evolution_trends_plot(self, results: Dict[str, Any], save_path: str):
        """创建演化趋势图"""
        fig, ax = plt.subplots(figsize=(14, 8))
        
        steps = [step_info['step'] for step_info in results['generation_history']]
        
        # 计算每步的平均改善
        avg_improvements = []
        max_improvements = []
        min_improvements = []
        
        for step_info in results['generation_history']:
            comp_analysis = step_info.get('component_analysis', {})
            improvements = []
            
            for layer_idx, layer_data in comp_analysis.get('component_predictions', {}).items():
                comparison = layer_data.get('comparison', {})
                if comparison:
                    improvements.append(comparison.get('probability_improvement', 0))
            
            if improvements:
                avg_improvements.append(np.mean(improvements))
                max_improvements.append(np.max(improvements))
                min_improvements.append(np.min(improvements))
            else:
                avg_improvements.append(0)
                max_improvements.append(0)
                min_improvements.append(0)
        
        # 绘制趋势
        ax.fill_between(steps, min_improvements, max_improvements, alpha=0.3, label='Min-Max Range')
        ax.plot(steps, avg_improvements, marker='o', linewidth=2, label='Average Improvement')
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        ax.set_xlabel('Generation Step')
        ax.set_ylabel('MLP Probability Improvement')
        ax.set_title('MLP Contribution Trends Across Generation')
        ax.legend()
        ax.grid(True, alpha=0.3)
        
        # 添加生成的token标注
        tokens = [step_info['predicted_token_text'] for step_info in results['generation_history']]
        for i, (step, token) in enumerate(zip(steps, tokens)):
            if i % 2 == 0:  # 只标注部分token避免拥挤
                ax.annotate(f"'{token}'", (step, avg_improvements[i]), 
                           textcoords="offset points", xytext=(0,10), ha='center', fontsize=8)
        
        plt.tight_layout()
        trends_path = save_path.replace('.png', '_evolution_trends.png')
        plt.savefig(trends_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"✅ 演化趋势图保存: {trends_path}")
    
    def _create_interactive_fine_grained_html(self, results: Dict[str, Any], save_path: str, image_path: str):
        """创建交互式细粒度分析HTML"""
        logger.info("🌐 创建交互式细粒度分析HTML...")
        
        # 编码图像
        import base64
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
        except Exception as e:
            logger.warning(f"图像编码失败: {e}")
            image_base64 = ""
        
        # 序列化数据
        serializable_data = self._make_serializable(results['generation_history'])
        
        # 构建HTML
        base_dir = os.path.dirname(save_path)
        html_filename = f"fine_grained_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        html_save_path = os.path.join(base_dir, html_filename)
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma2 真实三组件Logit Lens分析 - Attention vs MLP Pure vs Full</title>
    <style>
        body {{ margin: 0; padding: 20px; font-family: Arial, sans-serif; background: #f5f5f5; }}
        .container {{ max-width: 1800px; margin: 0 auto; }}
        .header {{ text-align: center; background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .main-content {{ display: flex; gap: 20px; }}
        .left-panel {{ flex: 0 0 400px; background: white; padding: 20px; border-radius: 10px; }}
        .right-panel {{ flex: 1; background: white; padding: 20px; border-radius: 10px; }}
        
        .step-controls {{ margin: 20px 0; }}
        .step-slider {{ width: 100%; }}
        .component-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
        .component-table th, .component-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        .component-table th {{ background: #f2f2f2; }}
        
        .attention-cell {{ background-color: #e3f2fd; }}
        .mlp-pure-cell {{ background-color: #fff9c4; }}
        .full-cell {{ background-color: #e8f5e8; }}
        .improvement-positive {{ color: #4caf50; font-weight: bold; }}
        .improvement-negative {{ color: #f44336; font-weight: bold; }}
        
        .stats-panel {{ background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 15px 0; }}
        .comparison-chart {{ margin: 20px 0; }}
        
        .real-measurement {{ font-weight: bold; color: #2e7d32; }}
        .calculated-value {{ font-style: italic; color: #666; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔬 PaliGemma2 真实三组件Logit Lens分析</h1>
            <h2>✅ 真实测量：Attention vs MLP Pure vs Full</h2>
            <p><strong>生成文本:</strong> "{results['final_generated_text']}"</p>
            <p><em>📊 现在显示三个真实的lm_head测量值，而非减法计算</em></p>
        </div>
        
        <div class="main-content">
            <div class="left-panel">
                <div class="image-container">
                    <h3>📸 源图像</h3>
                    {"<img src='data:image/jpeg;base64," + image_base64 + "' style='max-width: 100%; border-radius: 8px;'>" if image_base64 else "<p>图像不可用</p>"}
                </div>
                
                <div class="step-controls">
                    <h3>🎮 步骤控制</h3>
                    <label for="stepSlider">生成步骤:</label>
                    <input type="range" id="stepSlider" class="step-slider" 
                           min="0" max="{len(serializable_data)-1}" value="0" step="1">
                    <div id="stepInfo">Step 0</div>
                </div>
                
                <div class="stats-panel" id="statsPanel">
                    <h3>📊 当前步骤统计</h3>
                    <div id="currentStats">选择步骤查看统计...</div>
                </div>
            </div>
            
            <div class="right-panel">
                <div id="componentAnalysis">
                    <h3>🔬 真实三组件分析详情</h3>
                    <div id="analysisContent">请选择生成步骤...</div>
                </div>
            </div>
        </div>
    </div>

<script>
    const generationData = {json.dumps(serializable_data, indent=2)};
    let currentStep = 0;
    
    const stepSlider = document.getElementById('stepSlider');
    const stepInfo = document.getElementById('stepInfo');
    const statsPanel = document.getElementById('currentStats');
    const analysisContent = document.getElementById('analysisContent');
    
    stepSlider.addEventListener('input', function() {{
        currentStep = parseInt(this.value);
        updateDisplay();
    }});
    
    function updateDisplay() {{
        if (currentStep >= generationData.length) return;
        
        const stepData = generationData[currentStep];
        const compAnalysis = stepData.component_analysis || {{}};
        
        // 更新步骤信息
        stepInfo.textContent = `Step ${{stepData.step}}: Predicting "${{stepData.predicted_token_text}}"`;
        
        // 更新统计信息
        const predictions = compAnalysis.component_predictions || {{}};
        let totalLayers = Object.keys(predictions).length;
        let attnDominant = 0, mlpDominant = 0, fullDominant = 0;
        
        Object.values(predictions).forEach(layer => {{
            const allComp = layer.all_comparison || {{}};
            const dominant = allComp.dominant_component;
            if (dominant === 'attention') attnDominant++;
            else if (dominant === 'mlp_pure') mlpDominant++;
            else if (dominant === 'full') fullDominant++;
        }});
        
        statsPanel.innerHTML = `
            <p><strong>序列长度:</strong> ${{stepData.sequence_length}}</p>
            <p><strong>预测Token:</strong> "${{stepData.predicted_token_text}}"</p>
            <p><strong>已生成:</strong> "${{stepData.generated_so_far}}"</p>
            <p><strong>分析层数:</strong> ${{totalLayers}}</p>
            <p><strong>Attention主导:</strong> ${{attnDominant}} 层</p>
            <p><strong>MLP Pure主导:</strong> ${{mlpDominant}} 层</p>
            <p><strong>Full主导:</strong> ${{fullDominant}} 层</p>
        `;
        
        // 更新详细分析
        updateDetailedAnalysis(compAnalysis);
    }}
    
    function updateDetailedAnalysis(compAnalysis) {{
        const predictions = compAnalysis.component_predictions || {{}};
        
        let html = '<table class="component-table">';
        html += '<thead><tr>';
        html += '<th>Layer</th><th>Component</th><th>Top Token</th><th>Confidence</th><th>Entropy</th><th>vs Attention</th>';
        html += '</tr></thead><tbody>';
        
        for (const [layerIdx, layerData] of Object.entries(predictions)) {{
            const allComp = layerData.all_comparison || {{}};
            
            // Attention行 (基线)
            if (layerData.attention) {{
                const attn = layerData.attention;
                const topPred = attn.predictions[0];
                html += `<tr class="attention-cell">
                    <td rowspan="3">Layer ${{layerIdx}}</td>
                    <td><span class="real-measurement">Attention</span></td>
                    <td>"${{topPred.token_text}}"</td>
                    <td>${{(topPred.probability * 100).toFixed(2)}}%</td>
                    <td>${{attn.entropy.toFixed(3)}}</td>
                    <td>-</td>
                </tr>`;
            }}
            
            // MLP Pure行 (真实测量)
            if (layerData.mlp_pure) {{
                const mlp = layerData.mlp_pure;
                const topPred = mlp.predictions[0];
                const improvement = allComp.mlp_vs_attention_improvement || 0;
                const improvementClass = improvement > 0 ? 'improvement-positive' : 'improvement-negative';
                
                html += `<tr class="mlp-pure-cell">
                    <td><span class="real-measurement">MLP Pure</span></td>
                    <td>"${{topPred.token_text}}"</td>
                    <td>${{(topPred.probability * 100).toFixed(2)}}%</td>
                    <td>${{mlp.entropy.toFixed(3)}}</td>
                    <td class="${{improvementClass}}">${{(improvement * 100).toFixed(2)}}%</td>
                </tr>`;
            }}
            
            // Full行 (真实测量)
            if (layerData.full) {{
                const full = layerData.full;
                const topPred = full.predictions[0];
                const improvement = allComp.full_vs_attention_improvement || 0;
                const improvementClass = improvement > 0 ? 'improvement-positive' : 'improvement-negative';
                
                html += `<tr class="full-cell">
                    <td><span class="real-measurement">Full (Attn+MLP)</span></td>
                    <td>"${{topPred.token_text}}"</td>
                    <td>${{(topPred.probability * 100).toFixed(2)}}%</td>
                    <td>${{full.entropy.toFixed(3)}}</td>
                    <td class="${{improvementClass}}">${{(improvement * 100).toFixed(2)}}%</td>
                </tr>`;
            }}
        }}
        
        html += '</tbody></table>';
        
        // 添加说明
        html += `
        <div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 8px;">
            <h4>📊 测量说明</h4>
            <p><span class="real-measurement">●</span> 所有数值都是真实的lm_head测量结果</p>
            <p><strong>Attention:</strong> post_attention_state 的预测能力</p>
            <p><strong>MLP Pure:</strong> 纯MLP输出（无残差）的预测能力</p>
            <p><strong>Full:</strong> 完整层输出的预测能力</p>
            <p><strong>vs Attention:</strong> 相对于Attention基线的改善</p>
        </div>
        `;
        
        analysisContent.innerHTML = html;
    }}
    
    // 初始化显示
    updateDisplay();
</script>
</body>
</html>
        """
        
        # 保存HTML
        with open(html_save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"✅ 交互式细粒度分析HTML保存: {html_save_path}")
        return html_save_path
    
    def save_results(self, results: Dict[str, Any], sample_id: str):
        """保存分析结果"""
        if self.config.save_component_details:
            # 保存JSON格式
            json_path = os.path.join(self.config.output_dir, f"fine_grained_results_{sample_id}.json")
            serializable_results = self._make_serializable(results)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            # 保存pickle格式
            pickle_path = os.path.join(self.config.output_dir, f"fine_grained_results_{sample_id}.pkl")
            with open(pickle_path, 'wb') as f:
                pickle.dump(results, f)
            
            logger.info(f"📁 细粒度分析结果已保存: {json_path}, {pickle_path}")
    
    def _make_serializable(self, obj):
        """将包含tensor的对象转换为可序列化格式"""
        if isinstance(obj, torch.Tensor):
            return obj.cpu().tolist()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self._make_serializable(item) for item in obj)
        else:
            return obj


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PaliGemma2 Fine-Grained Logit Lens Analysis")
    
    parser.add_argument("--model_path", type=str, default="google/paligemma2-3b-pt-224", 
                       help="PaliGemma2模型路径")
    parser.add_argument("--device", type=str, default="cuda:0" if torch.cuda.is_available() else "cpu", 
                       help="设备")
    parser.add_argument("--dtype", type=str, default="bf16", help="数据类型")
    parser.add_argument("--image_path", type=str, 
                       default='/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images/real_image_20250610_170727.png', 
                       help="输入图像路径")
    parser.add_argument("--prompt", type=str, default="describe the image", help="提示词")
    parser.add_argument("--output_dir", type=str, default="/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/pro/paligemma2_fine_grained_results", 
                       help="输出目录")
    parser.add_argument("--max_new_tokens", type=int, default=15, help="最大生成token数")
    parser.add_argument("--target_layers", nargs='+', type=int, default=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26], 
                       help="要分析的目标层")
    
    args = parser.parse_args()
    
    # 创建配置
    config = FineGrainedLogitLensConfig(
        model_path=args.model_path,
        device=args.device,
        dtype=args.dtype,
        output_dir=args.output_dir,
        max_new_tokens=args.max_new_tokens,
        target_layers=args.target_layers
    )
    
    # 创建分析器
    analyzer = FineGrainedLogitLensAnalyzer(config)
    
    # 运行细粒度分析
    results = analyzer.analyze_fine_grained_generation(args.image_path, args.prompt)
    
    # 创建可视化
    if results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        viz_path = os.path.join(args.output_dir, f"fine_grained_visualization_{timestamp}.png")
        analyzer.create_fine_grained_visualization(results, viz_path, args.image_path)
        
        # 保存结果
        analyzer.save_results(results, timestamp)
        
        logger.info("🎉 === 细粒度Logit Lens分析完成 ===")
        logger.info(f"📁 结果保存在: {args.output_dir}")
        logger.info(f"🌐 查看交互式结果: 打开生成的HTML文件")
        logger.info(f"🔬 分析维度: Attention vs MLP组件对比")


if __name__ == "__main__":
    main() 