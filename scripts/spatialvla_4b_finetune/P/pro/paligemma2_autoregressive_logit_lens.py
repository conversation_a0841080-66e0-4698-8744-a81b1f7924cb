#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Autoregressive Logit Lens Analysis - 全位置动态分析版
========================================================================

真正的动态生成分析，支持所有位置的演化追踪：
- Vision Patches (256个位置)：观察视觉理解如何随生成演化
- Prompt Tokens：观察提示理解如何随上下文变化  
- Generated Tokens：观察新生成内容如何影响整体理解

### 核心改进：从单位置到全位置分析

#### 🚫 原始版本（只分析最后位置）：
```
last_position_hidden = layer_hidden[0, -1, :]  # 只看最后位置
# 只知道"预测下一个token"的能力
```

#### ✅ 改进版本（分析所有位置）：
```
for pos in range(sequence_length):
    position_hidden = layer_hidden[0, pos, :]  # 分析每个位置
    # 了解每个vision patch和token在当前上下文中的"理解"
```

### 新增功能

1. **全位置类型分类**：vision_patch | prompt_token | generated_token
2. **动态演化追踪**：同一位置在不同步骤的表示变化
3. **Yuan风格大表格**：支持Position×Layer×Step的三维可视化
4. **交互式导航**：在位置、层级、步骤间自由浏览

作者: Assistant (基于原始自回归代码改进)
日期: 2025-01-14  
版本: 全位置动态分析版
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn.functional as F
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any, Generator
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
from PIL import Image
from tqdm import tqdm
import argparse
import logging
from collections import defaultdict
import pickle
import copy
import random
import cv2
import time

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入transformers
try:
    from transformers import (
        AutoProcessor, AutoModelForImageTextToText, 
        PaliGemmaForConditionalGeneration,
        set_seed
    )
    logger.info("成功导入Transformers组件。")
except ImportError as e:
    logger.error(f"导入Transformers失败: {e}")
    sys.exit(1)

import warnings
warnings.filterwarnings("ignore")


@dataclass
class AutoregressiveLogitLensConfig:
    """自回归Logit Lens分析配置 - 增强版"""
    
    # 模型配置
    model_path: str = "google/paligemma2-3b-pt-224"
    device: str = "cuda:0" if torch.cuda.is_available() else "cpu"
    dtype: str = "bf16"
    
    # 生成配置
    max_new_tokens: int = 20  # 生成token数量（每步都分析）
    do_sample: bool = False   # 确定性生成便于分析
    temperature: float = 1.0
    
    # 分析配置 - 全位置支持
    target_layers: List[int] = field(default_factory=lambda: [0, 5, 10, 15, 20, 25])  # 关键层
    analyze_all_layers: bool = False  # 是否分析所有层（计算开销大）
    top_k_tokens: int = 5  # 每层显示的top预测数
    
    # 新增：位置分析配置
    analyze_all_positions: bool = True   # 是否分析所有位置（包括vision patches）
    vision_patch_sample_rate: float = 1.0  # vision patch采样率（1.0=全部，0.1=10%）
    text_positions_limit: int = 20  # 最大分析的text位置数
    
    # 输出配置
    output_dir: str = "./paligemma2_autoregressive_results"
    save_step_details: bool = True  # 保存每步详细信息
    
    # 可视化配置
    enable_visualization: bool = True
    create_gif: bool = True  # 创建动态GIF
    create_interactive_html: bool = True


class AutoregressiveLogitLensAnalyzer:
    """自回归Logit Lens分析器 - 追踪生成过程中的表示演化"""
    
    def __init__(self, config: AutoregressiveLogitLensConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        
        # 设置随机种子
        set_seed(1234)
        
        # 初始化模型和处理器
        self._load_model()
        
        # 结果存储
        self.generation_history = []  # 存储每步的完整信息
        
        logger.info("自回归Logit Lens分析器初始化完成")
    
    def _load_model(self):
        """加载PaliGemma2模型和处理器"""
        logger.info("正在加载PaliGemma2模型...")
        
        try:
            # 加载处理器
            self.processor = AutoProcessor.from_pretrained(self.config.model_path)
            
            # 加载模型
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)
            
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device
            )
            
            # 设置为评估模式
            self.model.eval()
            
            # 获取模型架构信息
            self.num_layers = len(self.model.language_model.model.layers)
            self.hidden_size = self.model.language_model.config.hidden_size
            self.vocab_size = self.model.language_model.config.vocab_size
            
            # 获取关键组件
            self.norm_layer = self.model.language_model.model.norm
            self.lm_head = self.model.language_model.lm_head
            
            logger.info(f"模型加载成功:")
            logger.info(f"  - 语言模型层数: {self.num_layers}")
            logger.info(f"  - 隐藏维度: {self.hidden_size}")
            logger.info(f"  - 词汇表大小: {self.vocab_size}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def analyze_autoregressive_generation(self, image_path: str, prompt: str) -> Dict[str, Any]:
        """🔄 分析自回归生成过程中的Logit Lens变化 - 核心方法"""
        logger.info("🔄 开始自回归Logit Lens分析...")
        logger.info(f"📸 图像: {image_path}")
        logger.info(f"💬 提示: {prompt}")
        
        # 准备初始输入
        image = Image.open(image_path).convert('RGB')
        initial_inputs = self.processor(text=prompt, images=image, return_tensors="pt")
        
        # 移动到设备
        for key, value in initial_inputs.items():
            if torch.is_tensor(value):
                initial_inputs[key] = value.to(self.device)
        
        # 获取初始序列长度
        initial_seq_len = initial_inputs['input_ids'].shape[1]
        logger.info(f"📏 初始序列长度: {initial_seq_len}")
        
        # 存储生成历史
        self.generation_history = []
        generated_token_ids = []
        
        # 当前输入（会在每步更新）
        current_inputs = initial_inputs
        
        # 🔄 逐步生成并分析
        for step in range(self.config.max_new_tokens):
            logger.info(f"\n🔄 === Step {step}: 预测第{step+1}个新token ===")
            
            step_start_time = time.time()
            
            with torch.no_grad():
                # 获取当前步的输出和隐藏状态
                step_outputs = self.model(
                    **current_inputs, 
                    output_hidden_states=True, 
                    return_dict=True
                )
                
                # 分析当前步的隐藏状态
                step_analysis = self._analyze_step_hidden_states(
                    hidden_states=step_outputs.language_model_outputs.hidden_states if hasattr(step_outputs, 'language_model_outputs') else step_outputs.hidden_states,
                    step=step,
                    current_sequence_length=current_inputs['input_ids'].shape[1]
                )
                
                # 获取最终预测的下一个token
                next_token_logits = step_outputs.logits[0, -1, :]  # 最后一个位置的logits
                next_token_id = next_token_logits.argmax().item()
                
                # 检查是否结束生成
                if next_token_id == self.processor.tokenizer.eos_token_id:
                    logger.info(f"🏁 遇到EOS token，生成结束于Step {step}")
                    break
                
                # 解码下一个token
                try:
                    next_token_text = self.processor.tokenizer.decode([next_token_id])
                    logger.info(f"🎯 Step {step} 预测token: '{next_token_text}' (ID: {next_token_id})")
                except:
                    next_token_text = f"<TOKEN_{next_token_id}>"
                    logger.warning(f"⚠️ token解码失败: {next_token_id}")
                
                # 记录当前步的完整信息
                step_info = {
                    'step': step,
                    'sequence_length': current_inputs['input_ids'].shape[1],
                    'predicted_token_id': next_token_id,
                    'predicted_token_text': next_token_text,
                    'generated_so_far': self.processor.tokenizer.decode(generated_token_ids, skip_special_tokens=True),
                    'logit_lens_analysis': step_analysis,
                    'final_prediction_logits': next_token_logits.cpu(),
                    'step_duration': time.time() - step_start_time
                }
                
                self.generation_history.append(step_info)
                
                # 更新生成的token列表
                generated_token_ids.append(next_token_id)
                
                # 🔄 准备下一步的输入：当前输入 + 新生成的token
                new_token_tensor = torch.tensor([[next_token_id]], device=self.device)
                updated_input_ids = torch.cat([current_inputs['input_ids'], new_token_tensor], dim=1)
                
                # 更新attention_mask
                new_attention = torch.ones((1, 1), device=self.device, dtype=current_inputs['attention_mask'].dtype)
                updated_attention_mask = torch.cat([current_inputs['attention_mask'], new_attention], dim=1)
                
                # 构建新的inputs（注意：pixel_values保持不变）
                current_inputs = {
                    'input_ids': updated_input_ids,
                    'attention_mask': updated_attention_mask,
                    'pixel_values': current_inputs['pixel_values']  # 图像信息保持不变
                }
                
                logger.info(f"⏱️ Step {step} 耗时: {step_info['step_duration']:.3f}s")
                logger.info(f"📝 已生成文本: '{step_info['generated_so_far']}'")
        
        # 生成最终结果
        final_generated_text = self.processor.tokenizer.decode(generated_token_ids, skip_special_tokens=True)
        
        results = {
            'initial_prompt': prompt,
            'final_generated_text': final_generated_text,
            'generation_steps': len(self.generation_history),
            'total_tokens_generated': len(generated_token_ids),
            'generation_history': self.generation_history,
            'initial_sequence_length': initial_seq_len,
            'final_sequence_length': current_inputs['input_ids'].shape[1],
            'model_info': {
                'model_path': self.config.model_path,
                'num_layers': self.num_layers,
                'target_layers': self.config.target_layers
            }
        }
        
        logger.info(f"🎉 自回归分析完成!")
        logger.info(f"   📊 分析了 {results['generation_steps']} 个生成步骤")
        logger.info(f"   📝 生成了 {results['total_tokens_generated']} 个tokens")
        logger.info(f"   📃 最终文本: '{final_generated_text}'")
        
        return results
    
    def _analyze_step_hidden_states(self, hidden_states: Tuple[torch.Tensor], 
                                    step: int, 
                                    current_sequence_length: int) -> Dict[str, Any]:
        """分析当前步骤中各层的隐藏状态和预测 - 增强版支持全位置分析"""
        logger.info(f"🔍 Step {step}: 开始分析隐藏状态...")
        logger.info(f"   📏 当前序列长度: {current_sequence_length}")
        logger.info(f"   🧠 模型层数: {len(hidden_states)}")
        
        # 确定要分析的层
        layers_to_analyze = self.config.target_layers if not self.config.analyze_all_layers else list(range(len(hidden_states)))
        
        # 确定要分析的位置
        positions_to_analyze = self._determine_positions_to_analyze(current_sequence_length, step)
        logger.info(f"   📍 分析位置数: {len(positions_to_analyze)} (范围: {min(positions_to_analyze)}-{max(positions_to_analyze)})")
        
        # 初始化分析结果 - 包含新旧两种数据结构
        step_analysis = {
            'analyzed_layers': layers_to_analyze,
            'analyzed_positions': positions_to_analyze,
            'sequence_length': current_sequence_length,
            'predictions_by_layer_and_position': defaultdict(dict),  # 增强版：全位置分析
            'predictions_by_layer': [],  # 🔧 兼容版：仅最后位置，兼容matplotlib图片生成
            'position_types': self._classify_positions(positions_to_analyze, current_sequence_length),
            'analysis_summary': {
                'vision_positions': 0,
                'prompt_positions': 0, 
                'generated_positions': 0
            }
        }
        
        # 计算位置统计
        for pos in positions_to_analyze:
            pos_type = step_analysis['position_types'][pos]
            if pos_type == 'vision_patch':
                step_analysis['analysis_summary']['vision_positions'] += 1
            elif pos_type == 'prompt_token':
                step_analysis['analysis_summary']['prompt_positions'] += 1
            elif pos_type == 'generated_token':
                step_analysis['analysis_summary']['generated_positions'] += 1
        
        # 分析每一层
        for layer_idx in layers_to_analyze:
            if layer_idx >= len(hidden_states):
                continue
                
            logger.info(f"   🧩 分析层 {layer_idx}...")
            layer_hidden = hidden_states[layer_idx]  # [1, seq_len, hidden_size]
            
            # 🔧 为兼容性添加：只分析最后一个位置（预测下一个token的位置）
            last_position_hidden = layer_hidden[0, -1, :]  # [hidden_size]
            normalized_hidden = self.norm_layer(last_position_hidden.unsqueeze(0)).squeeze(0)
            layer_logits = self.lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)
            layer_probs = F.softmax(layer_logits, dim=-1)
            top_probs, top_indices = torch.topk(layer_probs, self.config.top_k_tokens)
            
            # 🔧 构建兼容版数据结构 - 仅最后位置
            compat_predictions = []
            for i in range(self.config.top_k_tokens):
                token_id = top_indices[i].item()
                prob = top_probs[i].item()
                try:
                    token_text = self.processor.tokenizer.decode([token_id])
                    clean_token = token_text.replace('\n', '\\n').replace('\r', '\\r').strip()[:20]
                except:
                    clean_token = f"<TOKEN_{token_id}>"
                
                compat_predictions.append({
                    'rank': i + 1,
                    'token_id': token_id,
                    'token_text': clean_token,
                    'probability': prob,
                    'raw_token': token_text if 'token_text' in locals() else None
                })
            
            # 🔧 添加到兼容版数据结构
            step_analysis['predictions_by_layer'].append({
                'layer': layer_idx,
                'predictions': compat_predictions,
                'hidden_stats': {
                    'mean': last_position_hidden.mean().item(),
                    'std': last_position_hidden.std().item(),
                    'norm': last_position_hidden.norm().item()
                }
            })
            
            # 继续增强版的全位置分析
            step_analysis['predictions_by_layer_and_position'][layer_idx] = {}
            
            # 为每个位置进行分析
            for pos_idx, position in enumerate(positions_to_analyze):
                if position >= current_sequence_length:
                    continue
                    
                # 获取该位置的隐藏状态
                position_hidden = layer_hidden[0, position, :]  # [hidden_size]
                
                # 应用norm层和lm_head得到该层在该位置的预测
                normalized_hidden = self.norm_layer(position_hidden.unsqueeze(0)).squeeze(0)
                layer_logits = self.lm_head(normalized_hidden.unsqueeze(0)).squeeze(0)
                layer_probs = F.softmax(layer_logits, dim=-1)
                
                # 获取top-k预测
                top_probs, top_indices = torch.topk(layer_probs, self.config.top_k_tokens)
                
                # 解码top tokens
                position_predictions = []
                for i in range(self.config.top_k_tokens):
                    token_id = top_indices[i].item()
                    prob = top_probs[i].item()
                    
                    try:
                        token_text = self.processor.tokenizer.decode([token_id])
                        clean_token = token_text.replace('\n', '\\n').replace('\r', '\\r').strip()[:20]
                    except:
                        clean_token = f"<TOKEN_{token_id}>"
                    
                    position_predictions.append({
                        'rank': i + 1,
                        'token_id': token_id,
                        'token_text': clean_token,
                        'probability': prob,
                        'raw_token': token_text if 'token_text' in locals() else None
                    })
                
                # 存储该位置的分析结果
                step_analysis['predictions_by_layer_and_position'][layer_idx][position] = {
                    'predictions': position_predictions,
                    'position_type': step_analysis['position_types'][position],
                    'patch_coordinates': self._get_patch_coordinates(position) if step_analysis['position_types'][position] == 'vision_patch' else None,
                    'hidden_stats': {
                        'mean': position_hidden.mean().item(),
                        'std': position_hidden.std().item(), 
                        'norm': position_hidden.norm().item()
                    }
                }
                
                # 打印进度（每分析10个位置）
                if (pos_idx + 1) % 10 == 0:
                    logger.info(f"     已分析 {pos_idx + 1}/{len(positions_to_analyze)} 个位置...")
        
        logger.info(f"✅ Step {step}: 全位置分析完成")
        logger.info(f"   📊 分析统计: Vision={step_analysis['analysis_summary']['vision_positions']}, "
                   f"Prompt={step_analysis['analysis_summary']['prompt_positions']}, "
                   f"Generated={step_analysis['analysis_summary']['generated_positions']}")
        logger.info(f"   🔧 兼容性数据: {len(step_analysis['predictions_by_layer'])} 层已生成")
        
        return step_analysis
    
    def _determine_positions_to_analyze(self, current_sequence_length: int, step: int) -> List[int]:
        """确定要分析的位置 - 支持智能采样"""
        positions = []
        
        vision_patch_count = 256
        
        # 1. Vision patches - 支持采样
        if self.config.analyze_all_positions:
            vision_sample_count = int(vision_patch_count * self.config.vision_patch_sample_rate)
            if vision_sample_count == vision_patch_count:
                # 全部分析
                positions.extend(list(range(vision_patch_count)))
            else:
                # 均匀采样
                step_size = vision_patch_count // vision_sample_count
                sampled_patches = list(range(0, vision_patch_count, step_size))[:vision_sample_count]
                positions.extend(sampled_patches)
                logger.info(f"   🎯 Vision patches采样: {len(sampled_patches)}/{vision_patch_count}")
        
        # 2. Prompt tokens - 通常全部分析（数量较少）
        prompt_start = vision_patch_count
        estimated_prompt_end = min(261, current_sequence_length)  # 估算prompt结束位置
        if estimated_prompt_end > prompt_start:
            positions.extend(list(range(prompt_start, estimated_prompt_end)))
        
        # 3. Generated tokens - 限制数量避免计算过大
        generated_start = estimated_prompt_end
        if current_sequence_length > generated_start:
            generated_positions = list(range(generated_start, current_sequence_length))
            if len(generated_positions) > self.config.text_positions_limit:
                # 如果生成的token太多，只取最后N个
                generated_positions = generated_positions[-self.config.text_positions_limit:]
            positions.extend(generated_positions)
        
        return sorted(positions)
    
    def _get_patch_coordinates(self, position: int) -> Dict[str, int]:
        """获取vision patch的坐标（16x16网格）"""
        if position >= 256:
            return None
        
        row = position // 16
        col = position % 16
        return {'row': row, 'col': col, 'patch_id': position}
    
    def _classify_positions(self, positions: List[int], current_sequence_length: int) -> Dict[int, str]:
        """分类位置类型 - 新增缺失的方法"""
        position_types = {}
        
        vision_patch_count = 256  # PaliGemma2标准：256个vision patches
        initial_seq_len = 261  # 估算的初始序列长度（256 vision + 5 prompt tokens）
        
        for pos in positions:
            if pos < vision_patch_count:
                position_types[pos] = 'vision_patch'
            elif pos < initial_seq_len:
                position_types[pos] = 'prompt_token'
            else:
                position_types[pos] = 'generated_token'
        
        return position_types
    
    def create_autoregressive_visualization(self, results: Dict[str, Any], save_path: str, image_path: str):
        """创建自回归生成过程的可视化"""
        if not self.config.enable_visualization:
            return
            
        logger.info("🎨 创建自回归可视化...")
        
        # 1. 创建时间线可视化
        self._create_timeline_visualization(results, save_path)
        
        # 2. 创建层级演化可视化
        self._create_layer_evolution_plot(results, save_path)
        
        # 3. 创建交互式HTML（重点功能）
        if self.config.create_interactive_html:
            self._create_interactive_autoregressive_html(results, save_path, image_path)
        
        # 4. 如果配置了，创建动态GIF
        if self.config.create_gif:
            self._create_generation_gif(results, save_path)
    
    def _create_timeline_visualization(self, results: Dict[str, Any], save_path: str):
        """创建时间线可视化 - 显示每步的演化 (使用兼容数据结构)"""
        fig, axes = plt.subplots(2, 1, figsize=(16, 12))
        
        # 准备数据
        steps = [step_info['step'] for step_info in results['generation_history']]
        generated_tokens = [step_info['predicted_token_text'] for step_info in results['generation_history']]
        
        # 上图：各层的置信度演化
        ax1 = axes[0]
        for layer_idx in self.config.target_layers:
            confidences = []
            for step_info in results['generation_history']:
                # 🔧 修复：使用兼容的简单数据结构
                layer_data = next((l for l in step_info['logit_lens_analysis']['predictions_by_layer'] 
                                 if l['layer'] == layer_idx), None)
                if layer_data and layer_data.get('predictions') and len(layer_data['predictions']) > 0:
                    top_prob = layer_data['predictions'][0]['probability']
                    confidences.append(top_prob)
                else:
                    confidences.append(0)
            
            # 🔧 修复：只绘制有有效数据的层
            if any(c > 0 for c in confidences):
                ax1.plot(steps, confidences, marker='o', label=f'Layer {layer_idx}', linewidth=2)
        
        ax1.set_xlabel('Generation Step')
        ax1.set_ylabel('Top Prediction Confidence')
        ax1.set_title('Layer Confidence Evolution During Generation')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 下图：生成的token序列
        ax2 = axes[1]
        if generated_tokens:  # 确保有数据
            ax2.barh(range(len(generated_tokens)), [1] * len(generated_tokens))
            ax2.set_yticks(range(len(generated_tokens)))
            ax2.set_yticklabels([f"Step {s}: '{t}'" for s, t in zip(steps, generated_tokens)])
            ax2.set_xlabel('Generated Token Timeline')
            ax2.set_title('Generated Token Sequence')
        else:
            ax2.text(0.5, 0.5, 'No Generated Tokens', ha='center', va='center', transform=ax2.transAxes)
        
        plt.tight_layout()
        timeline_path = save_path.replace('.png', '_timeline.png')
        plt.savefig(timeline_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"✅ 时间线可视化保存: {timeline_path}")
    
    def _create_layer_evolution_plot(self, results: Dict[str, Any], save_path: str):
        """创建层级演化图 - 显示不同层在不同步骤的预测 (使用兼容数据结构)"""
        fig, ax = plt.subplots(figsize=(14, 10))
        
        # 构建热力图数据：[step, layer] = confidence
        steps = [step_info['step'] for step_info in results['generation_history']]
        layers = self.config.target_layers
        
        confidence_matrix = np.zeros((len(steps), len(layers)))
        valid_data_count = 0
        
        for i, step_info in enumerate(results['generation_history']):
            for j, layer_idx in enumerate(layers):
                # 🔧 修复：使用兼容的简单数据结构
                layer_data = next((l for l in step_info['logit_lens_analysis']['predictions_by_layer'] 
                                 if l['layer'] == layer_idx), None)
                if layer_data and layer_data.get('predictions') and len(layer_data['predictions']) > 0:
                    confidence_matrix[i, j] = layer_data['predictions'][0]['probability']
                    valid_data_count += 1
        
        # 🔧 修复：检查是否有有效数据
        if valid_data_count == 0:
            ax.text(0.5, 0.5, 'No Valid Prediction Data Available\nCheck data structure and keys', 
                   ha='center', va='center', transform=ax.transAxes, fontsize=14)
            ax.set_title('Layer Prediction Confidence - Data Issue Detected')
        else:
            # 创建热力图
            im = ax.imshow(confidence_matrix.T, aspect='auto', cmap='viridis', interpolation='nearest')
            
            # 设置轴标签
            ax.set_xticks(range(len(steps)))
            ax.set_xticklabels([f"Step {s}" for s in steps])
            ax.set_yticks(range(len(layers)))
            ax.set_yticklabels([f"Layer {l}" for l in layers])
            
            ax.set_xlabel('Generation Step')
            ax.set_ylabel('Model Layer')
            ax.set_title('Layer Prediction Confidence Across Generation Steps')
            
            # 添加颜色条
            cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
            cbar.set_label('Top Prediction Confidence')
            
            # 添加数值标注（对于较小的矩阵）
            if len(steps) <= 10 and len(layers) <= 8:
                for i in range(len(steps)):
                    for j in range(len(layers)):
                        if confidence_matrix[i, j] > 0:  # 只标注有数据的格子
                            text = ax.text(i, j, f'{confidence_matrix[i, j]:.2f}',
                                         ha="center", va="center", color="white", fontsize=8)
            
            logger.info(f"📊 热力图统计: {valid_data_count} 个有效数据点，最大置信度: {confidence_matrix.max():.4f}")
        
        plt.tight_layout()
        evolution_path = save_path.replace('.png', '_evolution.png')
        plt.savefig(evolution_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"✅ 层级演化图保存: {evolution_path}")
    
    def _create_interactive_autoregressive_html(self, results: Dict[str, Any], save_path: str, image_path: str):
        """创建交互式自回归HTML界面 - 全位置增强版 + 图像patch高亮"""
        logger.info("🌐 创建交互式自回归HTML界面（全位置版 + 图像高亮）...")
        
        # 编码图像
        import base64
        from PIL import Image
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 获取原始图像尺寸用于patch坐标计算
            original_image = Image.open(image_path)
            original_width, original_height = original_image.size
            
            # 计算显示尺寸（保持比例，最大边不超过400px）
            max_display_size = 400
            if original_width > original_height:
                display_width = max_display_size
                display_height = int((original_height / original_width) * max_display_size)
            else:
                display_height = max_display_size
                display_width = int((original_width / original_height) * max_display_size)
                
        except Exception as e:
            logger.warning(f"图像编码失败: {e}")
            image_base64 = ""
            original_width, original_height = 400, 400
            display_width, display_height = 400, 400
        
        # 准备数据 - 先进行序列化处理
        generation_data = self._make_serializable(results['generation_history'])
        model_info = results['model_info']
        
        # 构建HTML文件名
        base_dir = os.path.dirname(save_path)
        model_name = model_info['model_path'].split('/')[-1]
        image_name = os.path.basename(image_path).split('.')[0] if image_path else 'unknown'
        html_filename = f"{model_name}_{image_name}_full_position_autoregressive_logit_lens_with_highlights.html"
        html_save_path = os.path.join(base_dir, html_filename)
        
        # 生成增强的HTML内容 - 集成图像高亮功能
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PaliGemma2 Full-Position Autoregressive Logit Lens (With Image Highlights) - {model_name}</title>
    <style>
        body {{ margin: 0; padding: 20px; font-family: Arial, sans-serif; background-color: #f0f2f5; }}
        .container {{ max-width: 1600px; margin: 0 auto; }}
        .header {{ text-align: center; margin-bottom: 20px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .main-content {{ display: flex; gap: 20px; }}
        
        .left-panel {{ flex: 0 0 450px; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .image-container {{ margin-bottom: 20px; position: relative; }}
        .image-wrapper {{ position: relative; width: {display_width}px; height: {display_height}px; }}
        .source-image {{ 
            width: {display_width}px; 
            height: {display_height}px; 
            border: 2px solid #ddd; 
            border-radius: 8px; 
            display: block;
            object-fit: fill;
        }}
        
        /* 🎯 新增：图像patch高亮功能 */
        .highlight-box {{
            position: absolute;
            border: 3px solid #ff5722;
            pointer-events: none;
            display: none;
            box-shadow: 0 0 10px rgba(255, 87, 34, 0.5);
            z-index: 10;
            background-color: rgba(255, 87, 34, 0.2);
        }}
        
        .controls {{ margin-bottom: 20px; }}
        .step-slider {{ width: 100%; margin: 10px 0; }}
        .info-panel {{ background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; }}
        
        .right-panel {{ flex: 1; background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .step-display {{ margin-bottom: 20px; }}
        .current-step {{ font-size: 24px; font-weight: bold; color: #2196f3; }}
        .generated-text {{ background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0; }}
        .prediction-token {{ background-color: #4caf50; color: white; padding: 5px 10px; border-radius: 4px; }}
        
        /* 位置类型筛选器 */
        .position-filters {{ margin: 15px 0; }}
        .filter-btn {{ margin: 5px; padding: 8px 15px; border: none; border-radius: 20px; cursor: pointer; }}
        .filter-btn.active {{ font-weight: bold; }}
        .filter-vision {{ background-color: #2196f3; color: white; }}
        .filter-prompt {{ background-color: #4caf50; color: white; }}
        .filter-generated {{ background-color: #ff9800; color: white; }}
        
        /* 全位置预测表格 */
        .predictions-table-container {{ max-height: 600px; overflow: auto; margin-top: 20px; }}
        .predictions-table {{ width: 100%; border-collapse: collapse; font-size: 12px; }}
        .predictions-table th, .predictions-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
        .predictions-table th {{ background-color: #f2f2f2; position: sticky; top: 0; z-index: 10; }}
        .position-vision {{ background-color: #e3f2fd; }}
        .position-prompt {{ background-color: #e8f5e8; }}
        .position-generated {{ background-color: #fff3e0; }}
        
        .predictions-grid {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }}
        .layer-predictions {{ border: 1px solid #ddd; border-radius: 8px; padding: 15px; }}
        .layer-header {{ font-weight: bold; font-size: 16px; margin-bottom: 10px; color: #1976d2; }}
        .prediction-item {{ display: flex; justify-content: space-between; margin: 5px 0; padding: 5px 10px; background: #f5f5f5; border-radius: 4px; }}
        .token-text {{ font-family: monospace; }}
        .probability {{ font-weight: bold; color: #4caf50; }}
        
        .stats {{ font-size: 14px; color: #666; margin-top: 10px; }}
        .summary {{ background-color: #fff3e0; padding: 15px; border-radius: 8px; margin-bottom: 15px; }}
        .play-controls {{ text-align: center; margin: 15px 0; }}
        .play-btn {{ background: #4caf50; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 0 5px; }}
        .timeline {{ margin: 20px 0; }}
        .timeline-bar {{ width: 100%; height: 20px; background: #e0e0e0; border-radius: 10px; position: relative; }}
        .timeline-progress {{ height: 100%; background: linear-gradient(90deg, #4caf50, #2196f3); border-radius: 10px; transition: width 0.3s; }}
        
        /* 位置详情显示 */
        .position-details {{ margin-top: 20px; padding: 15px; background-color: #f8f9fa; border-radius: 8px; }}
        .position-highlight {{ 
            background-color: #ffeb3b !important; 
            font-weight: bold !important;
            border: 2px solid #ff9800 !important;
            box-shadow: 0 0 8px rgba(255, 152, 0, 0.5) !important;
        }}
        
        /* 🎯 增强：行高亮样式 */
        .highlighted-row {{ 
            background-color: #fff3e0 !important; 
            border-left: 4px solid #ff9800 !important;
            box-shadow: 2px 0 10px rgba(255, 152, 0, 0.3) !important;
        }}
        .highlighted-row td {{
            background-color: #fff3e0 !important;
            border-color: #ffcc80 !important;
        }}
        
        /* 🎯 表格hover增强 - 用于触发图像高亮 */
        .prediction-cell {{ cursor: pointer; transition: background-color 0.2s; position: relative; }}
        .prediction-cell:hover {{ background-color: #ffeb3b; }}
        
        /* 🎯 新增：进度条功能 */
        .probability-bar {{
            width: 100%;
            height: 4px;
            background-color: #e0e0e0;
            border-radius: 2px;
            margin-top: 3px;
            overflow: hidden;
        }}
        
        .probability-fill {{
            height: 100%;
            background: linear-gradient(90deg, #4caf50, #2196f3);
            border-radius: 2px;
            transition: width 0.3s ease;
        }}
        
        /* 🎯 新增：悬停提示框 */
        .custom-tooltip {{
            position: absolute;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 12px;
            border-radius: 8px;
            font-size: 11px;
            z-index: 1000;
            max-width: 250px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            pointer-events: none;
            display: none;
        }}
        
        .tooltip-header {{
            font-weight: bold;
            margin-bottom: 8px;
            color: #fff;
            border-bottom: 1px solid #444;
            padding-bottom: 4px;
        }}
        
        .tooltip-item {{
            display: flex;
            justify-content: space-between;
            margin: 4px 0;
            padding: 2px 0;
        }}
        
        .tooltip-token {{
            font-family: monospace;
            background-color: rgba(255, 255, 255, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }}
        
        .tooltip-prob {{
            font-weight: bold;
            color: #4caf50;
        }}
        
        /* 重复的样式定义已删除，使用上面更详细的.highlighted-row样式 */
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 PaliGemma2 Full-Position Autoregressive Logit Lens Analysis (With Image Highlights)</h1>
            <p>Model: {model_name} | Image: {image_name} | Generated: "{results['final_generated_text']}"</p>
            <div class="timeline">
                <div class="timeline-bar">
                    <div class="timeline-progress" id="timelineProgress"></div>
                </div>
                <p>Generation Progress: <span id="progressText">Step 0</span></p>
            </div>
        </div>
        
        <div class="main-content">
            <div class="left-panel">
                <div class="image-container">
                    <h3>📸 Source Image</h3>
                    <div class="image-wrapper">
                        {"<img src='data:image/jpeg;base64," + image_base64 + "' alt='Source Image' class='source-image' id='sourceImage'>" if image_base64 else "<p>Image not available</p>"}
                        <div class="highlight-box" id="highlightBox"></div>
                    </div>
                    <p class="instruction">💡 Hover over vision patch cells in the table to highlight corresponding image regions</p>
                </div>
                
                <div class="controls">
                    <h3>🎮 Controls</h3>
                    <label for="stepSlider">Generation Step:</label>
                    <input type="range" id="stepSlider" class="step-slider" 
                           min="0" max="{len(generation_data)-1}" value="0" step="1">
                    <div class="play-controls">
                        <button class="play-btn" onclick="toggleAutoPlay()">▶️ Auto Play</button>
                        <button class="play-btn" onclick="resetAnimation()">🔄 Reset</button>
                    </div>
                    
                    <div class="position-filters">
                        <h4>🎯 Position Type Filters:</h4>
                        <button class="filter-btn filter-vision active" onclick="toggleFilter('vision')">
                            🔵 Vision Patches
                        </button>
                        <button class="filter-btn filter-prompt active" onclick="toggleFilter('prompt')">
                            🟢 Prompt Tokens  
                        </button>
                        <button class="filter-btn filter-generated active" onclick="toggleFilter('generated')">
                            🟠 Generated Tokens
                        </button>
                    </div>
                </div>
                
                <div class="info-panel">
                    <h3>📊 Current Step Info</h3>
                    <div class="summary" id="stepSummary">
                        <p><strong>Step:</strong> <span id="currentStep">0</span></p>
                        <p><strong>Sequence Length:</strong> <span id="seqLength">{results['initial_sequence_length']}</span></p>
                        <p><strong>Next Token:</strong> <span id="nextToken" class="prediction-token">-</span></p>
                        <p><strong>Generated So Far:</strong></p>
                        <div id="generatedText" class="generated-text">"{results['initial_prompt']}"</div>
                    </div>
                </div>
                
                <div class="info-panel">
                    <h3>📈 Full-Position Statistics</h3>
                    <p><strong>Total Steps:</strong> {len(generation_data)}</p>
                    <p><strong>Tokens Generated:</strong> {results['total_tokens_generated']}</p>
                    <p><strong>Analyzed Layers:</strong> {', '.join(map(str, model_info['target_layers']))}</p>
                    <p><strong>Position Analysis:</strong> <span id="positionStats">All positions tracked</span></p>
                    <p><strong>Image Size:</strong> {original_width}×{original_height} (Display: {display_width}×{display_height})</p>
                </div>
            </div>
            
            <div class="right-panel">
                <div class="step-display">
                    <h2 class="current-step" id="stepTitle">Step 0: Initial State</h2>
                </div>
                
                <div class="predictions-table-container" id="predictionsTableContainer">
                    <table class="predictions-table" id="predictionsTable">
                        <!-- 动态内容将在这里插入 -->
                    </table>
                </div>
                
                <div class="position-details" id="positionDetails">
                    <h4>📍 Position Details</h4>
                    <p>Click on any cell in the table above to see detailed predictions for that position.</p>
                    <p>🎯 Hover over vision patch cells to highlight the corresponding image region.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 🎯 新增：自定义提示框 -->
    <div class="custom-tooltip" id="customTooltip">
        <div class="tooltip-header" id="tooltipHeader">Layer X - Position Y</div>
        <div class="tooltip-content" id="tooltipContent">
            <!-- 动态内容 -->
        </div>
    </div>

<script>
    // 数据
    const generationData = {json.dumps(generation_data, indent=2)};
    const initialPrompt = "{results['initial_prompt']}";
    const initialSeqLen = {results['initial_sequence_length']};
    
    // 🎯 图像高亮相关变量
    const originalWidth = {original_width};
    const originalHeight = {original_height};
    const imageDisplayWidth = {display_width};
    const imageDisplayHeight = {display_height};
    const gridSize = 16;  // 16x16 patch grid
    
    // 状态
    let currentStepIndex = 0;
    let isAutoPlaying = false;
    let autoPlayTimer = null;
    let activeFilters = new Set(['vision', 'prompt', 'generated']);
    let isHighlightLocked = false;
    let lockedPatchIndex = null;
    // 🔧 新增：防止重复触发的状态变量
    let currentHighlightedPatch = null;
    let isScrolling = false;
    let scrollTimeout = null;
    let lastMousePosition = {{ x: -1, y: -1 }};
    
    // UI元素
    const stepSlider = document.getElementById('stepSlider');
    const currentStepSpan = document.getElementById('currentStep');
    const seqLengthSpan = document.getElementById('seqLength');
    const nextTokenSpan = document.getElementById('nextToken');
    const generatedTextDiv = document.getElementById('generatedText');
    const stepTitle = document.getElementById('stepTitle');
    const predictionsTable = document.getElementById('predictionsTable');
    const timelineProgress = document.getElementById('timelineProgress');
    const progressText = document.getElementById('progressText');
    const positionDetails = document.getElementById('positionDetails');
    const positionStats = document.getElementById('positionStats');
    const highlightBox = document.getElementById('highlightBox');
    const sourceImage = document.getElementById('sourceImage');
    const customTooltip = document.getElementById('customTooltip');
    const tooltipHeader = document.getElementById('tooltipHeader');
    const tooltipContent = document.getElementById('tooltipContent');
    
    // 初始化
    updateDisplay();
    
    // 事件监听
    stepSlider.addEventListener('input', function() {{
        currentStepIndex = parseInt(this.value);
        updateDisplay();
        if (isAutoPlaying) {{
            stopAutoPlay();
        }}
    }});
    
    function updateDisplay() {{
        if (currentStepIndex >= generationData.length) {{
            currentStepIndex = generationData.length - 1;
        }}
        
        if (currentStepIndex < 0) {{
            currentStepIndex = 0;
        }}
        
        const stepData = generationData[currentStepIndex];
        
        // 更新基本信息
        currentStepSpan.textContent = stepData.step;
        seqLengthSpan.textContent = stepData.sequence_length;
        nextTokenSpan.textContent = stepData.predicted_token_text;
        
        // 更新生成文本
        const displayText = stepData.generated_so_far || initialPrompt;
        generatedTextDiv.innerHTML = `"${{displayText}}"`;
        
        // 更新标题
        stepTitle.textContent = `Step ${{stepData.step}}: Predicting "${{stepData.predicted_token_text}}"`;
        
        // 更新时间线
        const progress = ((currentStepIndex + 1) / generationData.length) * 100;
        timelineProgress.style.width = `${{progress}}%`;
        progressText.textContent = `Step ${{stepData.step}} / ${{generationData.length - 1}}`;
        
        // 更新全位置预测表格
        updateFullPositionTable(stepData.logit_lens_analysis);
        
        // 更新滑块
        stepSlider.value = currentStepIndex;
    }}
    
    function updateFullPositionTable(logitLensData) {{
        if (!logitLensData.predictions_by_layer_and_position) {{
            predictionsTable.innerHTML = '<tr><td>No full-position data available</td></tr>';
            return;
        }}
        
        const layers = Object.keys(logitLensData.predictions_by_layer_and_position).map(Number).sort((a,b) => a-b);
        const allPositions = new Set();
        
        // 收集所有位置
        layers.forEach(layer => {{
            Object.keys(logitLensData.predictions_by_layer_and_position[layer]).forEach(pos => {{
                allPositions.add(parseInt(pos));
            }});
        }});
        
        const sortedPositions = Array.from(allPositions).sort((a,b) => a-b);
        
        // 应用过滤器
        const filteredPositions = sortedPositions.filter(pos => {{
            const posType = logitLensData.position_types[pos];
            if (posType === 'vision_patch' && activeFilters.has('vision')) return true;
            if (posType === 'prompt_token' && activeFilters.has('prompt')) return true;
            if (posType === 'generated_token' && activeFilters.has('generated')) return true;
            return false;
        }});
        
        // 更新位置统计
        positionStats.textContent = `${{filteredPositions.length}}/${{sortedPositions.length}} positions shown`;
        
        // 生成表格头
        let headerHTML = '<tr><th>Position</th>';
        layers.forEach(layer => {{
            headerHTML += `<th>Layer ${{layer}}</th>`;
        }});
        headerHTML += '</tr>';
        
        // 生成表格行
        let bodyHTML = '';
        filteredPositions.forEach(pos => {{
            const posType = logitLensData.position_types[pos];
            let rowClass = '';
            if (posType === 'vision_patch') rowClass = 'position-vision';
            else if (posType === 'prompt_token') rowClass = 'position-prompt';
            else if (posType === 'generated_token') rowClass = 'position-generated';
            
            bodyHTML += `<tr class="${{rowClass}}">`;
            bodyHTML += `<td><strong>P${{pos}}</strong><br><small>${{posType.replace('_', ' ')}}</small></td>`;
            
            layers.forEach(layer => {{
                const layerData = logitLensData.predictions_by_layer_and_position[layer];
                if (layerData && layerData[pos]) {{
                    const topPred = layerData[pos].predictions[0];
                    const cleanToken = topPred.token_text.substring(0, 8);
                    const prob = (topPred.probability * 100).toFixed(1);
                    
                    // 🎯 为vision patch添加鼠标事件以触发图像高亮
                    const cellEvents = posType === 'vision_patch' ? 
                        `onmouseenter="highlightImagePatch(${{pos}}); showCustomTooltip(event, ${{layer}}, ${{pos}})" onmouseleave="hideImageHighlight(); hideCustomTooltip()" onclick="lockImageHighlight(${{pos}})"` : 
                        `onmouseenter="showCustomTooltip(event, ${{layer}}, ${{pos}})" onmouseleave="hideCustomTooltip()" onclick="showPositionDetails(${{layer}}, ${{pos}})"`;
                    
                    bodyHTML += `<td class="prediction-cell" data-layer="${{layer}}" data-position="${{pos}}" ${{cellEvents}}>
                                    <div class="token-text">${{cleanToken}}</div>
                                    <div class="probability">${{prob}}%</div>
                                    <div class="probability-bar">
                                        <div class="probability-fill" style="width: ${{prob}}%"></div>
                                    </div>
                                </td>`;
                }} else {{
                    bodyHTML += '<td>-</td>';
                }}
            }});
            
            bodyHTML += '</tr>';
        }});
        
        predictionsTable.innerHTML = headerHTML + bodyHTML;
    }}
    
    // 🎯 图像patch高亮核心函数 - 修复版
    function highlightImagePatch(patchIndex) {{
        if (patchIndex < 0 || patchIndex >= 256) return; // 只处理vision patches
        
        // 🔧 防止重复高亮同一个patch
        if (currentHighlightedPatch === patchIndex && !isHighlightLocked) {{
            return;
        }}
        
        // 🔧 避免在滚动过程中触发新的高亮
        if (isScrolling) {{
            return;
        }}
        
        currentHighlightedPatch = patchIndex;
        
        // 计算patch在原图中的位置 - 改进精度
        const row = Math.floor(patchIndex / gridSize);
        const col = patchIndex % gridSize;
        
        // 🔧 修复：使用更精确的坐标计算
        const patchWidthInDisplay = imageDisplayWidth / gridSize;
        const patchHeightInDisplay = imageDisplayHeight / gridSize;
        
        const left = col * patchWidthInDisplay;
        const top = row * patchHeightInDisplay;
        const width = patchWidthInDisplay;
        const height = patchHeightInDisplay;
        
        // 🔧 添加边界检查，确保高亮框不超出图像边界
        const clampedLeft = Math.max(0, Math.min(left, imageDisplayWidth - width));
        const clampedTop = Math.max(0, Math.min(top, imageDisplayHeight - height));
        const clampedWidth = Math.min(width, imageDisplayWidth - clampedLeft);
        const clampedHeight = Math.min(height, imageDisplayHeight - clampedTop);
        
        highlightBox.style.left = `${{clampedLeft}}px`;
        highlightBox.style.top = `${{clampedTop}}px`;
        highlightBox.style.width = `${{clampedWidth}}px`;
        highlightBox.style.height = `${{clampedHeight}}px`;
        highlightBox.style.display = 'block';
        
        // 🎯 修复：仅高亮对应的表格行 - 单一选择版
        highlightTableRowForPatch(patchIndex);
    }}
    
    // 🎯 修复：为patch高亮对应的表格行 - 单一选择版
    function highlightTableRowForPatch(patchIndex) {{
        // 🔧 修复：更精确的高亮清除和设置
        clearAllHighlights();
        
        // 🔧 修复：精确查找当前步骤中的patch对应行
        const stepData = generationData[currentStepIndex];
        if (!stepData || !stepData.logit_lens_analysis || !stepData.logit_lens_analysis.position_types) {{
            console.warn(`⚠️ 当前步骤 ${{currentStepIndex}} 数据不完整`);
            return;
        }}
        
        // 🔧 检查该patch在当前步骤是否存在
        const positionTypes = stepData.logit_lens_analysis.position_types;
        if (!positionTypes[patchIndex] || positionTypes[patchIndex] !== 'vision_patch') {{
            console.warn(`⚠️ Patch ${{patchIndex}} 在当前步骤不存在或不是vision patch`);
            return;
        }}
        
        // 🔧 修复：只寻找第一个匹配的单元格，避免多选
        const patchCell = document.querySelector(`[data-position="${{patchIndex}}"]`);
        
        if (patchCell) {{
            // 高亮单元格
            patchCell.classList.add('position-highlight');
            
            // 获取所在的行
            const targetRow = patchCell.closest('tr');
            if (targetRow) {{
                targetRow.classList.add('highlighted-row');
                
                // 🔧 修复：智能滚动 - 只在必要时滚动
                scrollToTableRowSafely(patchCell);
                
                console.log(`🎯 高亮Patch ${{patchIndex}}, 找到对应单元格`);
            }} else {{
                console.warn(`⚠️ 未找到Patch ${{patchIndex}} 对应的表格行`);
            }}
        }} else {{
            console.warn(`⚠️ 未找到Patch ${{patchIndex}} 对应的表格单元格，可能该patch在当前筛选条件下不可见`);
        }}
    }}
    
    // 🔧 新增：安全的清除所有高亮
    function clearAllHighlights() {{
        // 清除单元格高亮
        document.querySelectorAll('.prediction-cell').forEach(cell => {{
            cell.classList.remove('position-highlight');
        }});
        
        // 清除行高亮
        document.querySelectorAll('tr').forEach(row => {{
            row.classList.remove('highlighted-row');
        }});
        
        currentHighlightedPatch = null;
    }}
    
    // 🎯 修复：智能滚动到表格行 - 防止过度滚动版
    function scrollToTableRowSafely(cellElement) {{
        const tableContainer = document.getElementById('predictionsTableContainer');
        
        const targetRow = cellElement.closest('tr');
        if (!targetRow) {{
            console.warn('未找到目标行');
            return;
        }}
        
        // 🔧 修复：检查行是否已经在可见区域内
        const containerRect = tableContainer.getBoundingClientRect();
        const targetRowRect = targetRow.getBoundingClientRect();
        
        const rowTop = targetRowRect.top - containerRect.top;
        const rowBottom = targetRowRect.bottom - containerRect.top;
        const containerHeight = tableContainer.clientHeight;
        
        // 🔧 修复：定义更严格的可见性判断
        const isRowVisible = rowTop >= 0 && rowBottom <= containerHeight;
        const isRowReasonablyCentered = Math.abs(rowTop - containerHeight / 2) < containerHeight * 0.3; // 30%容差
        
        // 🔧 修复：只在行不可见或严重偏离中心时才滚动
        if (!isRowVisible || (!isRowReasonablyCentered && !isScrolling)) {{
            isScrolling = true;
            
            // 清除之前的滚动延时
            if (scrollTimeout) {{
                clearTimeout(scrollTimeout);
            }}
            
            // 计算目标滚动位置
            const targetRowOffsetTop = targetRow.offsetTop;
            const targetScrollTop = targetRowOffsetTop - (containerHeight / 2) + (targetRow.offsetHeight / 2);
            const maxScrollTop = tableContainer.scrollHeight - containerHeight;
            const finalScrollTop = Math.max(0, Math.min(targetScrollTop, maxScrollTop));
            
            // 🔧 修复：使用更温和的滚动
            tableContainer.scrollTo({{
                top: finalScrollTop,
                behavior: 'smooth'
            }});
            
            // 🔧 修复：滚动完成后重置状态
            scrollTimeout = setTimeout(() => {{
                isScrolling = false;
                scrollTimeout = null;
                
                // 确保高亮样式保持
                if (!targetRow.classList.contains('highlighted-row')) {{
                    targetRow.classList.add('highlighted-row');
                }}
            }}, 800); // 给滚动动画充分时间完成
        }}
    }}
    
    function hideImageHighlight() {{
        if (!isHighlightLocked) {{
            highlightBox.style.display = 'none';
            // 🎯 修复：清除表格高亮
            clearAllHighlights();
        }}
    }}
    
    function lockImageHighlight(patchIndex) {{
        isHighlightLocked = !isHighlightLocked;
        if (isHighlightLocked) {{
            lockedPatchIndex = patchIndex;
            highlightImagePatch(patchIndex);
            console.log(`🔒 锁定高亮Patch ${{patchIndex}}`);
        }} else {{
            lockedPatchIndex = null;
            highlightBox.style.display = 'none';
            clearAllHighlights();
            console.log(`🔓 解锁高亮`);
        }}
    }}
    
    // 🎯 新增：自定义tooltip功能
    function showCustomTooltip(event, layer, position) {{
        const stepData = generationData[currentStepIndex];
        const layerData = stepData.logit_lens_analysis.predictions_by_layer_and_position[layer];
        
        if (!layerData || !layerData[position]) {{
            hideCustomTooltip();
            return;
        }}
        
        const posData = layerData[position];
        const posType = posData.position_type;
        
        // 更新header
        if (posType === 'vision_patch') {{
            const coords = posData.patch_coordinates;
            tooltipHeader.textContent = `Patch ${{position}} - Layer ${{layer}} (Row ${{coords.row}}, Col ${{coords.col}})`;
        }} else {{
            tooltipHeader.textContent = `Position ${{position}} - Layer ${{layer}} (${{posType.replace('_', ' ')}})`;
        }}
        
        // 生成Top5预测内容
        let contentHTML = '';
        posData.predictions.slice(0, 5).forEach((pred, index) => {{
            const tokenText = pred.token_text.length > 12 ? 
                pred.token_text.substring(0, 12) + '...' : 
                pred.token_text;
            contentHTML += `
                <div class="tooltip-item">
                    <span class="tooltip-token">${{tokenText}}</span>
                    <span class="tooltip-prob">${{(pred.probability * 100).toFixed(2)}}%</span>
                </div>
            `;
        }});
        
        tooltipContent.innerHTML = contentHTML;
        
        // 定位tooltip
        positionTooltip(event);
        customTooltip.style.display = 'block';
    }}
    
    function hideCustomTooltip() {{
        customTooltip.style.display = 'none';
    }}
    
    function positionTooltip(event) {{
        const tooltip = customTooltip;
        const tooltipRect = tooltip.getBoundingClientRect();
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        let x = event.clientX + 15; // 15px offset from cursor
        let y = event.clientY + 15;
        
        // 调整位置防止超出视窗
        if (x + tooltipRect.width > viewportWidth) {{
            x = event.clientX - tooltipRect.width - 15;
        }}
        
        if (y + tooltipRect.height > viewportHeight) {{
            y = event.clientY - tooltipRect.height - 15;
        }}
        
        // 确保tooltip在视窗内
        x = Math.max(10, Math.min(x, viewportWidth - tooltipRect.width - 10));
        y = Math.max(10, Math.min(y, viewportHeight - tooltipRect.height - 10));
        
        tooltip.style.left = `${{x}}px`;
        tooltip.style.top = `${{y}}px`;
    }}
    
    function showPositionDetails(layer, position) {{
        const stepData = generationData[currentStepIndex];
        const layerData = stepData.logit_lens_analysis.predictions_by_layer_and_position[layer];
        
        if (!layerData || !layerData[position]) {{
            positionDetails.innerHTML = '<h4>📍 Position Details</h4><p>No data available for this position.</p>';
            return;
        }}
        
        const posData = layerData[position];
        const posType = posData.position_type;
        
        let detailsHTML = `
        <h4>📍 Position ${{position}} Details</h4>
        <p><strong>Type:</strong> ${{posType.replace('_', ' ')}}</p>
        <p><strong>Layer:</strong> ${{layer}}</p>
        `;
        
        if (posData.patch_coordinates) {{
            detailsHTML += `<p><strong>Patch Coordinates:</strong> Row ${{posData.patch_coordinates.row}}, Col ${{posData.patch_coordinates.col}}</p>`;
        }}
        
        detailsHTML += '<h5>🎯 Top Predictions:</h5><ul>';
        posData.predictions.forEach(pred => {{
            detailsHTML += `<li><strong>${{pred.token_text}}</strong>: ${{(pred.probability * 100).toFixed(2)}}%</li>`;
        }});
        detailsHTML += '</ul>';
        
        detailsHTML += `
        <h5>📊 Hidden State Stats:</h5>
        <p>Mean: ${{posData.hidden_stats.mean.toFixed(4)}}, Std: ${{posData.hidden_stats.std.toFixed(4)}}</p>
        `;
        
        positionDetails.innerHTML = detailsHTML;
        
        // 高亮选中的单元格
        document.querySelectorAll('.prediction-cell').forEach(cell => {{
            cell.classList.remove('position-highlight');
        }});
        const selectedCell = document.querySelector(`[data-layer="${{layer}}"][data-position="${{position}}"]`);
        if (selectedCell) {{
            selectedCell.classList.add('position-highlight');
        }}
    }}
    
    function toggleFilter(filterType) {{
        const btn = document.querySelector(`.filter-${{filterType}}`);
        
        if (activeFilters.has(filterType)) {{
            activeFilters.delete(filterType);
            btn.classList.remove('active');
        }} else {{
            activeFilters.add(filterType);
            btn.classList.add('active');
        }}
        
        updateDisplay();  // 重新渲染表格
    }}
    
    function toggleAutoPlay() {{
        if (isAutoPlaying) {{
            stopAutoPlay();
        }} else {{
            startAutoPlay();
        }}
    }}
    
    function startAutoPlay() {{
        isAutoPlaying = true;
        document.querySelector('.play-btn').textContent = '⏸️ Pause';
        
        autoPlayTimer = setInterval(() => {{
            if (currentStepIndex < generationData.length - 1) {{
                currentStepIndex++;
                updateDisplay();
            }} else {{
                stopAutoPlay();
            }}
        }}, 2000);  // 每2秒切换一步
    }}
    
    function stopAutoPlay() {{
        isAutoPlaying = false;
        document.querySelector('.play-btn').textContent = '▶️ Auto Play';
        if (autoPlayTimer) {{
            clearInterval(autoPlayTimer);
            autoPlayTimer = null;
        }}
    }}
    
    function resetAnimation() {{
        stopAutoPlay();
        currentStepIndex = 0;
        updateDisplay();
        isHighlightLocked = false;
        lockedPatchIndex = null;
        hideImageHighlight();
    }}
    
    // 键盘控制
    document.addEventListener('keydown', function(event) {{
        switch(event.key) {{
            case 'ArrowLeft':
                if (currentStepIndex > 0) {{
                    currentStepIndex--;
                    updateDisplay();
                }}
                break;
            case 'ArrowRight':
                if (currentStepIndex < generationData.length - 1) {{
                    currentStepIndex++;
                    updateDisplay();
                }}
                break;
            case ' ':  // 空格键
                event.preventDefault();
                toggleAutoPlay();
                break;
            case 'Escape':  // ESC键解锁高亮
                isHighlightLocked = false;
                lockedPatchIndex = null;
                hideImageHighlight();
                break;
        }}
    }});
    
    // 🎯 图像点击事件 - 支持直接在图像上点击
    if (sourceImage) {{
        sourceImage.addEventListener('mousemove', (e) => {{
            // 🔧 防止鼠标位置没有实际变化时重复触发
            const currentX = e.clientX;
            const currentY = e.clientY;
            if (Math.abs(currentX - lastMousePosition.x) < 2 && Math.abs(currentY - lastMousePosition.y) < 2) {{
                return; // 位置变化太小，跳过
            }}
            lastMousePosition = {{ x: currentX, y: currentY }};
            
            if (!isHighlightLocked && !isScrolling) {{
                const patchIndex = getPatchIndexFromMouseEvent(e);
                if (patchIndex >= 0 && patchIndex < 256) {{
                    highlightImagePatch(patchIndex);
                }}
            }}
        }});
        
        sourceImage.addEventListener('mouseleave', () => {{
            if (!isHighlightLocked) {{
                hideImageHighlight();
            }}
        }});
        
        sourceImage.addEventListener('click', (e) => {{
            const patchIndex = getPatchIndexFromMouseEvent(e);
            if (patchIndex >= 0 && patchIndex < 256) {{
                lockImageHighlight(patchIndex);
            }}
        }});
    }}
    
    // 🔧 修复：改进patch索引计算精度
    function getPatchIndexFromMouseEvent(e) {{
        const rect = sourceImage.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;
        
        // 🔧 修复：添加边界检查
        if (x < 0 || y < 0 || x >= rect.width || y >= rect.height) {{
            return -1; // 超出图像边界
        }}
        
        // 🔧 修复：更精确的patch坐标计算
        const patchWidth = rect.width / gridSize;
        const patchHeight = rect.height / gridSize;
        
        const patchX = Math.floor(x / patchWidth);
        const patchY = Math.floor(y / patchHeight);
        
        // 🔧 修复：确保patch索引在有效范围内
        const clampedPatchX = Math.max(0, Math.min(patchX, gridSize - 1));
        const clampedPatchY = Math.max(0, Math.min(patchY, gridSize - 1));
        
        const patchIndex = clampedPatchY * gridSize + clampedPatchX;
        
        // 🔧 调试信息（可选开启）
        if (window.DEBUG_PATCH_CALC) {{
            console.log(`🎯 鼠标位置: (${{x.toFixed(1)}}, ${{y.toFixed(1)}}) -> Patch (${{clampedPatchX}}, ${{clampedPatchY}}) = ${{patchIndex}}`);
        }}
        
        return Math.min(patchIndex, 255);  // 确保不超过255
    }}
</script>
</body>
</html>
        """
        
        # 保存HTML文件
        with open(html_save_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"✅ 全位置交互式自回归HTML（含图像高亮）保存: {html_save_path}")
        return html_save_path
    
    def _create_generation_gif(self, results: Dict[str, Any], save_path: str):
        """创建生成过程的动态GIF（可选功能）"""
        # 这里可以实现GIF生成，但由于matplotlib动画复杂，暂时跳过
        logger.info("ℹ️ GIF生成功能暂未实现")
    
    def save_results(self, results: Dict[str, Any], sample_id: str):
        """保存分析结果"""
        if self.config.save_step_details:
            
            # 保存JSON格式
            json_path = os.path.join(self.config.output_dir, f"autoregressive_results_{sample_id}.json")
            serializable_results = self._make_serializable(results)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            # 保存pickle格式（保留tensor信息）
            pickle_path = os.path.join(self.config.output_dir, f"autoregressive_results_{sample_id}.pkl")
            with open(pickle_path, 'wb') as f:
                pickle.dump(results, f)
            
            logger.info(f"📁 结果已保存: {json_path}, {pickle_path}")
    
    def _make_serializable(self, obj):
        """将包含tensor的对象转换为可序列化格式"""
        if isinstance(obj, torch.Tensor):
            return obj.cpu().tolist()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self._make_serializable(item) for item in obj)
        else:
            return obj


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="PaliGemma2 Autoregressive Logit Lens Analysis")
    
    parser.add_argument("--model_path", type=str, default="google/paligemma2-3b-pt-224", 
                       help="PaliGemma2模型路径")
    parser.add_argument("--device", type=str, default="cuda:0" if torch.cuda.is_available() else "cpu", 
                       help="设备")
    parser.add_argument("--dtype", type=str, default="bf16", help="数据类型")
    parser.add_argument("--image_path", type=str, 
                       default='/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/attention_knockout_diagnostic_output/real_images/real_image_20250610_170727.png', 
                       help="输入图像路径")
    parser.add_argument("--prompt", type=str, default="describe the image", help="提示词")
    parser.add_argument("--output_dir", type=str, default="/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/pro/paligemma2_autoregressive_results", 
                       help="输出目录")
    parser.add_argument("--max_new_tokens", type=int, default=20, help="最大生成token数")
    parser.add_argument("--target_layers", nargs='+', type=int, default=[0, 5, 10, 15, 20, 25 , 26], 
                       help="要分析的目标层")
    
    args = parser.parse_args()
    
    # 创建配置
    config = AutoregressiveLogitLensConfig(
        model_path=args.model_path,
        device=args.device,
        dtype=args.dtype,
        output_dir=args.output_dir,
        max_new_tokens=args.max_new_tokens,
        target_layers=args.target_layers
    )
    
    # 创建分析器
    analyzer = AutoregressiveLogitLensAnalyzer(config)
    
    # 运行自回归分析
    results = analyzer.analyze_autoregressive_generation(args.image_path, args.prompt)
    
    # 创建可视化
    if results:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        viz_path = os.path.join(args.output_dir, f"autoregressive_visualization_{timestamp}.png")
        analyzer.create_autoregressive_visualization(results, viz_path, args.image_path)
        
        # 保存结果
        analyzer.save_results(results, timestamp)
        
        logger.info("🎉 === 自回归Logit Lens分析完成 ===")
        logger.info(f"📁 结果保存在: {args.output_dir}")
        logger.info(f"🌐 查看交互式结果: 打开生成的HTML文件")


if __name__ == "__main__":
    main() 