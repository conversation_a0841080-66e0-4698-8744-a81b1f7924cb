#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 原生 vs MonkeyPatch Baseline 对比测试
=================================================

验证MonkeyPatch是否改变了模型的正常行为
"""

import torch
from transformers import AutoProcessor, PaliGemmaForConditionalGeneration, set_seed
from PIL import Image
import numpy as np
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_native_paligemma2(image_path: str, prompt: str):
    """测试原生PaliGemma2输出"""
    logger.info("🔬 测试原生PaliGemma2...")
    
    model_id = "google/paligemma2-3b-pt-224"
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    
    # 加载原生模型
    model = PaliGemmaForConditionalGeneration.from_pretrained(
        model_id, 
        torch_dtype=torch.bfloat16, 
        device_map="cuda:0"
    ).eval()
    processor = AutoProcessor.from_pretrained(model_id)
    
    # 准备输入
    image = Image.open(image_path).convert('RGB')
    inputs = processor(text=prompt, images=image, return_tensors="pt")
    
    # 移动到设备
    for key, value in inputs.items():
        if torch.is_tensor(value):
            inputs[key] = value.to(device)
    
    # 生成
    with torch.inference_mode():
        outputs = model.generate(
            **inputs, 
            max_new_tokens=20, 
            do_sample=False,
            temperature=1.0
        )
    
    # 解码
    input_len = inputs["input_ids"].shape[-1]
    generated_text = processor.decode(outputs[0][input_len:], skip_special_tokens=True)
    
    logger.info(f"✅ 原生输出: '{generated_text}'")
    return generated_text

def test_monkeypatch_baseline(image_path: str, prompt: str):
    """测试MonkeyPatch baseline输出"""
    logger.info("🐒 测试MonkeyPatch baseline...")
    
    # 导入我们的MonkeyPatch类
    import sys
    sys.path.append('/home/<USER>/claude/SpatialVLA/scripts/spatialvla_4b_finetune/P/pro')
    
    from paligemma2_monkey_patch_interference import MonkeyPatchConfig, InvasiveLogitLensAnalyzer
    
    # 配置 - baseline模式应该不做任何干扰
    config = MonkeyPatchConfig(
        model_path="google/paligemma2-3b-pt-224",
        device="cuda:0" if torch.cuda.is_available() else "cpu",
        max_new_tokens=20,
        output_dir="./temp_comparison"
    )
    
    # 创建分析器
    analyzer = InvasiveLogitLensAnalyzer(config)
    
    try:
        # 运行baseline实验
        result = analyzer.run_interference_experiment(
            image_path=image_path,
            prompt=prompt,
            experiment_type="baseline"
        )
        
        generated_text = result['generation_results']['final_generated_text']
        logger.info(f"✅ MonkeyPatch baseline输出: '{generated_text}'")
        return generated_text
        
    finally:
        # 确保安全恢复
        analyzer._restore_model_safely()

def compare_outputs():
    """对比两种方法的输出"""
    logger.info("=" * 80)
    logger.info("🔍 PaliGemma2 原生 vs MonkeyPatch Baseline 对比测试")
    logger.info("=" * 80)
    
    # 测试参数
    image_path = "coke.png"
    prompt = "Describe what you see in this image."
    
    # 检查图像是否存在
    import os
    if not os.path.exists(image_path):
        logger.info("📸 创建测试图像...")
        test_image = Image.new('RGB', (224, 224), color='red')
        test_image.save(image_path)
    
    # 设置随机种子保证一致性
    set_seed(42)
    
    # 测试原生模型
    native_output = test_native_paligemma2(image_path, prompt)
    
    # 重置随机种子
    set_seed(42)
    
    # 测试MonkeyPatch baseline
    monkeypatch_output = test_monkeypatch_baseline(image_path, prompt)
    
    # 对比结果
    logger.info("\n" + "=" * 60)
    logger.info("📊 对比结果")
    logger.info("=" * 60)
    logger.info(f"🔬 原生输出:      '{native_output}'")
    logger.info(f"🐒 MonkeyPatch:   '{monkeypatch_output}'")
    
    if native_output.strip() == monkeypatch_output.strip():
        logger.info("✅ 输出一致 - MonkeyPatch baseline正确模拟了原生行为")
        return True
    else:
        logger.error("❌ 输出不一致 - MonkeyPatch可能改变了正常行为！")
        logger.error("这表明MonkeyPatch在baseline模式下引入了意外的干扰")
        return False

def investigate_differences():
    """深入调查差异原因"""
    logger.info("\n🔍 调查MonkeyPatch可能的问题...")
    
    # 可能的原因：
    logger.info("🧐 可能的原因：")
    logger.info("1. MonkeyPatch的forward替换改变了计算图")
    logger.info("2. target_layers的选择影响了非目标层")
    logger.info("3. 状态记录过程引入了副作用")
    logger.info("4. 随机种子/随机状态不一致")
    logger.info("5. 模型加载或初始化的差异")
    
    # 建议的修复方案：
    logger.info("\n💡 建议的修复方案：")
    logger.info("1. 确保baseline模式下完全不应用任何patch")
    logger.info("2. 检查target_layers是否真的需要限制范围")
    logger.info("3. 在baseline模式下完全跳过MonkeyPatch应用")
    logger.info("4. 添加strict模式，确保与原生输出完全一致")

if __name__ == "__main__":
    try:
        # 运行对比测试
        is_consistent = compare_outputs()
        
        if not is_consistent:
            investigate_differences()
            
            logger.info("\n⚠️ 建议：")
            logger.info("需要修改MonkeyPatch系统，确保baseline模式下与原生输出完全一致")
            logger.info("这是确保实验有效性的关键前提")
            
    except Exception as e:
        logger.error(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 