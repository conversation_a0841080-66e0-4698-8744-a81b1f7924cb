#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SpatialVLA Visual Interpretation Analysis
==========================================

基于论文 "Towards Interpreting Visual Information Processing in Vision-Language Models" 
(https://arxiv.org/pdf/2410.07149) 的方法，将其适配到SpatialVLA模型上。

### Logit Lens 原理详解（针对SpatialVLA）

#### 1. SpatialVLA架构回顾
```
输入: "Pick up the coke can" + 图像
  ↓
Vision Encoder (预训练的视觉编码器)
  ↓ 
Cross-Attention (视觉-语言融合)
  ↓
Language Model Layers (多层Transformer) ← 我们分析的重点
  ↓
lm_head (线性层: hidden_size=4096 → vocab_size=259584)
  ↓
输出: 动作token概率分布，如 <loc1234>, <gripper_open> 等
```

#### 2. Logit Lens的核心思想
在正常推理时，只有最后一层的隐藏状态会通过lm_head产生最终输出。
但Logit Lens让我们可以"窥视"中间层在"思考"什么：

**例子**：假设我们分析Layer 15的某个patch位置
```python
# 正常推理流程
hidden_layer_15 = model.layers[15](...)     # [1, seq_len, 4096]
# ... 继续到Layer 31
final_hidden = model.layers[31](...)        # [1, seq_len, 4096] 
final_logits = model.lm_head(final_hidden)  # [1, seq_len, 259584]

# Logit Lens分析：直接用Layer 15的隐藏状态
patch_hidden = hidden_layer_15[0, patch_pos, :]  # [4096] 某个patch位置的隐藏向量
lens_logits = model.lm_head(patch_hidden)        # [259584] 该位置"想要"输出什么
lens_probs = softmax(lens_logits)                # 概率分布
top_tokens = torch.topk(lens_probs, 5)          # 前5个最可能的token
```

#### 3. 为什么这样做有意义？
- **lm_head是共享的**：同一个线性变换用于所有层
- **中间层的"半成品思考"**：Layer 15可能已经识别出"可乐罐"，但还没决定具体动作
- **演化过程**：我们可以看到从"物体识别"到"动作规划"的逐层演化

#### 4. SpatialVLA特定的调整
1. **视觉token位置识别**：SpatialVLA使用256个<image> token对应16x16 patches
2. **动作token词汇表**：除了普通文本token，还有位置token如<loc1234>、<seg001>等
3. **Cross-attention影响**：视觉信息通过cross-attention影响语言层的隐藏状态

#### 5. 实际应用
通过Logit Lens，我们可以回答：
- 在Layer 10，模型是否已经识别出"coke can"？
- 在Layer 20，模型开始规划"pick up"动作了吗？
- 不同patch（物体vs背景）在各层的表示有何不同？

核心功能：
1. Logit Lens - 分析视觉token在不同层的表示演化
2. Ablation Study - 验证物体信息的空间局部化
3. Attention Knockout - 分析信息提取机制

使用SimplerEnv环境获取真实机器人任务数据进行分析。

作者: Assistant (基于Clement Neo等人的工作)
日期: 2025-01-14
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn.functional as F
from datetime import datetime
from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
from PIL import Image
from torch.utils.data import DataLoader
from tqdm import tqdm
import argparse
import logging
from collections import defaultdict
import pickle
import copy
import random
import cv2

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根路径
project_root = "/home/<USER>/claude"
if project_root not in sys.path:
    sys.path.append(project_root)
    logger.info(f"项目根路径已添加到sys.path: {project_root}")

# 添加SpatialVLA路径
spatialvla_path = "/home/<USER>/claude/SpatialVLA"
if spatialvla_path not in sys.path:
    sys.path.append(spatialvla_path)
    logger.info(f"SpatialVLA路径已添加到sys.path: {spatialvla_path}")

# 导入SimplerEnv
try:
    import simpler_env
    from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
    from simpler_env.policies.spatialvla.spatialvla_model import SpatialVLAInference
    logger.info("成功导入SimplerEnv组件。")
except ImportError as e:
    logger.error(f"导入SimplerEnv失败: {e}")
    sys.exit(1)

# 导入SpatialVLA组件
try:
    from transformers import HfArgumentParser, set_seed
    from model import SpatialVLAConfig, SpatialVLAForConditionalGeneration, SpatialVLAProcessor, SpatialActionTokenizer
    logger.info("成功导入SpatialVLA模型组件。")
except ImportError as e:
    logger.error(f"导入SpatialVLA组件失败: {e}")
    sys.exit(1)

# 设置SAPIEN
try:
    import sapien.core as sapien
    sapien.render_config.rt_use_denoiser = False
    logger.info("SAPIEN配置成功。")
except ImportError:
    logger.warning("无法导入SAPIEN，某些功能可能受限")

import warnings
warnings.filterwarnings("ignore")


@dataclass
class VisualInterpretationConfig:
    """视觉解释分析配置"""
    
    # 模型配置
    model_path: str = "IPEC-COMMUNITY/spatialvla-4b-224-pt"
    device: str = "cuda:2"
    dtype: str = "bf16"
    
    # SimplerEnv配置
    task_name: str = "google_robot_pick_coke_can"
    seed: int = 1234
    policy_setup: str = "google_robot"
    action_scale: float = 1.0
    action_ensemble_temp: float = -0.8
    
    # 数据配置
    max_samples: int = 100
    
    # 输出配置
    output_dir: str = "./visual_interpretation_results"
    save_intermediate: bool = True
    
    # Logit Lens配置
    enable_logit_lens: bool = True
    target_layers: List[int] = field(default_factory=lambda: list(range(20, 32)))  # 后期层
    enable_enhanced_logit_lens: bool = True  # 新增：启用增强的Logit Lens可视化
    
    # Ablation配置
    enable_ablation: bool = True
    ablation_methods: List[str] = field(default_factory=lambda: [
        "object_tokens", "object_with_buffer", "random_tokens", "high_gradient_tokens"
    ])
    buffer_sizes: List[int] = field(default_factory=lambda: [1, 2])
    
    # Attention Knockout配置  
    enable_attention_knockout: bool = True
    layer_windows: List[Tuple[int, int]] = field(default_factory=lambda: [
        (1, 10),    # early
        (5, 14),    # early-mid
        (11, 20),   # mid
        (15, 24),   # mid-late
        (21, 31)    # late
    ])


class SpatialVLAVisualInterpreter:
    """SpatialVLA视觉解释分析器"""
    
    def __init__(self, config: VisualInterpretationConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # 创建输出目录
        os.makedirs(config.output_dir, exist_ok=True)
        
        # 设置随机种子
        set_seed(config.seed)
        
        # 初始化模型和处理器
        self._load_model()
        
        # SimplerEnv环境和SpatialVLA推理模型
        self.env = None
        self.spatialvla_model = None
        self.current_instruction = None
        
        # 结果存储
        self.results = {
            'logit_lens': [],
            'ablation': [],
            'attention_knockout': []
        }
        
        logger.info("SpatialVLA视觉解释分析器初始化完成")
    
    def _load_model(self):
        """加载SpatialVLA模型和处理器"""
        logger.info("正在加载SpatialVLA模型...")
        
        try:
            # 加载模型配置
            self.model_config = SpatialVLAConfig.from_pretrained(self.config.model_path)
            
            # 加载模型
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)
            
            self.model = SpatialVLAForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device,
                trust_remote_code=True
            )
            
            # 加载处理器
            self.processor = SpatialVLAProcessor.from_pretrained(
                self.config.model_path,
                trust_remote_code=True
            )
            
            # 设置为评估模式
            self.model.eval()
            
            # 获取模型架构信息
            self.num_layers = len(self.model.language_model.model.layers)
            self.hidden_size = self.model.language_model.config.hidden_size
            self.vocab_size = self.model.language_model.config.vocab_size
            
            logger.info(f"模型加载成功: {self.num_layers}层, 隐藏维度{self.hidden_size}, 词汇表大小{self.vocab_size}")
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            raise
    
    def extract_visual_tokens_and_positions(self, inputs: Dict[str, torch.Tensor]) -> List[int]:
        """提取视觉token的位置
        
        SpatialVLA的架构：
        - input_ids包含: [指令tokens] + [<image>] + [动作tokens] 
        - 真正的视觉特征在pixel_values中，通过vision encoder处理后注入到语言模型
        - 我们需要找到视觉特征被注入到的位置
        """
        input_ids = inputs['input_ids'][0]  # [seq_len]
        seq_len = len(input_ids)
        
        logger.info(f"序列长度: {seq_len}")
        logger.info(f"输入keys: {list(inputs.keys())}")
        
        # 找到<image> token的位置
        image_token_id = 257152  # <image> token
        image_positions = []
        
        for i, token_id in enumerate(input_ids):
            if token_id.item() == image_token_id:
                image_positions.append(i)
        
        logger.info(f"找到<image> token位置: {image_positions}")
        
        # 根据SpatialVLA的架构，视觉特征通常会替换或插入到<image> token位置
        # 但实际上，视觉特征可能在模型内部通过cross-attention处理
        # 我们需要分析模型的实际架构来确定视觉信息的处理位置
        
        # 方法1: 如果有pixel_values，说明视觉特征是独立处理的
        if 'pixel_values' in inputs:
            # 在这种情况下，我们需要找到视觉特征影响的token位置
            # 通常这些位置在<image> token附近或者在动作预测tokens位置
            
            # 策略：分析输出token，找到可能受视觉影响的位置
            # 通常是动作相关的tokens（位置、方向、抓取等）
            
            # 分析token类型来识别动作相关的位置
            action_related_positions = []
            
            for i, token_id in enumerate(input_ids):
                token_val = token_id.item()
                
                # 位置tokens: <loc####>
                if 256000 <= token_val <= 257000:
                    action_related_positions.append(i)
                # 分割tokens: <seg###>
                elif 257000 <= token_val <= 257100:
                    action_related_positions.append(i)
                # 其他特殊动作tokens
                elif token_val in [256208, 256984]:  # 我们之前看到的位置tokens
                    action_related_positions.append(i)
            
            # 如果找到动作相关位置，说明这些可能受视觉影响
            if action_related_positions:
                logger.info(f"找到动作相关token位置: {action_related_positions}")
                # 为了Logit Lens分析，我们需要更多位置
                # 扩展到<image> token之后的区域
                extended_positions = action_related_positions.copy()
                
                # 添加<image> token之后的一些位置（假设视觉信息在这里处理）
                for img_pos in image_positions:
                    for offset in range(1, 20):  # <image> token后的20个位置
                        pos = img_pos + offset
                        if pos < seq_len and pos not in extended_positions:
                            extended_positions.append(pos)
                
                return extended_positions[:50]  # 限制数量
        
        # 方法2: 如果没有pixel_values，或者上面的方法没找到足够的位置
        # 假设视觉信息在序列的特定区域
        
        # 基于序列结构的假设分析
        visual_positions = []
        
        # 添加<image> token位置
        visual_positions.extend(image_positions)
        
        # 添加序列后半部分（假设包含视觉处理结果）
        seq_mid = seq_len // 2
        for i in range(seq_mid, min(seq_mid + 50, seq_len)):
            if i not in visual_positions:
                visual_positions.append(i)
        
        logger.info(f"最终视觉token位置数量: {len(visual_positions)}")
        if len(visual_positions) > 0:
            logger.info(f"视觉token位置范围: {min(visual_positions)} - {max(visual_positions)}")
            logger.info(f"示例位置的token: {[(pos, input_ids[pos].item()) for pos in visual_positions[:5]]}")
        
        return visual_positions
    
    def run_logit_lens_analysis(self, inputs: Dict[str, torch.Tensor], 
                               instruction: str) -> Dict[str, Any]:
        """运行Logit Lens分析
        
        分析视觉token在不同层的表示如何演化向可解释的文本token
        """
        if not self.config.enable_logit_lens:
            return {}
        
        logger.info("开始Logit Lens分析...")
        
        try:
            with torch.no_grad():
                # 获取所有层的隐藏状态
                outputs = self.model(**inputs, output_hidden_states=True, return_dict=True)
                hidden_states = outputs.hidden_states  # List[Tensor], 每个是 [batch, seq_len, hidden_size]
                
                # 获取视觉token位置
                visual_positions = self.extract_visual_tokens_and_positions(inputs)
                
                # 获取语言模型的unembedding矩阵 (词汇表映射)
                lm_head = self.model.language_model.lm_head
                
                logit_lens_results = []
                
                for layer_idx in self.config.target_layers:
                    if layer_idx >= len(hidden_states):
                        continue
                        
                    layer_hidden = hidden_states[layer_idx]  # [batch, seq_len, hidden_size]
                    
                    # 对每个视觉token位置进行logit lens (限制数量以节省时间)
                    max_positions = min(10, len(visual_positions))
                    for i in range(max_positions):
                        pos_idx = visual_positions[i]
                        
                        if pos_idx >= layer_hidden.shape[1]:
                            continue
                        
                        token_hidden = layer_hidden[0, pos_idx, :]  # [hidden_size]
                        
                        # 确保数据类型一致
                        if token_hidden.dtype != lm_head.weight.dtype:
                            token_hidden = token_hidden.to(dtype=lm_head.weight.dtype)
                        
                        # 投影到词汇空间
                        logits = lm_head(token_hidden.unsqueeze(0))  # [1, vocab_size]
                        probs = F.softmax(logits, dim=-1)
                        
                        # 获取top-k tokens
                        top_k = 5
                        top_probs, top_indices = torch.topk(probs, top_k, dim=-1)
                        
                        # 解码top tokens
                        top_tokens = []
                        for j in range(top_k):
                            token_id = top_indices[0, j].item()
                            try:
                                token_text = self.processor.tokenizer.decode([token_id])
                                prob = top_probs[0, j].item()
                                top_tokens.append({
                                    'token': token_text,
                                    'probability': prob,
                                    'token_id': token_id
                                })
                            except Exception as decode_error:
                                logger.warning(f"Token解码失败 {token_id}: {decode_error}")
                                continue
                        
                        if top_tokens:  # 只在有有效tokens时添加结果
                            logit_lens_results.append({
                                'layer': layer_idx,
                                'position': pos_idx,
                                'top_tokens': top_tokens,
                                'instruction': instruction
                            })
                
                logger.info(f"Logit Lens分析完成，分析了{len(logit_lens_results)}个位置")
                
                return {
                    'results': logit_lens_results,
                    'visual_positions': visual_positions,  # 移除[:10]限制，使用完整的visual_positions
                    'num_layers_analyzed': len([l for l in self.config.target_layers if l < len(hidden_states)]),
                    'hidden_states': hidden_states,  # 新增：保存隐藏状态用于增强可视化
                    'instruction': instruction
                }
                
        except Exception as e:
            logger.error(f"Logit Lens分析中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return {
                'results': [],
                'visual_positions': [],
                'num_layers_analyzed': 0,
                'error': str(e)
            }
    
    def run_ablation_study(self, inputs: Dict[str, torch.Tensor], 
                          instruction: str) -> Dict[str, Any]:
        """运行消融研究
        
        通过替换特定位置的视觉tokens来验证物体信息的空间局部化
        """
        if not self.config.enable_ablation:
            return {}
        
        logger.info("开始消融研究分析...")
        
        # 获取原始输出作为baseline
        with torch.no_grad():
            original_outputs = self.model(**inputs, return_dict=True)
            original_logits = original_outputs.logits
            
        visual_positions = self.extract_visual_tokens_and_positions(inputs)
        
        # 计算平均视觉token (用于替换)
        mean_visual_token = self._compute_mean_visual_token()
        
        ablation_results = []
        
        for method in self.config.ablation_methods:
            
            if method == "object_tokens":
                # 假设物体在图像中心区域 (简化假设)
                center_positions = self._get_center_positions(visual_positions)
                ablated_positions = center_positions
                
            elif method == "object_with_buffer":
                for buffer_size in self.config.buffer_sizes:
                    center_positions = self._get_center_positions(visual_positions)
                    ablated_positions = self._expand_positions_with_buffer(
                        center_positions, visual_positions, buffer_size
                    )
                    
                    # 执行消融
                    accuracy_drop = self._perform_ablation(
                        inputs, ablated_positions, mean_visual_token, 
                        original_logits, instruction,
                        ablation_method=method
                    )
                    
                    ablation_results.append({
                        'method': f'{method}_buffer_{buffer_size}',
                        'ablated_positions': ablated_positions,
                        'accuracy_drop': accuracy_drop,
                        'num_ablated_tokens': len(ablated_positions)
                    })
                continue
                
            elif method == "random_tokens":
                # 随机选择tokens进行消融
                num_random = min(20, len(visual_positions) // 4)  
                ablated_positions = np.random.choice(
                    visual_positions, num_random, replace=False
                ).tolist()
                
            elif method == "high_gradient_tokens":
                # 使用梯度信息选择重要tokens (简化实现)
                ablated_positions = self._get_high_gradient_positions(
                    inputs, visual_positions, instruction
                )
            
            else:
                continue
            
            # 执行消融
            accuracy_drop = self._perform_ablation(
                inputs, ablated_positions, mean_visual_token, 
                original_logits, instruction,
                ablation_method=method
            )
            
            ablation_results.append({
                'method': method,
                'ablated_positions': ablated_positions,
                'accuracy_drop': accuracy_drop,
                'num_ablated_tokens': len(ablated_positions)
            })
        
        logger.info(f"消融研究完成，测试了{len(ablation_results)}种方法")
        
        return {
            'results': ablation_results,
            'visual_positions': visual_positions,
            'instruction': instruction
        } 
    
    def _compute_mean_visual_token(self) -> torch.Tensor:
        """计算平均视觉token，用于消融实验中的替换"""
        # 这里需要从大量图像中计算平均visual token
        # 简化实现：返回零向量
        return torch.zeros(self.hidden_size, device=self.device, dtype=self.model.dtype)
    
    def _get_center_positions(self, visual_positions: List[int]) -> List[int]:
        """获取图像中心区域的token位置 (假设物体在中心)"""
        # 假设视觉tokens按patch顺序排列 (如24x24 grid)
        patch_size = int(np.sqrt(len(visual_positions)))
        center_start = patch_size // 4
        center_end = 3 * patch_size // 4
        
        center_positions = []
        for i in range(center_start, center_end):
            for j in range(center_start, center_end):
                pos = i * patch_size + j
                if pos < len(visual_positions):
                    center_positions.append(visual_positions[pos])
        
        return center_positions
    
    def _expand_positions_with_buffer(self, positions: List[int], 
                                     all_positions: List[int], 
                                     buffer_size: int) -> List[int]:
        """扩展位置列表，添加周围的buffer区域"""
        patch_size = int(np.sqrt(len(all_positions)))
        expanded = set(positions)
        
        for pos in positions:
            if pos >= len(all_positions):
                continue
            row, col = divmod(pos, patch_size)
            
            # 添加周围的positions
            for dr in range(-buffer_size, buffer_size + 1):
                for dc in range(-buffer_size, buffer_size + 1):
                    new_row, new_col = row + dr, col + dc
                    if 0 <= new_row < patch_size and 0 <= new_col < patch_size:
                        new_pos = new_row * patch_size + new_col
                        if new_pos < len(all_positions):
                            expanded.add(all_positions[new_pos])
        
        return list(expanded)
    
    def _get_high_gradient_positions(self, inputs: Dict[str, torch.Tensor], 
                                    visual_positions: List[int], 
                                    instruction: str) -> List[int]:
        """使用梯度信息获取重要的视觉token位置"""
        try:
            # 需要计算对输出的梯度
            inputs_copy = {k: v.clone().requires_grad_(True) if v.dtype.is_floating_point else v 
                          for k, v in inputs.items()}
            
            outputs = self.model(**inputs_copy, return_dict=True)
            
            # 假设我们关心最后一个token的预测
            last_token_logits = outputs.logits[0, -1, :]  # [vocab_size]
            target_logit = last_token_logits.max()  # 最高logit
            
            # 计算梯度
            if hasattr(self.model, 'get_input_embeddings'):
                embeddings = self.model.get_input_embeddings()
                input_embeds = embeddings(inputs_copy['input_ids'])
                
                # 添加allow_unused=True来避免梯度计算错误
                grad_outputs = torch.autograd.grad(
                    target_logit, input_embeds, 
                    retain_graph=True, 
                    create_graph=False,
                    allow_unused=True
                )[0]  # [batch, seq_len, hidden_size]
                
                if grad_outputs is not None:
                    # 计算每个位置的梯度范数
                    grad_norms = torch.norm(grad_outputs[0], dim=-1)  # [seq_len]
                    
                    # 选择梯度最大的视觉token位置
                    visual_grad_norms = [(pos, grad_norms[pos].item()) for pos in visual_positions 
                                       if pos < len(grad_norms)]
                    visual_grad_norms.sort(key=lambda x: x[1], reverse=True)
                    
                    # 返回前20个最重要的位置
                    return [pos for pos, _ in visual_grad_norms[:20]]
            
        except Exception as e:
            logger.warning(f"梯度计算失败: {e}")
            
        # 如果无法计算梯度，返回随机位置
        np.random.seed(self.config.seed)
        return np.random.choice(visual_positions, min(20, len(visual_positions)), replace=False).tolist()
    
    def _perform_ablation(self, inputs: Dict[str, torch.Tensor], 
                         ablated_positions: List[int],
                         mean_token: torch.Tensor,
                         original_logits: torch.Tensor,
                         instruction: str,
                         ablation_method: str = "unknown",
                         image_for_vis: Optional[np.ndarray] = None,
                         debug_patch_indices: Optional[List[int]] = None,
                         original_region_mask: Optional[np.ndarray] = None) -> float:
        """执行消融实验并计算性能下降
        
        对于SpatialVLA，我们需要修改pixel_values而不是input tokens
        现在增加可视化功能来显示消融效果，并严格限制消融区域
        """
        
        try:
            # 创建修改后的inputs
            inputs_ablated = copy.deepcopy(inputs)
            
            # SpatialVLA的消融：修改pixel_values中的特定区域
            if 'pixel_values' in inputs_ablated:
                pixel_values = inputs_ablated['pixel_values'].clone()  # [1, 3, H, W]
                original_pixel_values = pixel_values.clone()  # 保存原始值用于可视化
                
                # 确保数据类型兼容性 - 转换为float32进行处理
                if pixel_values.dtype in [torch.bfloat16, torch.float16]:
                    pixel_values = pixel_values.float()
                    original_pixel_values = original_pixel_values.float()
                
                C, H, W = pixel_values.shape[1], pixel_values.shape[2], pixel_values.shape[3]
                
                # 将ablated_positions转换为图像空间的遮挡
                if len(ablated_positions) > 0:
                    # 使用debug_patch_indices（如果提供）或者从ablated_positions推导
                    if debug_patch_indices is not None:
                        patch_indices_to_use = debug_patch_indices
                    else:
                        # 回退到原来的方法
                        patch_indices_to_use = ablated_positions
                    
                    # 创建基础遮挡掩码
                    ablation_mask = self._create_ablation_mask_from_patches(
                        patch_indices_to_use, H, W, patch_size=16
                    )
                    
                    # 关键修复：如果提供了原始区域mask，则严格限制消融区域
                    if original_region_mask is not None:
                        # 将原始区域mask缩放到模型图像尺寸
                        region_mask_resized = self._resize_mask_to_model_size(
                            original_region_mask, H, W
                        )
                        region_mask_tensor = torch.from_numpy(region_mask_resized).to(ablation_mask.device)
                        
                        # 取交集：只在指定区域内进行消融
                        ablation_mask = ablation_mask & region_mask_tensor
                        
                        logger.info(f"消融方法: {ablation_method}")
                        logger.info(f"  - 原始patch mask覆盖: {torch.sum(self._create_ablation_mask_from_patches(patch_indices_to_use, H, W, patch_size=16)).item()} 像素")
                        logger.info(f"  - 区域限制后覆盖: {torch.sum(ablation_mask).item()} 像素")
                        logger.info(f"  - 限制比例: {torch.sum(ablation_mask).item() / max(1, torch.sum(self._create_ablation_mask_from_patches(patch_indices_to_use, H, W, patch_size=16)).item()) * 100:.1f}%")
                    
                    # 应用消融（这里使用均值遮挡）
                    pixel_mean = pixel_values.mean(dim=[2, 3], keepdim=True)  # [1, 3, 1, 1]
                    
                    # 遮挡指定区域
                    for c in range(C):
                        pixel_values[0, c][ablation_mask] = pixel_mean[0, c, 0, 0]
                    
                    # 将修改后的值转换回原始数据类型
                    if inputs_ablated['pixel_values'].dtype in [torch.bfloat16, torch.float16]:
                        pixel_values = pixel_values.to(dtype=inputs_ablated['pixel_values'].dtype)
                    
                    inputs_ablated['pixel_values'] = pixel_values
                    
                    # 可视化消融效果
                    if image_for_vis is not None:
                        self._visualize_ablation_effect(
                            image_for_vis, ablation_mask, patch_indices_to_use, 
                            ablation_method, original_pixel_values, pixel_values
                        )
            
            # 获取修改后的输出
            with torch.no_grad():
                ablated_outputs = self.model(**inputs_ablated, return_dict=True)
                ablated_logits = ablated_outputs.logits
                
                # 计算最后token位置的预测变化
                original_probs = F.softmax(original_logits[0, -1, :], dim=-1)
                ablated_probs = F.softmax(ablated_logits[0, -1, :], dim=-1)
                
                # 使用KL散度衡量分布变化
                kl_div = F.kl_div(ablated_probs.log(), original_probs, reduction='sum').item()
                
                # 或者使用top-1预测的变化
                original_pred = torch.argmax(original_logits[0, -1, :])
                ablated_pred = torch.argmax(ablated_logits[0, -1, :])
                
                # 计算更细粒度的性能影响
                # 1. 预测token是否改变
                prediction_changed = (original_pred != ablated_pred).item()
                
                # 2. 原始预测的概率下降
                original_top_prob = original_probs[original_pred].item()
                ablated_top_prob = ablated_probs[original_pred].item()
                prob_drop = original_top_prob - ablated_top_prob
                
                # 3. 综合影响分数
                if prediction_changed:
                    accuracy_drop = min(1.0, 0.5 + kl_div / 10.0)  # 预测改变是重大影响
                else:
                    accuracy_drop = min(0.8, prob_drop + kl_div / 20.0)  # 概率下降 + KL散度
                
                logger.info(f"消融方法: {ablation_method}")
                logger.info(f"  - 预测改变: {prediction_changed}")
                logger.info(f"  - 概率下降: {prob_drop:.4f}")
                logger.info(f"  - KL散度: {kl_div:.4f}")
                logger.info(f"  - 综合影响: {accuracy_drop:.4f}")
                
                return accuracy_drop
                
        except Exception as e:
            logger.warning(f"消融实验执行失败: {e}")
            return 0.0

    def _create_ablation_mask_from_patches(self, patch_indices: List[int], 
                                         height: int, width: int, 
                                         patch_size: int = 16) -> torch.Tensor:
        """从patch索引创建消融掩码"""
        patches_h = patches_w = patch_size
        patch_height = height / patches_h
        patch_width = width / patches_w
        
        mask = torch.zeros((height, width), dtype=torch.bool)
        
        for patch_id in patch_indices:
            if patch_id >= patch_size * patch_size:
                continue
            
            patch_row = patch_id // patches_w
            patch_col = patch_id % patches_w
            
            y_start = int(patch_row * patch_height)
            y_end = int(min(y_start + patch_height, height))
            x_start = int(patch_col * patch_width)
            x_end = int(min(x_start + patch_width, width))
            
            mask[y_start:y_end, x_start:x_end] = True
        
        return mask

    def _visualize_ablation_effect(self, original_image: np.ndarray, 
                                 ablation_mask: torch.Tensor,
                                 ablated_positions: List[int],
                                 method_name: str,
                                 original_pixel_values: torch.Tensor,
                                 ablated_pixel_values: torch.Tensor):
        """可视化消融效果"""
        
        # 创建时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建可视化目录
        vis_dir = os.path.join(self.config.output_dir, "ablation_visualization")
        os.makedirs(vis_dir, exist_ok=True)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle(f'Ablation Visualization: {method_name}', fontsize=16)
        
        # 1. 原始图像
        axes[0, 0].imshow(original_image)
        axes[0, 0].set_title('Original Image')
        axes[0, 0].axis('off')
        
        # 2. 消融掩码
        axes[0, 1].imshow(original_image)
        mask_overlay = ablation_mask.cpu().numpy()
        axes[0, 1].imshow(mask_overlay, alpha=0.5, cmap='Reds')
        axes[0, 1].set_title(f'Ablation Mask\n{len(ablated_positions)} patches affected')
        axes[0, 1].axis('off')
        
        # 3. 消融后图像
        # 从tensor转换为numpy显示 - 修复BFloat16问题
        try:
            # 确保转换为float32进行处理
            ablated_img_tensor = ablated_pixel_values[0].float().permute(1, 2, 0)  # [H, W, C]
            
            # 转换为numpy并确保数据范围正确
            ablated_img_numpy = ablated_img_tensor.cpu().numpy()
            
            # 归一化到0-255范围
            if ablated_img_numpy.max() <= 1.0:
                ablated_img_numpy = (ablated_img_numpy * 255).astype(np.uint8)
            else:
                ablated_img_numpy = np.clip(ablated_img_numpy, 0, 255).astype(np.uint8)
            
            axes[0, 2].imshow(ablated_img_numpy)
            axes[0, 2].set_title('Ablated Image')
            axes[0, 2].axis('off')
            
        except Exception as e:
            logger.warning(f"消融图像显示失败: {e}")
            axes[0, 2].text(0.5, 0.5, f'Visualization Error:\n{str(e)}', 
                           ha='center', va='center', transform=axes[0, 2].transAxes)
            axes[0, 2].set_title('Ablated Image (Error)')
            axes[0, 2].axis('off')
        
        # 4. Patch网格可视化
        axes[1, 0].imshow(original_image)
        height, width = original_image.shape[:2]
        patch_height = height / 16
        patch_width = width / 16
        
        # 绘制网格
        for i in range(1, 16):
            y = i * patch_height
            axes[1, 0].axhline(y=y, color='white', linestyle='-', alpha=0.3, linewidth=1)
        for j in range(1, 16):
            x = j * patch_width
            axes[1, 0].axvline(x=x, color='white', linestyle='-', alpha=0.3, linewidth=1)
        
        # 高亮被消融的patches
        for patch_id in ablated_positions[:20]:  # 限制显示数量
            if patch_id >= 256:
                continue
            patch_row = patch_id // 16
            patch_col = patch_id % 16
            y_start = patch_row * patch_height
            x_start = patch_col * patch_width
            
            rect = plt.Rectangle((x_start, y_start), patch_width, patch_height,
                               linewidth=2, edgecolor='red', facecolor='red', alpha=0.4)
            axes[1, 0].add_patch(rect)
        
        axes[1, 0].set_title(f'Affected Patches (16x16 grid)\nRed: {len(ablated_positions)} patches')
        axes[1, 0].axis('off')
        
        # 5. 差异图像
        try:
            if original_pixel_values.shape == ablated_pixel_values.shape:
                # 确保使用float32进行计算
                orig_float = original_pixel_values.float()
                ablated_float = ablated_pixel_values.float()
                diff_tensor = torch.abs(orig_float - ablated_float).mean(dim=1)[0]  # [H, W]
                diff_numpy = diff_tensor.cpu().numpy()
                
                im = axes[1, 1].imshow(diff_numpy, cmap='hot')
                axes[1, 1].set_title('Difference Map')
                axes[1, 1].axis('off')
                plt.colorbar(im, ax=axes[1, 1], fraction=0.046, pad=0.04)
            else:
                axes[1, 1].text(0.5, 0.5, 'Shape mismatch\nfor difference', 
                               ha='center', va='center', transform=axes[1, 1].transAxes)
                axes[1, 1].set_title('Difference Map (Error)')
                axes[1, 1].axis('off')
                
        except Exception as e:
            logger.warning(f"差异图像计算失败: {e}")
            axes[1, 1].text(0.5, 0.5, f'Diff Calculation Error:\n{str(e)}', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Difference Map (Error)')
            axes[1, 1].axis('off')
        
        # 6. 统计信息
        axes[1, 2].text(0.1, 0.9, f'Method: {method_name}', fontsize=12, transform=axes[1, 2].transAxes)
        axes[1, 2].text(0.1, 0.8, f'Patches affected: {len(ablated_positions)}', fontsize=12, transform=axes[1, 2].transAxes)
        axes[1, 2].text(0.1, 0.7, f'Mask coverage: {ablation_mask.sum().item() / ablation_mask.numel() * 100:.1f}%', 
                        fontsize=12, transform=axes[1, 2].transAxes)
        
        # 显示一些patch ID
        patch_text = f'Patch IDs: {ablated_positions[:10]}'
        if len(ablated_positions) > 10:
            patch_text += f'... (+{len(ablated_positions) - 10} more)'
        axes[1, 2].text(0.1, 0.6, patch_text, fontsize=10, transform=axes[1, 2].transAxes)
        
        axes[1, 2].set_xlim(0, 1)
        axes[1, 2].set_ylim(0, 1)
        axes[1, 2].axis('off')
        
        plt.tight_layout()
        
        # 保存可视化
        save_path = os.path.join(vis_dir, f"ablation_{method_name}_{timestamp}.png")
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"消融可视化已保存: {save_path}")
    
    def run_attention_knockout(self, inputs: Dict[str, torch.Tensor], 
                              instruction: str) -> Dict[str, Any]:
        """运行注意力阻断分析
        
        通过阻断特定tokens间的注意力流来分析信息提取机制
        """
        if not self.config.enable_attention_knockout:
            return {}
        
        logger.info("开始注意力阻断分析...")
        
        visual_positions = self.extract_visual_tokens_and_positions(inputs)
        
        # 获取原始输出作为baseline
        with torch.no_grad():
            original_outputs = self.model(**inputs, return_dict=True)
            original_logits = original_outputs.logits[0, -1, :]  # 最后token的logits
            original_prediction = torch.argmax(original_logits)
        
        knockout_results = []
        
        for window_name, (start_layer, end_layer) in zip(
            ['early', 'early-mid', 'mid', 'mid-late', 'late'], 
            self.config.layer_windows
        ):
            
            # 测试不同的注意力阻断模式
            blocking_patterns = [
                ('object_to_last', self._get_center_positions(visual_positions), [-1]),
                ('visual_to_last', visual_positions, [-1]),
                ('all_to_visual_last_row', list(range(len(visual_positions))), 
                 visual_positions[-24:] if len(visual_positions) >= 24 else visual_positions[-5:])
            ]
            
            for pattern_name, from_positions, to_positions in blocking_patterns:
                
                try:
                    # 执行注意力阻断
                    performance_ratio = self._perform_attention_knockout(
                        inputs, from_positions, to_positions, 
                        start_layer, end_layer, original_prediction
                    )
                    
                    knockout_results.append({
                        'window': window_name,
                        'pattern': pattern_name,
                        'layers': (start_layer, end_layer),
                        'from_positions': from_positions[:5],  # 只保存前5个用于记录
                        'to_positions': to_positions,
                        'performance_ratio': performance_ratio
                    })
                    
                except Exception as e:
                    logger.warning(f"注意力阻断失败 {pattern_name} in {window_name}: {e}")
                    continue
        
        logger.info(f"注意力阻断分析完成，测试了{len(knockout_results)}种模式")
        
        return {
            'results': knockout_results,
            'visual_positions': visual_positions,
            'instruction': instruction
        }
    
    def _perform_attention_knockout(self, inputs: Dict[str, torch.Tensor],
                                   from_positions: List[int], 
                                   to_positions: List[int],
                                   start_layer: int, 
                                   end_layer: int,
                                   original_prediction: torch.Tensor) -> float:
        """执行注意力阻断并计算性能保持比例"""
        
        # 注意：这是一个简化实现，实际的注意力阻断需要深入修改模型的attention机制
        # 这里提供框架代码
        
        with torch.no_grad():
            try:
                # 创建修改版本的模型调用
                # 实际实现需要hook到attention计算过程中
                
                # 简化版：假设我们可以修改attention mask
                modified_inputs = copy.deepcopy(inputs)
                
                # 这里需要实际的attention mask修改逻辑
                # 由于SpatialVLA的复杂性，这里提供简化的性能评估
                
                outputs = self.model(**modified_inputs, return_dict=True)
                modified_logits = outputs.logits[0, -1, :]
                modified_prediction = torch.argmax(modified_logits)
                
                # 计算性能保持比例
                performance_ratio = 1.0 if modified_prediction == original_prediction else 0.8
                
                return performance_ratio
                
            except Exception as e:
                logger.warning(f"注意力阻断执行失败: {e}")
                return 1.0  # 默认无影响
    
    def analyze_sample(self, use_env_data: bool = True) -> Dict[str, Any]:
        """分析样本
        
        Args:
            use_env_data: 是否使用环境数据（True）还是静态图片（False，保持向后兼容）
        """
        results = {}
        
        if use_env_data:
            # 使用SimplerEnv环境数据
            logger.info("=== 开始SimplerEnv环境分析 ===")
            
            # 设置环境
            obs, reset_info = self.setup_simpler_env()
            
            # 创建SpatialVLA推理模型
            if not self.create_spatialvla_model():
                raise Exception("SpatialVLA推理模型创建失败")
            
            # 获取overhead camera数据
            rgb_overhead, seg_overhead, success = self.get_overhead_camera_data(obs)
            if not success:
                raise Exception("无法获取overhead camera数据")
            
            # 检测关键物体
            key_objects = self.detect_key_objects(seg_overhead, rgb_overhead)
            
            # 将numpy数组转换为PIL Image
            image = Image.fromarray(rgb_overhead)
            instruction = self.current_instruction
            
            logger.info(f"使用环境数据 - 图像尺寸: {image.size}, 指令: {instruction}")
            
        else:
            # 保持原有的静态图片方式（向后兼容）
            logger.error("静态图片模式已被移除，请使用SimplerEnv环境数据")
            raise NotImplementedError("请使用use_env_data=True")
        
        # 预处理输入
        try:
            inputs = self.processor(
                text=instruction,
                images=image,
                return_tensors="pt"
            )
            
            # 移动到正确设备并确保数据类型一致
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            target_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)
            
            # 确保所有输入tensor都使用正确的数据类型和设备
            for key, value in inputs.items():
                if torch.is_tensor(value):
                    if value.dtype.is_floating_point:
                        inputs[key] = value.to(device=self.device, dtype=target_dtype)
                    else:
                        inputs[key] = value.to(device=self.device)
                else:
                    inputs[key] = value
            
            logger.info(f"输入预处理完成，数据类型已转换为: {target_dtype}")
            logger.info(f"输入keys: {list(inputs.keys())}")
            if 'input_ids' in inputs:
                logger.info(f"序列长度: {inputs['input_ids'].shape[1]}")
            if 'pixel_values' in inputs:
                logger.info(f"pixel_values类型: {inputs['pixel_values'].dtype}, 设备: {inputs['pixel_values'].device}")
            if 'intrinsic' in inputs:
                logger.info(f"intrinsic类型: {inputs['intrinsic'].dtype}, 设备: {inputs['intrinsic'].device}")
            
        except Exception as e:
            logger.error(f"输入预处理失败: {e}")
            raise
        
        # 运行各项分析
        with torch.no_grad():
            
            # 0. 首先演示Logit Lens原理（如果启用了Logit Lens）
            if self.config.enable_logit_lens:
                logger.info("开始Logit Lens原理演示...")
                try:
                    self.demonstrate_logit_lens_principle(inputs, instruction)
                    logger.info("✅ Logit Lens原理演示完成")
                except Exception as e:
                    logger.error(f"❌ Logit Lens原理演示失败: {e}")
            
            # 1. Logit Lens分析
            if self.config.enable_logit_lens:
                logger.info("开始Logit Lens分析...")
                try:
                    logit_results = self.run_logit_lens_analysis(inputs, instruction)
                    results['logit_lens'] = logit_results
                    logger.info("✅ Logit Lens分析完成")
                except Exception as e:
                    logger.error(f"❌ Logit Lens分析失败: {e}")
                    results['logit_lens'] = {}
            
            # 2. 消融研究（使用检测到的物体区域）
            if self.config.enable_ablation:
                logger.info("开始空间区域消融研究...")
                try:
                    if use_env_data and seg_overhead is not None:
                        ablation_results = self.run_spatial_ablation_study(inputs, instruction, key_objects, rgb_overhead.shape[:2])
                    else:
                        ablation_results = self.run_ablation_study(inputs, instruction)
                    results['ablation'] = ablation_results
                    logger.info("✅ 消融研究完成")
                except Exception as e:
                    logger.error(f"❌ 消融研究失败: {e}")
                    results['ablation'] = {}
            
            # 3. 注意力阻断分析
            if self.config.enable_attention_knockout:
                logger.info("开始注意力阻断分析...")
                try:
                    attention_results = self.run_attention_knockout(inputs, instruction)
                    results['attention_knockout'] = attention_results
                    logger.info("✅ 注意力阻断分析完成")
                except Exception as e:
                    logger.error(f"❌ 注意力阻断分析失败: {e}")
                    results['attention_knockout'] = {}
        
        # 4. 增强的Logit Lens可视化 (如果有相关数据)
        if (self.config.enable_logit_lens and 
            self.config.enable_enhanced_logit_lens and  # 新增条件检查
            results.get('logit_lens') and 
            use_env_data and 
            rgb_overhead is not None):
            
            logger.info("开始创建增强的Logit Lens可视化...")
            try:
                # 创建增强可视化的保存路径
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                enhanced_viz_path = os.path.join(
                    self.config.output_dir, 
                    f"enhanced_logit_lens_{timestamp}.png"
                )
                
                self.create_comprehensive_logit_lens_visualization(
                    results, key_objects, rgb_overhead, enhanced_viz_path
                )
                logger.info("✅ 增强的Logit Lens可视化完成")
                
            except Exception as e:
                logger.error(f"❌ 增强的Logit Lens可视化失败: {e}")
                import traceback
                traceback.print_exc()
        
        # 清理环境
        if use_env_data and self.env:
            self.env.close()
            del self.spatialvla_model
            self.spatialvla_model = None
            self.env = None
        
        logger.info("样本分析完成")
        return results
    
    def create_visualization(self, results: Dict[str, Any], 
                           save_path: str):
        """创建分析结果的可视化"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('SpatialVLA Visual Interpretation Analysis', fontsize=16)
        
        # 1. Logit Lens可视化
        if 'logit_lens' in results and results['logit_lens']:
            ax = axes[0, 0]
            self._plot_logit_lens(results['logit_lens'], ax)
            ax.set_title('Logit Lens: Token Evolution Across Layers')
        
        # 2. 消融研究可视化
        if 'ablation' in results and results['ablation']:
            ax = axes[0, 1]
            self._plot_ablation_results(results['ablation'], ax)
            ax.set_title('Ablation Study: Performance Drop by Method')
        
        # 3. 注意力阻断可视化
        if 'attention_knockout' in results and results['attention_knockout']:
            ax = axes[1, 0]
            self._plot_attention_knockout(results['attention_knockout'], ax)
            ax.set_title('Attention Knockout: Performance by Layer Window')
        
        # 4. 综合分析
        ax = axes[1, 1]
        self._plot_summary(results, ax)
        ax.set_title('Analysis Summary')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"可视化结果已保存到: {save_path}")
    
    def _plot_logit_lens(self, logit_lens_data: Dict[str, Any], ax):
        """绘制Logit Lens结果"""
        if not logit_lens_data or 'results' not in logit_lens_data:
            ax.text(0.5, 0.5, 'No Logit Lens Data', ha='center', va='center')
            return
        
        results = logit_lens_data['results']
        
        # 按层分组统计top token
        layer_tokens = defaultdict(list)
        for result in results:
            layer = result['layer']
            top_token = result['top_tokens'][0]['token']  # 最高概率的token
            layer_tokens[layer].append(top_token)
        
        # 绘制层与token的关系
        layers = sorted(layer_tokens.keys())
        for i, layer in enumerate(layers[:10]):  # 限制显示层数
            tokens = layer_tokens[layer][:5]  # 前5个tokens
            y_pos = len(layers) - i - 1
            ax.text(0, y_pos, f'Layer {layer}:', fontsize=10, weight='bold')
            for j, token in enumerate(tokens):
                ax.text(0.2 + j * 0.15, y_pos, token.strip(), fontsize=8)
        
        ax.set_xlim(0, 1)
        ax.set_ylim(-0.5, len(layers) - 0.5)
        ax.set_ylabel('Layer (top to bottom: early to late)')
        ax.set_xticks([])
    
    def _plot_ablation_results(self, ablation_data: Dict[str, Any], ax):
        """绘制消融研究结果"""
        if not ablation_data or 'results' not in ablation_data:
            ax.text(0.5, 0.5, 'No Ablation Data', ha='center', va='center')
            return
        
        results = ablation_data['results']
        
        methods = [r['method'] for r in results]
        accuracy_drops = [r['accuracy_drop'] for r in results]
        
        bars = ax.bar(range(len(methods)), accuracy_drops, alpha=0.7)
        ax.set_xticks(range(len(methods)))
        ax.set_xticklabels(methods, rotation=45, ha='right')
        ax.set_ylabel('Accuracy Drop')
        ax.set_ylim(0, 1)
        
        # 添加数值标签
        for bar, drop in zip(bars, accuracy_drops):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                   f'{drop:.2f}', ha='center', va='bottom')
    
    def _plot_attention_knockout(self, attention_data: Dict[str, Any], ax):
        """绘制注意力阻断结果"""
        if not attention_data or 'results' not in attention_data:
            ax.text(0.5, 0.5, 'No Attention Knockout Data', ha='center', va='center')
            return
        
        results = attention_data['results']
        
        # 按window分组
        windows = ['early', 'early-mid', 'mid', 'mid-late', 'late']
        patterns = ['object_to_last', 'visual_to_last', 'all_to_visual_last_row']
        
        # 创建热力图数据
        heatmap_data = np.ones((len(patterns), len(windows)))
        
        for result in results:
            try:
                window_idx = windows.index(result['window'])
                pattern_idx = patterns.index(result['pattern'])
                heatmap_data[pattern_idx, window_idx] = result['performance_ratio']
            except (ValueError, KeyError):
                continue
        
        im = ax.imshow(heatmap_data, cmap='RdYlBu', vmin=0, vmax=1)
        
        ax.set_xticks(range(len(windows)))
        ax.set_xticklabels(windows)
        ax.set_yticks(range(len(patterns)))
        ax.set_yticklabels([p.replace('_', ' ') for p in patterns])
        
        # 添加数值
        for i in range(len(patterns)):
            for j in range(len(windows)):
                ax.text(j, i, f'{heatmap_data[i, j]:.2f}', 
                       ha='center', va='center', color='black')
        
        ax.set_xlabel('Layer Windows')
        ax.set_ylabel('Attention Patterns')
        
        # 添加colorbar
        plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
    
    def _plot_summary(self, results: Dict[str, Any], ax):
        """绘制分析摘要"""
        summary_text = []
        
        if 'logit_lens' in results and results['logit_lens']:
            logit_results = results['logit_lens']
            summary_text.append(f"Logit Lens: Analyzed {logit_results.get('num_layers_analyzed', 0)} layers")
        
        if 'ablation' in results and results['ablation']:
            ablation_results = results['ablation']['results']
            max_drop = max([r['accuracy_drop'] for r in ablation_results], default=0)
            best_method = max(ablation_results, key=lambda x: x['accuracy_drop'], default={'method': 'N/A'})
            summary_text.append(f"Ablation: Max drop {max_drop:.2f} ({best_method['method']})")
        
        if 'attention_knockout' in results and results['attention_knockout']:
            attention_results = results['attention_knockout']['results']
            avg_performance = np.mean([r['performance_ratio'] for r in attention_results])
            summary_text.append(f"Attention: Avg performance {avg_performance:.2f}")
        
        # 关键发现
        key_findings = [
            "• Visual tokens show layer-wise evolution",
            "• Object information is spatially localized", 
            "• Mid-late layers extract visual information",
            "• Direct information transfer to final token"
        ]
        
        all_text = summary_text + ['', 'Key Findings:'] + key_findings
        
        ax.text(0.05, 0.95, '\n'.join(all_text), transform=ax.transAxes, 
               verticalalignment='top', fontsize=10, family='monospace')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')
    
    def save_results(self, results: Dict[str, Any], 
                    sample_id: str):
        """保存分析结果"""
        if self.config.save_intermediate:
            
            # 保存JSON格式的结果
            json_path = os.path.join(self.config.output_dir, f"results_{sample_id}.json")
            
            # 转换tensor到可序列化格式
            serializable_results = self._make_serializable(results)
            
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)
            
            # 保存pickle格式的原始结果
            pickle_path = os.path.join(self.config.output_dir, f"results_{sample_id}.pkl")
            with open(pickle_path, 'wb') as f:
                pickle.dump(results, f)
            
            logger.info(f"结果已保存: {json_path}, {pickle_path}")
    
    def _make_serializable(self, obj):
        """将包含tensor的对象转换为可序列化格式"""
        if isinstance(obj, torch.Tensor):
            return obj.cpu().tolist()
        elif isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return tuple(self._make_serializable(item) for item in obj)
        else:
            return obj 

    def setup_simpler_env(self):
        """设置SimplerEnv环境"""
        logger.info(f"正在设置SimplerEnv任务: {self.config.task_name}")
        
        try:
            # 创建环境
            self.env = simpler_env.make(self.config.task_name)
            logger.info("SimplerEnv环境创建成功")
            
            # 重置环境
            obs, reset_info = self.env.reset(seed=self.config.seed)
            
            # 获取任务指令
            self.current_instruction = self.env.get_language_instruction()
            logger.info(f"任务指令: {self.current_instruction}")
            
            return obs, reset_info
            
        except Exception as e:
            logger.error(f"SimplerEnv环境设置失败: {e}")
            raise
    
    def create_spatialvla_model(self):
        """创建SpatialVLA推理模型"""
        try:
            logger.info("正在创建SpatialVLA推理模型...")
            self.spatialvla_model = SpatialVLAInference(
                saved_model_path=self.config.model_path,
                policy_setup=self.config.policy_setup,
                action_scale=self.config.action_scale,
                action_ensemble_temp=self.config.action_ensemble_temp
            )
            
            # 重置模型
            if self.current_instruction:
                self.spatialvla_model.reset(self.current_instruction)
            
            logger.info("✅ SpatialVLA推理模型创建成功")
            return True
        except Exception as e:
            logger.error(f"❌ SpatialVLA推理模型创建失败: {e}")
            return False
    
    def get_overhead_camera_data(self, obs) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], bool]:
        """获取overhead_camera的RGB和分割数据"""
        if not isinstance(obs, dict) or "image" not in obs:
            return None, None, False
        
        if "overhead_camera" not in obs["image"]:
            return None, None, False
        
        cam_data = obs["image"]["overhead_camera"]
        rgb = cam_data.get("rgb")
        segmentation = cam_data.get("Segmentation")
        
        if rgb is None or segmentation is None:
            return None, None, False
        
        # 转换RGB格式
        if rgb.max() <= 1.0:
            rgb = (rgb * 255).astype(np.uint8)
        if len(rgb.shape) == 3 and rgb.shape[2] == 4:
            rgb = rgb[:, :, :3]
        
        logger.info(f"获取overhead camera数据成功: RGB shape={rgb.shape}")
        return rgb, segmentation, True

    def detect_key_objects(self, segmentation: np.ndarray, rgb_image: np.ndarray) -> Dict[str, Any]:
        """检测关键物体：目标物体、机械爪、背景区域"""
        if len(segmentation.shape) < 3:
            return {"target_object": None, "gripper": None, "background": None}
        
        mesh_ids = segmentation[:, :, 0]
        unique_ids = np.unique(mesh_ids)
        height, width = mesh_ids.shape
        center_y, center_x = height // 2, width // 2
        
        logger.info(f"图像尺寸={height}x{width}, 检测到 {len(unique_ids)} 个唯一ID")
        
        results = {"target_object": None, "gripper": None, "background": None}
        
        # 分析每个ID的详细信息
        id_analysis = {}
        for uid in unique_ids:
            if uid == 0:
                continue
                
            mask = (mesh_ids == uid)
            pixel_count = np.sum(mask)
            percentage = pixel_count / mesh_ids.size * 100
            
            if pixel_count > 0:
                y_coords, x_coords = np.where(mask)
                centroid_y = np.mean(y_coords)
                centroid_x = np.mean(x_coords)
                dist_to_center = np.sqrt((centroid_y - center_y)**2 + (centroid_x - center_x)**2)
                
                id_analysis[int(uid)] = {
                    'pixels': int(pixel_count),
                    'percentage': float(percentage),
                    'centroid': (float(centroid_y), float(centroid_x)),
                    'dist_to_center': float(dist_to_center),
                    'mask': mask
                }
        
        # 按像素数量排序
        sorted_objects = sorted(id_analysis.items(), key=lambda x: x[1]['pixels'], reverse=True)
        
        # 检测目标物体：选择最靠近中心且像素适中的物体
        target_candidates = []
        for uid, info in sorted_objects:
            if (info['pixels'] > 500 and  # 最小像素要求
                info['pixels'] < 50000 and  # 最大像素要求（排除背景）
                info['dist_to_center'] < 150):  # 距离中心限制
                
                score = info['pixels'] / (info['dist_to_center'] + 1)
                target_candidates.append({
                    'id': int(uid),
                    'mask': info['mask'],
                    'info': info,
                    'score': score
                })
        
        if target_candidates:
            target_candidates.sort(key=lambda x: x['score'], reverse=True)
            results["target_object"] = target_candidates[0]
            logger.info(f"选择目标物体: ID={target_candidates[0]['id']}, 像素={target_candidates[0]['info']['pixels']}")
        
        # 检测机械爪：选择在边缘的较小物体
        gripper_candidates = []
        for uid, info in sorted_objects:
            if (info['pixels'] > 100 and  # 最小像素要求
                info['pixels'] < 10000):  # 机械爪不会太大
                
                # 检查是否在边缘
                near_edge = (info['centroid'][1] < 80 or info['centroid'][1] > width-80 or 
                           info['centroid'][0] < 80 or info['centroid'][0] > height-80)
                
                if near_edge:
                    gripper_candidates.append({
                        'id': int(uid),
                        'mask': info['mask'],
                        'info': info
                    })
        
        if gripper_candidates:
            # 合并所有机械爪候选
            combined_mask = np.zeros_like(mesh_ids, dtype=bool)
            gripper_ids = []
            total_pixels = 0
            
            for candidate in gripper_candidates:
                combined_mask |= candidate['mask']
                gripper_ids.append(candidate['id'])
                total_pixels += candidate['info']['pixels']
            
            results["gripper"] = {
                'ids': gripper_ids,
                'mask': combined_mask,
                'pixels': int(total_pixels)
            }
            logger.info(f"检测到机械爪: IDs={gripper_ids}, 总像素={total_pixels}")
        
        # 创建背景区域（排除目标物体和机械爪）
        background_mask = np.ones_like(mesh_ids, dtype=bool)
        if results["target_object"]:
            background_mask &= ~results["target_object"]["mask"]
        if results["gripper"]:
            background_mask &= ~results["gripper"]["mask"]
        
        background_pixels = np.sum(background_mask)
        if background_pixels > 0:
            results["background"] = {
                'mask': background_mask,
                'pixels': int(background_pixels)
            }
            logger.info(f"背景区域像素: {background_pixels}")
        
        return results
    
    def get_patches_from_mask(self, mask: np.ndarray, patch_size: int = 16) -> List[int]:
        """从空间掩码获取对应的patch索引"""
        height, width = mask.shape
        patch_height = height / patch_size
        patch_width = width / patch_size
        
        # 获取相关patches
        relevant_patches = set()
        y_coords, x_coords = np.where(mask)
        
        for y, x in zip(y_coords, x_coords):
            patch_row = min(int(y / patch_height), patch_size - 1)
            patch_col = min(int(x / patch_width), patch_size - 1)
            patch_id = patch_row * patch_size + patch_col
            relevant_patches.add(patch_id)
        
        return sorted(list(relevant_patches))
    
    def map_patches_to_tokens(self, patch_indices: List[int], visual_positions: List[int]) -> List[int]:
        """将patch索引映射到视觉token位置
        
        由于SpatialVLA有256个<image> token对应16x16的patches，
        我们可以直接使用patch索引作为visual token的位置索引
        """
        if not patch_indices or not visual_positions:
            return []
        
        logger.info(f"🔗 映射patches到tokens:")
        logger.info(f"   输入patch索引: {patch_indices}")
        logger.info(f"   可用visual位置: {len(visual_positions)}")
        
        mapped_positions = []
        for patch_id in patch_indices:
            # 直接使用patch_id作为visual_positions的索引
            # 因为visual_positions包含256个位置，对应16x16的patches
            if patch_id < len(visual_positions):
                token_pos = visual_positions[patch_id]
                mapped_positions.append(token_pos)
                logger.info(f"   ✓ Patch {patch_id} -> Token位置 {token_pos}")
            else:
                logger.warning(f"   ⚠️ Patch {patch_id} 超出范围({len(visual_positions)})")
        
        logger.info(f"🎯 映射完成: {len(mapped_positions)}/{len(patch_indices)} patches成功映射")
        return mapped_positions

    def run_spatial_ablation_study(self, inputs: Dict[str, torch.Tensor], 
                                  instruction: str, key_objects: Dict[str, Any],
                                  image_shape: Tuple[int, int]) -> Dict[str, Any]:
        """基于空间区域的消融研究"""
        logger.info("开始空间区域消融研究分析...")
        
        # 提取视觉token位置
        visual_positions = self.extract_visual_tokens_and_positions(inputs)
        
        if not visual_positions:
            logger.warning("未找到视觉token位置")
            return {"results": [], "visual_positions": []}
        
        logger.info(f"最终视觉token位置数量: {len(visual_positions)}")
        logger.info(f"视觉token位置范围: {min(visual_positions)} - {max(visual_positions)}")
        
        # 获取原始预测
        try:
            original_outputs = self.model(**inputs, return_dict=True)
            original_logits = original_outputs.logits
            original_prediction = torch.argmax(original_logits[0, -1, :])
            logger.info(f"原始预测token: {original_prediction.item()}")
        except Exception as e:
            logger.error(f"获取原始预测失败: {e}")
            return {"results": [], "visual_positions": visual_positions}
        
        # 计算均值视觉token用于替换
        mean_token = self._compute_mean_visual_token()
        
        # 从inputs中提取图像用于可视化（如果有pixel_values）
        image_for_vis = None
        model_image_height, model_image_width = 224, 224  # SpatialVLA的默认输入尺寸
        
        if 'pixel_values' in inputs:
            try:
                # 从pixel_values提取图像用于可视化
                pixel_values = inputs['pixel_values'][0]  # [C, H, W]
                model_image_height, model_image_width = pixel_values.shape[1], pixel_values.shape[2]
                
                if pixel_values.dtype == torch.bfloat16 or pixel_values.dtype == torch.float16:
                    pixel_values = pixel_values.float()
                
                # 转换为numpy格式 [H, W, C]
                img_tensor = pixel_values.permute(1, 2, 0).cpu()
                
                # 归一化到0-255
                if img_tensor.max() <= 1.0:
                    img_numpy = (img_tensor.numpy() * 255).astype(np.uint8)
                else:
                    img_numpy = img_tensor.numpy().astype(np.uint8)
                    
                image_for_vis = img_numpy
                logger.info(f"提取可视化图像成功，尺寸: {image_for_vis.shape}")
                logger.info(f"模型图像尺寸: {model_image_height}x{model_image_width}")
            except Exception as e:
                logger.warning(f"提取可视化图像失败: {e}")
        
        results = []
        ablation_methods = []
        
        # 1. 目标物体消融
        if key_objects.get("target_object"):
            target_mask = key_objects["target_object"]["mask"]
            # 将原始图像尺寸的mask缩放到模型输入尺寸
            target_mask_resized = self._resize_mask_to_model_size(
                target_mask, model_image_height, model_image_width
            )
            target_patches = self.get_patches_from_mask_model_size(target_mask_resized)
            target_positions = self.map_patches_to_tokens(target_patches, visual_positions)
            
            if target_positions:
                logger.info(f"目标物体patches: {len(target_patches)}, tokens: {len(target_positions)}")
                logger.info(f"目标物体patch索引: {target_patches}")
                accuracy_drop = self._perform_ablation(
                    inputs, target_positions, mean_token, original_logits, instruction,
                    ablation_method="target_object", image_for_vis=image_for_vis,
                    debug_patch_indices=target_patches,  # 传递实际的patch索引用于可视化
                    original_region_mask=target_mask_resized
                )
                results.append({
                    "method": "target_object",
                    "ablated_positions": target_positions,
                    "patch_indices": target_patches,
                    "num_positions": len(target_positions),
                    "accuracy_drop": accuracy_drop
                })
                ablation_methods.append("target_object")
        
        # 2. 机械爪消融
        if key_objects.get("gripper"):
            gripper_mask = key_objects["gripper"]["mask"]
            gripper_mask_resized = self._resize_mask_to_model_size(
                gripper_mask, model_image_height, model_image_width
            )
            gripper_patches = self.get_patches_from_mask_model_size(gripper_mask_resized)
            gripper_positions = self.map_patches_to_tokens(gripper_patches, visual_positions)
            
            if gripper_positions:
                logger.info(f"机械爪patches: {len(gripper_patches)}, tokens: {len(gripper_positions)}")
                logger.info(f"机械爪patch索引: {gripper_patches}")
                accuracy_drop = self._perform_ablation(
                    inputs, gripper_positions, mean_token, original_logits, instruction,
                    ablation_method="gripper", image_for_vis=image_for_vis,
                    debug_patch_indices=gripper_patches,
                    original_region_mask=gripper_mask_resized
                )
                results.append({
                    "method": "gripper",
                    "ablated_positions": gripper_positions,
                    "patch_indices": gripper_patches,
                    "num_positions": len(gripper_positions),
                    "accuracy_drop": accuracy_drop
                })
                ablation_methods.append("gripper")
        
        # 3. 背景区域消融
        if key_objects.get("background"):
            background_mask = key_objects["background"]["mask"]
            background_mask_resized = self._resize_mask_to_model_size(
                background_mask, model_image_height, model_image_width
            )
            background_patches = self.get_patches_from_mask_model_size(background_mask_resized)
            
            if background_patches:
                # 对于背景，我们随机选择一部分进行消融（避免过多）
                random.seed(self.config.seed)
                max_background_patches = min(50, len(background_patches))  # 限制背景patch数量
                background_patches_sample = random.sample(background_patches, max_background_patches)
                background_positions_sample = self.map_patches_to_tokens(background_patches_sample, visual_positions)
                
                logger.info(f"背景patches总数: {len(background_patches)}, 采样patches: {len(background_patches_sample)}")
                logger.info(f"背景采样patch索引: {background_patches_sample}")
                accuracy_drop = self._perform_ablation(
                    inputs, background_positions_sample, mean_token, original_logits, instruction,
                    ablation_method="background", image_for_vis=image_for_vis,
                    debug_patch_indices=background_patches_sample,  # 使用采样后的patch索引
                    original_region_mask=background_mask_resized
                )
                results.append({
                    "method": "background",
                    "ablated_positions": background_positions_sample,
                    "patch_indices": background_patches_sample,
                    "num_positions": len(background_positions_sample),
                    "accuracy_drop": accuracy_drop
                })
                ablation_methods.append("background")
        
        # 4. 中心区域消融（作为对比）
        center_positions = self._get_center_positions(visual_positions)
        if center_positions:
            # 获取中心区域对应的patch索引
            center_patches = list(range(80, 176))  # 16x16网格的中心区域，大概是第5-11行，第5-11列
            center_patches = [p for p in center_patches if p < 256]  # 确保不超出范围
            
            logger.info(f"中心区域tokens: {len(center_positions)}")
            logger.info(f"中心区域patch索引: {center_patches[:10]}...")  # 显示前10个
            accuracy_drop = self._perform_ablation(
                inputs, center_positions, mean_token, original_logits, instruction,
                ablation_method="center_region", image_for_vis=image_for_vis,
                debug_patch_indices=center_patches,
                original_region_mask=background_mask_resized
            )
            results.append({
                "method": "center_region",
                "ablated_positions": center_positions,
                "patch_indices": center_patches,
                "num_positions": len(center_positions),
                "accuracy_drop": accuracy_drop
            })
            ablation_methods.append("center_region")
        
        # 5. 随机区域消融（作为基准）
        random.seed(self.config.seed)
        num_random = min(20, len(visual_positions))  # 随机选择20个token
        random_positions = random.sample(visual_positions, num_random)
        random_patches = random.sample(list(range(256)), num_random)  # 对应的随机patch索引
        
        logger.info(f"随机区域tokens: {len(random_positions)}")
        logger.info(f"随机区域patch索引: {random_patches}")
        accuracy_drop = self._perform_ablation(
            inputs, random_positions, mean_token, original_logits, instruction,
            ablation_method="random_region", image_for_vis=image_for_vis,
            debug_patch_indices=random_patches,
            original_region_mask=background_mask_resized
        )
        results.append({
            "method": "random_region",
            "ablated_positions": random_positions,
            "patch_indices": random_patches,
            "num_positions": len(random_positions),
            "accuracy_drop": accuracy_drop
        })
        ablation_methods.append("random_region")
        
        logger.info(f"空间区域消融研究完成，测试了{len(results)}种方法: {ablation_methods}")
        
        return {
            "results": results,
            "visual_positions": visual_positions,
            "original_prediction": original_prediction.item(),
            "key_objects_detected": {
                "target_object": key_objects.get("target_object") is not None,
                "gripper": key_objects.get("gripper") is not None,
                "background": key_objects.get("background") is not None
            }
        }

    def _resize_mask_to_model_size(self, mask: np.ndarray, target_height: int, target_width: int) -> np.ndarray:
        """将原始图像尺寸的mask缩放到模型输入尺寸"""
        from PIL import Image
        
        # 将布尔mask转换为uint8
        mask_uint8 = (mask.astype(np.uint8) * 255)
        
        # 使用PIL进行缩放
        mask_pil = Image.fromarray(mask_uint8)
        mask_resized_pil = mask_pil.resize((target_width, target_height), Image.NEAREST)
        
        # 转换回布尔mask
        mask_resized = np.array(mask_resized_pil) > 127  # 阈值化
        
        return mask_resized

    def get_patches_from_mask_model_size(self, mask: np.ndarray, patch_size: int = 16) -> List[int]:
        """从模型尺寸的掩码获取对应的patch索引"""
        height, width = mask.shape
        patch_height = height / patch_size
        patch_width = width / patch_size
        
        # 获取相关patches
        relevant_patches = set()
        y_coords, x_coords = np.where(mask)
        
        for y, x in zip(y_coords, x_coords):
            patch_row = min(int(y / patch_height), patch_size - 1)
            patch_col = min(int(x / patch_width), patch_size - 1)
            patch_id = patch_row * patch_size + patch_col
            relevant_patches.add(patch_id)
        
        return sorted(list(relevant_patches))

    def _sample_representative_patches(self, key_objects: Dict[str, Any], 
                                     visual_positions: List[int], 
                                     num_samples: int = 3) -> Dict[str, List[int]]:
        """从Object/Gripper/Background中采样代表性patches"""
        samples = {
            'object': [],
            'gripper': [],
            'background': []
        }
        
        logger.info(f"🔍 开始采样代表性patches (num_samples={num_samples})")
        logger.info(f"   可用视觉token位置: {len(visual_positions)}")
        
        # Object patches - 选择目标物体区域的patches
        if key_objects.get('target_object'):
            try:
                target_mask = key_objects['target_object']['mask']
                logger.info(f"🎯 处理目标物体: mask shape={target_mask.shape}")
                
                # 缩放到模型尺寸
                target_mask_resized = self._resize_mask_to_model_size(target_mask, 224, 224)
                object_patches = self.get_patches_from_mask_model_size(target_mask_resized)
                
                logger.info(f"   目标物体patches: {len(object_patches)} - {object_patches[:10]}")
                
                if object_patches:
                    # 选择质心附近的patches - 改进选择策略
                    if len(object_patches) <= num_samples:
                        samples['object'] = object_patches
                    else:
                        # 选择分布均匀的patches
                        step = max(1, len(object_patches) // num_samples)
                        samples['object'] = object_patches[::step][:num_samples]
                    
                    logger.info(f"✓ 选择的目标物体patches: {samples['object']}")
                else:
                    logger.warning("⚠️ 目标物体未产生有效patches")
            except Exception as e:
                logger.error(f"❌ 处理目标物体失败: {e}")
        else:
            logger.info("ℹ️ 未检测到目标物体")
        
        # Gripper patches - 选择机械爪区域的patches  
        if key_objects.get('gripper'):
            try:
                gripper_mask = key_objects['gripper']['mask']
                logger.info(f"🤖 处理机械爪: mask shape={gripper_mask.shape}")
                
                gripper_mask_resized = self._resize_mask_to_model_size(gripper_mask, 224, 224)
                gripper_patches = self.get_patches_from_mask_model_size(gripper_mask_resized)
                
                logger.info(f"   机械爪patches: {len(gripper_patches)} - {gripper_patches[:10]}")
                
                if gripper_patches:
                    samples['gripper'] = gripper_patches[:num_samples]
                    logger.info(f"✓ 选择的机械爪patches: {samples['gripper']}")
                else:
                    logger.warning("⚠️ 机械爪未产生有效patches")
            except Exception as e:
                logger.error(f"❌ 处理机械爪失败: {e}")
        else:
            logger.info("ℹ️ 未检测到机械爪")
        
        # Background patches - 随机选择背景区域的patches
        if key_objects.get('background'):
            try:
                background_mask = key_objects['background']['mask']
                logger.info(f"🌅 处理背景: mask shape={background_mask.shape}")
                
                background_mask_resized = self._resize_mask_to_model_size(background_mask, 224, 224)
                background_patches = self.get_patches_from_mask_model_size(background_mask_resized)
                
                logger.info(f"   背景patches: {len(background_patches)} - {background_patches[:10]}")
                
                if background_patches:
                    # 随机采样背景patches
                    random.seed(self.config.seed)
                    random_bg_patches = random.sample(
                        background_patches, 
                        min(num_samples, len(background_patches))
                    )
                    samples['background'] = random_bg_patches
                    logger.info(f"✓ 选择的背景patches: {samples['background']}")
                else:
                    logger.warning("⚠️ 背景未产生有效patches")
            except Exception as e:
                logger.error(f"❌ 处理背景失败: {e}")
        else:
            logger.info("ℹ️ 未检测到背景")
        
        # 如果某些类别没有patches，尝试使用基于位置的backup策略
        total_samples = sum(len(patches) for patches in samples.values())
        if total_samples < num_samples * 3:  # 如果总数少于期望的数量，启用备用策略
            logger.warning(f"⚠️ 当前patches总数({total_samples})不足，使用基于位置的备用策略补充")
            
            # 基于16x16 patch网格的位置策略
            patch_grid_size = 16
            center_start = patch_grid_size // 4  # 中心区域开始
            center_end = 3 * patch_grid_size // 4  # 中心区域结束
            
            # 目标物体：中心区域
            if len(samples['object']) < num_samples:
                object_patches_backup = []
                for row in range(center_start, center_end):
                    for col in range(center_start, center_end):
                        patch_id = row * patch_grid_size + col
                        if patch_id < 256:  # 确保不超出16x16范围
                            object_patches_backup.append(patch_id)
                
                # 补充到目标数量
                needed = num_samples - len(samples['object'])
                samples['object'].extend(object_patches_backup[:needed])
            
            # 机械爪：边缘区域
            if len(samples['gripper']) < num_samples:
                gripper_patches_backup = []
                for patch_id in range(256):
                    row, col = divmod(patch_id, patch_grid_size)
                    if (row < 3 or row >= patch_grid_size-3 or 
                        col < 3 or col >= patch_grid_size-3):
                        gripper_patches_backup.append(patch_id)
                
                # 补充到目标数量
                needed = num_samples - len(samples['gripper'])
                samples['gripper'].extend(gripper_patches_backup[:needed])
            
            # 背景：均匀分布选择
            if len(samples['background']) < num_samples:
                background_patches_backup = []
                step = 256 // (num_samples * 2)  # 计算步长以均匀分布
                for i in range(0, 256, step):
                    background_patches_backup.append(i)
                
                # 补充到目标数量
                needed = num_samples - len(samples['background'])
                samples['background'].extend(background_patches_backup[:needed])
            
            logger.info(f"🔄 备用策略补充后:")
            logger.info(f"   object: {len(samples['object'])} patches")
            logger.info(f"   gripper: {len(samples['gripper'])} patches")
            logger.info(f"   background: {len(samples['background'])} patches")
        
        # 最终统计
        final_total = sum(len(patches) for patches in samples.values())
        logger.info(f"📊 采样完成: 总计{final_total}个patches")
        for category, patches in samples.items():
            logger.info(f"   {category}: {len(patches)} patches")
        
        return samples

    def _trace_token_evolution(self, patch_indices: List[int], 
                             visual_positions: List[int],
                             hidden_states: List[torch.Tensor], 
                             target_layers: List[int]) -> Dict[int, Dict[int, List[Tuple[str, float]]]]:
        """追踪特定patches在不同层的token演化"""
        evolution_data = {}
        
        logger.info(f"🧠 开始追踪token演化")
        logger.info(f"   patch索引: {len(patch_indices)} - {patch_indices[:10]}")
        logger.info(f"   visual位置: {len(visual_positions)} - {visual_positions[:10]}")
        logger.info(f"   目标层: {target_layers}")
        logger.info(f"   隐藏状态可用层数: {len(hidden_states)}")
        
        # 获取语言模型的unembedding矩阵
        try:
            lm_head = self.model.language_model.lm_head
            logger.info(f"✓ 获取LM头成功: {lm_head.weight.shape}")
        except Exception as e:
            logger.error(f"❌ 获取LM头失败: {e}")
            return evolution_data
        
        successful_traces = 0
        total_attempts = 0
        
        for layer_idx in target_layers:
            if layer_idx >= len(hidden_states):
                logger.warning(f"⚠️ Layer {layer_idx} 超出隐藏状态范围 ({len(hidden_states)})")
                continue
                
            try:
                layer_hidden = hidden_states[layer_idx]  # [batch, seq_len, hidden_size]
                logger.info(f"🔍 处理Layer {layer_idx}: hidden shape={layer_hidden.shape}")
                evolution_data[layer_idx] = {}
                layer_successful = 0
                
                for patch_idx in patch_indices:
                    total_attempts += 1
                    
                    if patch_idx >= len(visual_positions):
                        logger.warning(f"⚠️ Patch索引{patch_idx}超出visual_positions范围({len(visual_positions)})")
                        continue
                        
                    token_pos = visual_positions[patch_idx]
                    if token_pos >= layer_hidden.shape[1]:
                        logger.warning(f"⚠️ Token位置{token_pos}超出序列长度({layer_hidden.shape[1]})")
                        continue
                    
                    try:
                        # 获取该位置的隐藏状态
                        token_hidden = layer_hidden[0, token_pos, :]  # [hidden_size]
                        
                        # 确保数据类型一致
                        if token_hidden.dtype != lm_head.weight.dtype:
                            token_hidden = token_hidden.to(dtype=lm_head.weight.dtype)
                        
                        # 投影到词汇空间
                        logits = lm_head(token_hidden.unsqueeze(0))  # [1, vocab_size]
                        probs = F.softmax(logits, dim=-1)
                        
                        # 获取top-5 tokens
                        top_k = 5
                        top_probs, top_indices = torch.topk(probs, top_k, dim=-1)
                        
                        # 解码tokens
                        tokens_with_probs = []
                        for j in range(top_k):
                            token_id = top_indices[0, j].item()
                            prob = top_probs[0, j].item()
                            try:
                                token_text = self.processor.tokenizer.decode([token_id]).strip()
                                if not token_text:  # 处理空token
                                    token_text = f"<{token_id}>"
                                # 移除文本长度限制，显示完整token名称
                                tokens_with_probs.append((token_text, prob))
                            except Exception as decode_error:
                                logger.warning(f"Token解码失败 {token_id}: {decode_error}")
                                tokens_with_probs.append((f"<{token_id}>", prob))
                        
                        evolution_data[layer_idx][patch_idx] = tokens_with_probs
                        layer_successful += 1
                        successful_traces += 1
                        
                        # 只对前几个patch显示详细信息，避免日志过多
                        if len(evolution_data[layer_idx]) <= 3:
                            top_token = tokens_with_probs[0]
                            logger.info(f"   ✓ Layer{layer_idx} Patch{patch_idx}->Pos{token_pos}: {top_token[0]}({top_token[1]:.3f})")
                            
                    except Exception as token_error:
                        logger.warning(f"⚠️ Layer{layer_idx} Patch{patch_idx} 处理失败: {token_error}")
                        continue
                
                logger.info(f"✓ Layer {layer_idx} 完成: {layer_successful}/{len(patch_indices)} patches成功")
                
            except Exception as layer_error:
                logger.error(f"❌ Layer {layer_idx} 处理失败: {layer_error}")
                continue
        
        # 最终统计
        success_rate = successful_traces / max(1, total_attempts) * 100
        logger.info(f"🎯 Token演化追踪完成:")
        logger.info(f"   总成功: {successful_traces}/{total_attempts} ({success_rate:.1f}%)")
        logger.info(f"   成功的层: {len(evolution_data)}/{len(target_layers)}")
        
        # 如果成功率太低，给出诊断建议
        if success_rate < 50:
            logger.warning("⚠️ 成功率较低，可能的原因:")
            logger.warning("   - patch索引与visual_positions映射问题")
            logger.warning("   - 隐藏状态维度不匹配")
            logger.warning("   - 模型结构访问问题")
        
        return evolution_data

    def _render_patch_annotations(self, image: np.ndarray, 
                                patch_data: Dict[int, List[Tuple[str, float]]], 
                                patch_indices: List[int],
                                color: str,
                                category_name: str) -> np.ndarray:
        """在图像上渲染patch标注和token信息 - 使用数字标号+右侧显示方案"""
        annotated_image = image.copy()
        height, width = image.shape[:2]
        
        # 定义颜色映射
        color_map = {
            'object': (255, 68, 68),    # 红色
            'gripper': (68, 68, 255),   # 蓝色  
            'background': (68, 255, 68) # 绿色
        }
        
        bbox_color = color_map.get(color, (255, 255, 255))
        
        # 计算patch尺寸
        patch_size = 16
        patch_height = height / patch_size
        patch_width = width / patch_size
        
        # 收集所有有效的patch信息，按patch索引排序
        valid_patches = []
        for patch_idx in sorted(patch_indices):
            if patch_idx in patch_data:
                valid_patches.append((patch_idx, patch_data[patch_idx]))
        
        if not valid_patches:
            return annotated_image
        
        # 在图像上绘制数字标号
        for i, (patch_idx, tokens_info) in enumerate(valid_patches, 1):
            # 计算patch在图像中的位置
            patch_row = patch_idx // patch_size
            patch_col = patch_idx % patch_size
            
            y_start = int(patch_row * patch_height)
            y_end = int(min(y_start + patch_height, height))
            x_start = int(patch_col * patch_width)  
            x_end = int(min(x_start + patch_width, width))
            
            # 绘制边框
            cv2.rectangle(annotated_image, (x_start, y_start), (x_end, y_end), 
                         bbox_color, 2)
            
            # 在patch中心绘制数字标号
            center_x = (x_start + x_end) // 2
            center_y = (y_start + y_end) // 2
            
            # 绘制数字背景圆圈
            cv2.circle(annotated_image, (center_x, center_y), 12, (255, 255, 255), -1)
            cv2.circle(annotated_image, (center_x, center_y), 12, bbox_color, 2)
            
            # 绘制数字
            cv2.putText(annotated_image, str(i),
                       (center_x - 6, center_y + 4),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, bbox_color, 2)
        
        # 在图像右侧创建信息面板
        panel_width = 400  # 信息面板宽度
        panel_image = np.ones((height, panel_width, 3), dtype=np.uint8) * 255  # 白色背景
        
        # 绘制信息面板内容
        y_offset = 30
        line_height = 25
        
        # 标题
        title = f"{category_name.title()} Patches"
        cv2.putText(panel_image, title, (10, y_offset), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 0), 2)
        y_offset += line_height + 10
        
        # 显示每个patch的token信息
        for i, (patch_idx, tokens_info) in enumerate(valid_patches, 1):
            # 绘制patch编号和索引
            patch_info = f"{i}. Patch{patch_idx}:"
            cv2.putText(panel_image, patch_info, (10, y_offset), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, bbox_color, 1)
            y_offset += line_height
            
            # 显示前3个token
            for j, (token, prob) in enumerate(tokens_info[:3]):
                # 清理token文本，处理编码问题
                clean_token = self._clean_token_text(token)
                token_line = f"  {clean_token}: {prob:.3f}"
                
                # 确保不超出面板范围
                if y_offset < height - 20:
                    cv2.putText(panel_image, token_line, (15, y_offset), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.4, (50, 50, 50), 1)
                    y_offset += line_height - 5
            
            y_offset += 5  # patch间隔
            
            # 如果超出面板高度，停止绘制
            if y_offset > height - 50:
                remaining = len(valid_patches) - i
                if remaining > 0:
                    cv2.putText(panel_image, f"... +{remaining} more patches", 
                               (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (100, 100, 100), 1)
                break
        
        # 拼接原图和信息面板
        combined_image = np.hstack([annotated_image, panel_image])
        
        return combined_image

    def _clean_token_text(self, token: str) -> str:
        """清理token文本，处理编码和特殊字符问题"""
        if not token:
            return "<empty>"
        
        # 移除换行符和回车符
        clean_token = token.replace('\n', '').replace('\r', '')
        
        # 处理特殊的unicode字符和问号
        clean_token = clean_token.replace('?', ' ')
        clean_token = clean_token.replace('？', ' ')
        
        # 处理常见的编码问题
        try:
            # 尝试重新编码以处理编码问题
            clean_token = clean_token.encode('ascii', errors='ignore').decode('ascii')
        except:
            pass
        
        # 如果清理后为空或只有特殊字符，使用token ID表示
        if not clean_token or clean_token.isspace() or len(clean_token.strip()) == 0:
            # 尝试从原始token提取有用信息
            if '<' in token and '>' in token:
                clean_token = token  # 保留特殊token格式如<loc0984>
            else:
                clean_token = f"<token>"
        
        # 限制长度以适应显示
        if len(clean_token) > 25:
            clean_token = clean_token[:25] + "..."
        
        return clean_token

    def create_comprehensive_logit_lens_visualization(self, 
                                                    results: Dict[str, Any], 
                                                    key_objects: Dict[str, Any],
                                                    original_image: np.ndarray,
                                                    save_path: str):
        """创建增强的Logit Lens可视化，类似论文效果"""
        
        if 'logit_lens' not in results or not results['logit_lens']:
            logger.warning("没有Logit Lens数据可用于增强可视化")
            return
            
        logit_lens_data = results['logit_lens']
        if 'results' not in logit_lens_data:
            return
            
        # 获取配置参数 - 移除层数限制，使用完整配置
        target_layers = self.config.target_layers  # 移除[:4]限制
        visual_positions = logit_lens_data.get('visual_positions', [])
        hidden_states = logit_lens_data.get('hidden_states', [])
        
        logger.info(f"🔍 增强可视化调试信息:")
        logger.info(f"  - 配置的目标层: {target_layers}")
        logger.info(f"  - 可用的隐藏状态层数: {len(hidden_states) if hidden_states else 0}")
        logger.info(f"  - 视觉token位置数量: {len(visual_positions)}")
        
        if not visual_positions:
            logger.warning("没有视觉token位置信息")
            return
            
        if not hidden_states:
            logger.warning("没有隐藏状态信息")
            return
        
        # 检测关键物体并调试
        logger.info(f"🎯 关键物体检测状态:")
        logger.info(f"  - 目标物体: {'✓' if key_objects.get('target_object') else '✗'}")
        logger.info(f"  - 机械爪: {'✓' if key_objects.get('gripper') else '✗'}")
        logger.info(f"  - 背景: {'✓' if key_objects.get('background') else '✗'}")
        
        # 采样代表性patches并增加调试
        patch_samples = self._sample_representative_patches(key_objects, visual_positions, num_samples=15)  # 增加采样数量到15个
        logger.info(f"📊 Patch采样结果:")
        for category, patches in patch_samples.items():
            logger.info(f"  - {category}: {len(patches)} patches - {patches[:5]}")  # 显示前5个
        
        # 收集所有需要分析的patch_indices
        all_patch_indices = []
        for category_patches in patch_samples.values():
            all_patch_indices.extend(category_patches)
        
        logger.info(f"🔗 总patch索引: {len(all_patch_indices)} - {all_patch_indices[:10]}")  # 显示前10个
        
        if not all_patch_indices:
            logger.warning("❌ 没有找到可分析的patches，尝试使用备用方案")
            # 备用方案：使用视觉token位置的前几个作为patch索引
            backup_patches = list(range(min(15, len(visual_positions))))  # 使用前15个位置
            all_patch_indices = backup_patches
            # 将备用patches分配给不同类别
            patch_samples = {
                'object': backup_patches[:5],
                'gripper': backup_patches[5:10] if len(backup_patches) > 5 else [],
                'background': backup_patches[10:15] if len(backup_patches) > 10 else []
            }
            logger.info(f"🔄 使用备用方案: {len(all_patch_indices)} patches")
        
        # 确保target_layers不超出可用范围
        valid_target_layers = [layer for layer in target_layers if layer < len(hidden_states)]
        if len(valid_target_layers) != len(target_layers):
            logger.warning(f"⚠️ 部分层超出范围，有效层: {valid_target_layers}")
        target_layers = valid_target_layers
        
        if not target_layers:
            logger.error("❌ 没有有效的目标层")
            return
        
        # 使用_trace_token_evolution方法获取演化数据
        try:
            evolution_data = self._trace_token_evolution(
                all_patch_indices, visual_positions, hidden_states, target_layers
            )
            logger.info(f"🧠 演化数据获取成功:")
            for layer, layer_data in evolution_data.items():
                logger.info(f"  - Layer {layer}: {len(layer_data)} patches有数据")
        except Exception as e:
            logger.error(f"❌ 获取演化数据失败: {e}")
            import traceback
            traceback.print_exc()
            return
        
        # 创建可视化 - 动态布局
        num_layers = len(target_layers)
        
        # 动态计算图像尺寸 - 考虑到现在每个子图都包含信息面板，需要更大的宽度
        fig_width = 25  # 增加宽度以容纳信息面板
        fig_height = max(4, min(25, 4 * num_layers))  # 高度根据层数调整，但有上下限
        
        logger.info(f"📐 创建可视化布局: {num_layers}层, 尺寸 {fig_width}x{fig_height}")
        
        fig, axes = plt.subplots(num_layers, 4, figsize=(fig_width, fig_height))
        
        # 确保axes是2D数组
        if num_layers == 1:
            axes = [axes]
        elif num_layers == 0:
            logger.error("❌ 没有层可显示")
            return
        
        fig.suptitle('Enhanced Logit Lens Visualization: Token Evolution with Numbered Patches', 
                     fontsize=16, fontweight='bold')
        
        # 为每层创建可视化
        for i, layer in enumerate(target_layers):
            layer_evolution = evolution_data.get(layer, {})
            
            logger.info(f"🎨 处理Layer {layer}: {len(layer_evolution)} patches有演化数据")
            
            # 列1: 原始图像
            axes[i][0].imshow(original_image)
            axes[i][0].set_title(f'Layer {layer}: Original Image', fontsize=10)
            axes[i][0].axis('off')
            
            # 列2-4: 不同类别的patches
            categories = ['object', 'gripper', 'background']
            colors = ['object', 'gripper', 'background']
            
            for j, (category, color) in enumerate(zip(categories, colors)):
                patches = patch_samples.get(category, [])
                
                # 检查是否有数据
                has_data = patches and any(p in layer_evolution for p in patches)
                
                if has_data:
                    # 渲染带标注的图像
                    try:
                        annotated_image = self._render_patch_annotations(
                            original_image, layer_evolution, patches, color, category
                        )
                        axes[i][j+1].imshow(annotated_image)
                        logger.info(f"✓ Layer {layer} {category} 渲染成功")
                    except Exception as e:
                        logger.warning(f"⚠️ Layer {layer} {category} 渲染失败: {e}")
                        # 显示原图
                        axes[i][j+1].imshow(original_image)
                        axes[i][j+1].text(0.5, 0.5, f'{category}\nRender Error', 
                                        ha='center', va='center', 
                                        transform=axes[i][j+1].transAxes,
                                        fontsize=9, bbox=dict(boxstyle="round,pad=0.3", 
                                                              facecolor="red", alpha=0.7))
                else:
                    # 显示原图但标注没有数据
                    axes[i][j+1].imshow(original_image)
                    reason = "No patches" if not patches else "No evolution data"
                    axes[i][j+1].text(0.5, 0.5, f'No {category}\ndata available\n({reason})', 
                                    ha='center', va='center', 
                                    transform=axes[i][j+1].transAxes,
                                    fontsize=9, bbox=dict(boxstyle="round,pad=0.3", 
                                                          facecolor="white", alpha=0.8))
                    logger.info(f"ℹ️ Layer {layer} {category}: {reason}")
                
                axes[i][j+1].set_title(f'Layer {layer}: {category.title()} Patches', fontsize=10)
                axes[i][j+1].axis('off')
        
        # 调整布局
        plt.tight_layout()
        
        # 如果图像太高，调整子图间距
        if num_layers > 6:
            plt.subplots_adjust(hspace=0.3, wspace=0.2)
        
        # 保存图像
        try:
            plt.savefig(save_path, dpi=200, bbox_inches='tight')  # 降低DPI以节省空间
            plt.close()
            logger.info(f"✅ 增强的Logit Lens可视化已保存到: {save_path}")
            logger.info(f"📊 最终统计: {num_layers}层, 分析了{len(all_patch_indices)}个patches")
        except Exception as e:
            logger.error(f"❌ 保存可视化失败: {e}")
            plt.close()

    def demonstrate_logit_lens_principle(self, inputs: Dict[str, torch.Tensor], 
                                        instruction: str):
        """演示Logit Lens在SpatialVLA中的工作原理
        
        这个函数会详细展示每一步的计算过程，帮助理解原理
        """
        logger.info("=== Logit Lens 原理演示开始 ===")
        
        with torch.no_grad():
            # 第1步：获取所有层的隐藏状态
            logger.info("📊 第1步：运行模型获取所有层的隐藏状态...")
            outputs = self.model(**inputs, output_hidden_states=True, return_dict=True)
            hidden_states = outputs.hidden_states  # List[Tensor], 每个是 [batch, seq_len, hidden_size]
            final_logits = outputs.logits  # [batch, seq_len, vocab_size] 最终输出
            
            logger.info(f"   ✓ 获得 {len(hidden_states)} 层隐藏状态")
            logger.info(f"   ✓ 隐藏状态形状: {hidden_states[0].shape}")
            logger.info(f"   ✓ 最终logits形状: {final_logits.shape}")
            
            # 第2步：获取语言模型头部
            logger.info("🧠 第2步：获取语言模型头部(lm_head)...")
            lm_head = self.model.language_model.lm_head
            logger.info(f"   ✓ lm_head权重形状: {lm_head.weight.shape}")  # [vocab_size, hidden_size]
            logger.info(f"   ✓ 词汇表大小: {lm_head.weight.shape[0]}")
            logger.info(f"   ✓ 隐藏维度: {lm_head.weight.shape[1]}")
            
            # 第3步：选择一个具体的位置进行分析
            visual_positions = self.extract_visual_tokens_and_positions(inputs)
            if not visual_positions:
                logger.warning("未找到视觉token位置")
                return
                
            # 选择第一个视觉token位置作为例子
            example_pos = visual_positions[10] if len(visual_positions) > 10 else visual_positions[0]
            logger.info(f"🎯 第3步：分析位置 {example_pos} 的token演化...")
            
            # 第4步：逐层分析这个位置的表示演化
            logger.info("🔍 第4步：逐层分析表示演化...")
            
            # 分析几个关键层
            demo_layers = [5, 10, 15, 20, 25, 30] if len(hidden_states) > 30 else list(range(0, len(hidden_states), 5))
            demo_layers = [l for l in demo_layers if l < len(hidden_states)][:6]  # 最多6层
            
            logger.info(f"   演示层: {demo_layers}")
            
            for layer_idx in demo_layers:
                logger.info(f"\n--- Layer {layer_idx} 分析 ---")
                
                # 提取该层该位置的隐藏状态
                layer_hidden = hidden_states[layer_idx]  # [1, seq_len, hidden_size]
                if example_pos >= layer_hidden.shape[1]:
                    logger.warning(f"位置 {example_pos} 超出序列长度 {layer_hidden.shape[1]}")
                    continue
                    
                position_hidden = layer_hidden[0, example_pos, :]  # [hidden_size]
                logger.info(f"   隐藏状态维度: {position_hidden.shape}")
                logger.info(f"   隐藏状态范围: [{position_hidden.min():.3f}, {position_hidden.max():.3f}]")
                
                # 数据类型对齐
                if position_hidden.dtype != lm_head.weight.dtype:
                    position_hidden = position_hidden.to(dtype=lm_head.weight.dtype)
                
                # Logit Lens的核心计算：hidden_state @ lm_head
                logger.info("   🔥 核心计算: hidden_state @ lm_head.weight^T")
                lens_logits = lm_head(position_hidden.unsqueeze(0))  # [1, vocab_size]
                lens_logits = lens_logits.squeeze(0)  # [vocab_size]
                
                logger.info(f"   ✓ Logits形状: {lens_logits.shape}")
                logger.info(f"   ✓ Logits范围: [{lens_logits.min():.3f}, {lens_logits.max():.3f}]")
                
                # 转换为概率分布
                lens_probs = F.softmax(lens_logits, dim=-1)
                logger.info(f"   ✓ 概率和: {lens_probs.sum():.6f} (应该≈1.0)")
                
                # 获取top-5预测
                top_probs, top_indices = torch.topk(lens_probs, 5)
                
                logger.info("   🏆 Top-5 预测:")
                for i in range(5):
                    token_id = top_indices[i].item()
                    prob = top_probs[i].item()
                    
                    try:
                        # 解码token
                        token_text = self.processor.tokenizer.decode([token_id])
                        # 清理显示
                        clean_token = token_text.replace('\n', '\\n').replace('\r', '\\r')
                        if len(clean_token) > 20:
                            clean_token = clean_token[:20] + "..."
                            
                        logger.info(f"      {i+1}. Token {token_id}: '{clean_token}' (概率: {prob:.4f})")
                        
                        # 特别标注SpatialVLA的特殊token
                        if token_id >= 256000:  # SpatialVLA的特殊token通常在这个范围
                            if 256000 <= token_id <= 257000:
                                logger.info(f"         ↳ 这是位置token <loc{token_id-256000}>")
                            elif 257000 <= token_id <= 257100:
                                logger.info(f"         ↳ 这是分割token <seg{token_id-257000}>")
                            else:
                                logger.info(f"         ↳ 这是其他特殊动作token")
                    except Exception as e:
                        logger.info(f"      {i+1}. Token {token_id}: <解码失败> (概率: {prob:.4f})")
            
            # 第5步：对比最终输出
            logger.info(f"\n🎯 第5步：对比最终层(Layer {len(hidden_states)-1})的输出...")
            final_layer_hidden = hidden_states[-1][0, example_pos, :]
            if final_layer_hidden.dtype != lm_head.weight.dtype:
                final_layer_hidden = final_layer_hidden.to(dtype=lm_head.weight.dtype)
                
            final_layer_logits = lm_head(final_layer_hidden.unsqueeze(0)).squeeze(0)
            final_layer_probs = F.softmax(final_layer_logits, dim=-1)
            final_top_probs, final_top_indices = torch.topk(final_layer_probs, 5)
            
            logger.info("   最终层Top-5预测:")
            for i in range(5):
                token_id = final_top_indices[i].item()
                prob = final_top_probs[i].item()
                try:
                    token_text = self.processor.tokenizer.decode([token_id])
                    clean_token = token_text.replace('\n', '\\n').replace('\r', '\\r')
                    if len(clean_token) > 20:
                        clean_token = clean_token[:20] + "..."
                    logger.info(f"      {i+1}. '{clean_token}' (概率: {prob:.4f})")
                except:
                    logger.info(f"      {i+1}. Token {token_id} (概率: {prob:.4f})")
            
            # 第6步：验证与正常推理的一致性
            logger.info(f"\n✅ 第6步：验证一致性...")
            normal_final_logits = final_logits[0, example_pos, :]  # 正常推理的该位置logits
            our_final_logits = final_layer_logits  # 我们计算的logits
            
            # 计算差异
            logits_diff = torch.abs(normal_final_logits - our_final_logits).max()
            logger.info(f"   最大logits差异: {logits_diff:.6f} (应该≈0)")
            
            if logits_diff < 1e-4:
                logger.info("   ✅ 验证通过！我们的Logit Lens计算与模型正常推理一致")
            else:
                logger.warning(f"   ⚠️ 验证失败，差异较大: {logits_diff}")
        
        logger.info("\n=== Logit Lens 原理演示完成 ===")
        logger.info("📚 总结:")
        logger.info("   1. Logit Lens利用预训练好的lm_head")
        logger.info("   2. 中间层隐藏状态包含'半成品'的语义信息")
        logger.info("   3. 通过lm_head可以将其转换为token概率分布")
        logger.info("   4. 这让我们看到模型的'思考过程'演化")
        logger.info("   5. SpatialVLA的特殊之处是包含位置和动作token")

def main():
    # 创建解析器
    parser = argparse.ArgumentParser(description="SpatialVLA Visual Interpretation Analysis")
    
    # 添加命令行参数
    parser.add_argument("--model_path", type=str, default="IPEC-COMMUNITY/spatialvla-4b-224-pt", help="模型路径")
    parser.add_argument("--device", type=str, default="cuda:2", help="设备")
    parser.add_argument("--dtype", type=str, default="bf16", help="数据类型")
    parser.add_argument("--task_name", type=str, default="google_robot_pick_coke_can", help="任务名称")
    parser.add_argument("--seed", type=int, default=1234, help="随机种子")
    parser.add_argument("--policy_setup", type=str, default="google_robot", help="策略设置")
    parser.add_argument("--action_scale", type=float, default=1.0, help="动作缩放比例")
    parser.add_argument("--action_ensemble_temp", type=float, default=-0.8, help="动作集成温度")
    parser.add_argument("--max_samples", type=int, default=100, help="最大样本数")
    parser.add_argument("--output_dir", type=str, default="./visual_interpretation_results", help="输出目录")
    parser.add_argument("--save_intermediate", type=bool, default=True, help="是否保存中间结果")
    parser.add_argument("--enable_logit_lens", type=bool, default=True, help="是否启用Logit Lens")
    parser.add_argument("--target_layers", type=list, default=[0, 21, 22, 23, 24, 25, 26], help="目标层")
    parser.add_argument("--enable_enhanced_logit_lens", type=bool, default=True, help="是否启用增强的Logit Lens可视化")
    parser.add_argument("--enable_ablation", type=bool, default=True, help="是否启用消融研究")
    parser.add_argument("--ablation_methods", type=list, default=["object_tokens", "object_with_buffer", "random_tokens", "high_gradient_tokens"], help="消融方法")
    parser.add_argument("--buffer_sizes", type=list, default=[1, 2], help="缓冲区大小")
    parser.add_argument("--enable_attention_knockout", type=bool, default=True, help="是否启用注意力阻断分析")
    parser.add_argument("--layer_windows", type=list, default=[(1, 10), (5, 14), (11, 20), (15, 24), (21, 31)], help="注意力层窗口")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 创建配置对象
    config = VisualInterpretationConfig(
        model_path=args.model_path,
        device=args.device,
        dtype=args.dtype,
        task_name=args.task_name,
        seed=args.seed,
        policy_setup=args.policy_setup,
        action_scale=args.action_scale,
        action_ensemble_temp=args.action_ensemble_temp,
        max_samples=args.max_samples,
        output_dir=args.output_dir,
        save_intermediate=args.save_intermediate,
        enable_logit_lens=args.enable_logit_lens,
        target_layers=args.target_layers,
        enable_enhanced_logit_lens=args.enable_enhanced_logit_lens,
        enable_ablation=args.enable_ablation,
        ablation_methods=args.ablation_methods,
        buffer_sizes=args.buffer_sizes,
        enable_attention_knockout=args.enable_attention_knockout,
        layer_windows=args.layer_windows
    )
    
    # 创建SpatialVLA视觉解释分析器
    interpreter = SpatialVLAVisualInterpreter(config)
    
    # 分析样本
    results = interpreter.analyze_sample()
    
    # 创建可视化
    save_path = os.path.join(args.output_dir, "visualization.png")
    interpreter.create_visualization(results, save_path)
    
    # 保存分析结果
    sample_id = datetime.now().strftime("%Y%m%d_%H%M%S")
    interpreter.save_results(results, sample_id)

if __name__ == "__main__":
    main()