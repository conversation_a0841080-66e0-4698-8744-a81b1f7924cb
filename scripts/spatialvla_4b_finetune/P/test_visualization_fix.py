#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化修复效果
"""

import os
import sys
import logging
from PIL import Image
import tempfile

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入分析器
try:
    from paligemma2_logit_lens_analysis import PaliGemma2LogitLensAnalyzer, PaliGemma2LogitLensConfig
    from paligemma2_interactive_visualization import PaliGemma2InteractiveVisualizer
    logger.info("✅ 成功导入PaliGemma2分析组件")
except ImportError as e:
    logger.error(f"❌ 导入失败: {e}")
    sys.exit(1)

def test_visualization_fix():
    """测试可视化修复效果"""
    
    # 创建测试图像
    test_image = Image.new('RGB', (224, 224), color='blue')
    with tempfile.NamedTemporaryFile(suffix='.png', delete=False) as tmp:
        test_image.save(tmp.name)
        image_path = tmp.name
    
    try:
        # 创建配置（使用CPU以避免GPU问题）
        config = PaliGemma2LogitLensConfig(
            model_path="google/paligemma2-3b-pt-224",
            device="cpu",
            dtype="fp32",
            output_dir="./test_outputs",
            target_layers=[20, 24],  # 只测试2层
            max_samples=1,
            enable_visualization=True,
            max_tokens_display=3
        )
        
        logger.info("🚀 创建分析器...")
        analyzer = PaliGemma2LogitLensAnalyzer(config)
        
        logger.info("🔍 运行分析...")
        results = analyzer.analyze_sample(image_path, "describe the image")
        
        if 'error' in results:
            logger.error(f"❌ 分析失败: {results['error']}")
            return False
        
        logger.info("🎨 创建可视化...")
        visualizer = PaliGemma2InteractiveVisualizer(output_dir="./test_outputs")
        
        # 生成交互式HTML
        html_path = "./test_outputs/test_visualization_fix.html"
        if 'logit_lens' in results and results['logit_lens']:
            
            # 检查数据结构
            logit_data = results['logit_lens']
            logger.info(f"📊 数据检查:")
            logger.info(f"  - 结果数量: {len(logit_data.get('results', []))}")
            
            # 处理数据并检查键类型
            processed_data = visualizer._process_logit_lens_data(logit_data)
            logger.info(f"  - 处理后的patches数量: {len(processed_data.get('patches', {}))}")
            logger.info(f"  - 样本patch键: {list(processed_data.get('patches', {}).keys())[:5]}")
            
            visualizer.create_interactive_html(
                logit_lens_results=logit_data,
                image_path=image_path,
                save_path=html_path
            )
            
            logger.info(f"✅ 可视化已保存到: {html_path}")
            
            # 检查HTML文件内容
            with open(html_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
                
            # 验证修复
            if 'const patchKey = i.toString();' in html_content:
                logger.info("✅ 修复已应用：发现字符串转换代码")
            else:
                logger.warning("⚠️ 修复可能未完全应用")
                
            if 'console.log(' in html_content:
                logger.info("✅ 调试信息已添加")
            else:
                logger.warning("⚠️ 调试信息可能未添加")
                
            return True
        else:
            logger.error("❌ 未生成有效的logit lens结果")
            return False
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(image_path)
        except:
            pass

if __name__ == "__main__":
    logger.info("🧪 开始测试可视化修复...")
    success = test_visualization_fix()
    
    if success:
        logger.info("🎉 测试成功！可视化修复已应用")
        logger.info("💡 建议：")
        logger.info("  1. 在浏览器中打开生成的HTML文件")
        logger.info("  2. 打开浏览器开发者工具查看console日志")
        logger.info("  3. 验证patch grid是否正确显示")
    else:
        logger.error("❌ 测试失败，请检查错误信息")
