#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Logit Lens Analyzer for Hugging Face Spaces
======================================================

Simplified version of the logit lens analyzer optimized for web deployment.
"""

import os
import torch
import torch.nn.functional as F
from typing import Dict, List, Tuple, Optional, Any
import numpy as np
from PIL import Image
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import io
import base64
import logging
from dataclasses import dataclass

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    from transformers import (
        AutoProcessor, 
        PaliGemmaForConditionalGeneration,
        set_seed
    )
except ImportError as e:
    logger.error(f"Failed to import transformers: {e}")
    raise


@dataclass
class LogitLensConfig:
    """Simplified configuration for web deployment"""
    model_path: str = "google/paligemma2-3b-pt-224"
    device: str = "cuda" if torch.cuda.is_available() else "cpu"
    dtype: str = "fp16"
    target_layers: List[int] = None
    max_patches_analyze: int = 64  # Limit for web performance
    max_text_tokens: int = 8
    
    def __post_init__(self):
        if self.target_layers is None:
            self.target_layers = list(range(15, 26))  # Last 11 layers


class PaliGemma2LogitLensAnalyzer:
    """Simplified Logit Lens Analyzer for Gradio deployment"""
    
    def __init__(self, config: LogitLensConfig):
        self.config = config
        self.device = torch.device(config.device)
        self.model = None
        self.processor = None
        
        # Set random seed for reproducibility
        set_seed(42)
        
        logger.info(f"Initializing analyzer on device: {self.device}")
    
    def load_model(self, progress_callback=None):
        """Load model with progress tracking"""
        if self.model is not None:
            return  # Already loaded
            
        try:
            if progress_callback:
                progress_callback("Loading processor...")
            
            self.processor = AutoProcessor.from_pretrained(self.config.model_path)
            
            if progress_callback:
                progress_callback("Loading model...")
            
            # Configure dtype
            dtype_map = {
                "fp16": torch.float16,
                "fp32": torch.float32,
                "bf16": torch.bfloat16
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.float16)
            
            self.model = PaliGemmaForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device,
                low_cpu_mem_usage=True
            )
            
            self.model.eval()
            
            # Get model info
            self.num_layers = len(self.model.language_model.model.layers)
            self.hidden_size = self.model.language_model.config.hidden_size
            self.vocab_size = self.model.language_model.config.vocab_size
            
            if progress_callback:
                progress_callback("Model loaded successfully!")
            
            logger.info(f"Model loaded: {self.num_layers} layers, {self.hidden_size} hidden size")
            
        except Exception as e:
            logger.error(f"Failed to load model: {e}")
            raise
    
    def analyze_image(self, image: Image.Image, prompt: str, 
                     progress_callback=None) -> Dict[str, Any]:
        """Analyze image with logit lens"""
        
        if self.model is None:
            self.load_model(progress_callback)
        
        try:
            if progress_callback:
                progress_callback("Preprocessing input...")
            
            # Preprocess inputs
            inputs = self.processor(
                text=prompt,
                images=image,
                return_tensors="pt"
            )
            
            # Move to device
            for key, value in inputs.items():
                if torch.is_tensor(value):
                    inputs[key] = value.to(self.device)
            
            if progress_callback:
                progress_callback("Running model inference...")
            
            with torch.no_grad():
                # Get hidden states
                outputs = self.model(**inputs, output_hidden_states=True, return_dict=True)
                
                # Extract hidden states
                if hasattr(outputs, 'language_model_outputs') and outputs.language_model_outputs is not None:
                    hidden_states = outputs.language_model_outputs.hidden_states
                else:
                    hidden_states = outputs.hidden_states
                
                if progress_callback:
                    progress_callback("Analyzing token positions...")
                
                # Get token positions
                vision_positions = self._extract_vision_positions(inputs)
                text_positions = self._extract_text_positions(inputs)
                
                if progress_callback:
                    progress_callback("Running logit lens analysis...")
                
                # Run logit lens analysis
                results = self._run_logit_lens(
                    hidden_states, vision_positions, text_positions, prompt
                )
                
                if progress_callback:
                    progress_callback("Generating visualizations...")
                
                # Generate visualizations
                viz_data = self._create_visualizations(results)
                
                return {
                    'success': True,
                    'results': results,
                    'visualizations': viz_data,
                    'stats': {
                        'num_vision_tokens': len(vision_positions),
                        'num_text_tokens': len(text_positions),
                        'layers_analyzed': len(self.config.target_layers),
                        'sequence_length': inputs['input_ids'].shape[1]
                    }
                }
                
        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'results': [],
                'visualizations': {},
                'stats': {}
            }
    
    def _extract_vision_positions(self, inputs: Dict[str, torch.Tensor]) -> List[int]:
        """Extract vision token positions (first 256 tokens)"""
        seq_len = inputs['input_ids'].shape[1]
        vision_tokens = min(256, seq_len // 2)  # Conservative estimate
        return list(range(vision_tokens))
    
    def _extract_text_positions(self, inputs: Dict[str, torch.Tensor]) -> List[int]:
        """Extract text token positions"""
        seq_len = inputs['input_ids'].shape[1]
        vision_tokens = min(256, seq_len // 2)
        if seq_len > vision_tokens:
            return list(range(vision_tokens, seq_len))
        return []
    
    def _run_logit_lens(self, hidden_states, vision_positions, text_positions, prompt):
        """Run logit lens analysis on selected positions"""
        
        # Get model components
        language_model = self.model.language_model
        norm_layer = language_model.model.norm
        lm_head = language_model.lm_head
        
        results = []
        
        # Limit analysis for performance
        vision_sample = vision_positions[:self.config.max_patches_analyze]
        text_sample = text_positions[:self.config.max_text_tokens]
        
        for layer_idx in self.config.target_layers:
            if layer_idx >= len(hidden_states):
                continue
                
            layer_hidden = hidden_states[layer_idx]  # [1, seq_len, hidden_size]
            
            # Apply norm and lm_head
            normalized_hidden = norm_layer(layer_hidden)
            layer_logits = lm_head(normalized_hidden)
            layer_probs = F.softmax(layer_logits, dim=-1)
            
            # Analyze vision tokens
            for i, pos in enumerate(vision_sample):
                if pos >= layer_probs.shape[1]:
                    continue
                    
                position_probs = layer_probs[0, pos, :]
                top_probs, top_indices = torch.topk(position_probs, 5)
                
                top_tokens = []
                for j in range(5):
                    token_id = top_indices[j].item()
                    try:
                        token_text = self.processor.tokenizer.decode([token_id])
                        prob = top_probs[j].item()
                        top_tokens.append({
                            'token': token_text,
                            'probability': prob,
                            'token_id': token_id
                        })
                    except:
                        continue
                
                if top_tokens:
                    results.append({
                        'layer': layer_idx,
                        'position': pos,
                        'token_type': 'vision',
                        'patch_id': i,
                        'patch_row': i // 16,
                        'patch_col': i % 16,
                        'top_tokens': top_tokens,
                        'prompt': prompt
                    })
            
            # Analyze text tokens
            for i, pos in enumerate(text_sample):
                if pos >= layer_probs.shape[1]:
                    continue
                    
                position_probs = layer_probs[0, pos, :]
                top_probs, top_indices = torch.topk(position_probs, 5)
                
                top_tokens = []
                for j in range(5):
                    token_id = top_indices[j].item()
                    try:
                        token_text = self.processor.tokenizer.decode([token_id])
                        prob = top_probs[j].item()
                        top_tokens.append({
                            'token': token_text,
                            'probability': prob,
                            'token_id': token_id
                        })
                    except:
                        continue
                
                if top_tokens:
                    results.append({
                        'layer': layer_idx,
                        'position': pos,
                        'token_type': 'text',
                        'text_token_id': i,
                        'top_tokens': top_tokens,
                        'prompt': prompt
                    })
        
        return results
    
    def _create_visualizations(self, results: List[Dict]) -> Dict[str, str]:
        """Create visualization plots and return as base64 strings"""
        
        viz_data = {}
        
        try:
            # 1. Layer evolution plot
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # Group by layer and token type
            vision_results = [r for r in results if r['token_type'] == 'vision']
            text_results = [r for r in results if r['token_type'] == 'text']
            
            if vision_results:
                layers = sorted(set(r['layer'] for r in vision_results))
                avg_probs = []
                
                for layer in layers:
                    layer_results = [r for r in vision_results if r['layer'] == layer]
                    if layer_results:
                        probs = [r['top_tokens'][0]['probability'] for r in layer_results if r['top_tokens']]
                        avg_probs.append(np.mean(probs) if probs else 0)
                    else:
                        avg_probs.append(0)
                
                ax.plot(layers, avg_probs, 'b-o', label='Vision Tokens', linewidth=2)
            
            if text_results:
                layers = sorted(set(r['layer'] for r in text_results))
                avg_probs = []
                
                for layer in layers:
                    layer_results = [r for r in text_results if r['layer'] == layer]
                    if layer_results:
                        probs = [r['top_tokens'][0]['probability'] for r in layer_results if r['top_tokens']]
                        avg_probs.append(np.mean(probs) if probs else 0)
                    else:
                        avg_probs.append(0)
                
                ax.plot(layers, avg_probs, 'r-s', label='Text Tokens', linewidth=2)
            
            ax.set_xlabel('Layer')
            ax.set_ylabel('Average Top-1 Probability')
            ax.set_title('Token Prediction Confidence Across Layers')
            ax.legend()
            ax.grid(True, alpha=0.3)
            
            # Convert to base64
            buffer = io.BytesIO()
            plt.savefig(buffer, format='png', dpi=150, bbox_inches='tight')
            buffer.seek(0)
            viz_data['layer_evolution'] = base64.b64encode(buffer.getvalue()).decode()
            plt.close()
            
        except Exception as e:
            logger.error(f"Visualization creation failed: {e}")
            viz_data['error'] = str(e)
        
        return viz_data
