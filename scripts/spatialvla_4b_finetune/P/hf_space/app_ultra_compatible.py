#!/usr/bin/env python3
"""
PaliGemma2 Logit Lens Analysis - Ultra Compatible Version
=========================================================
Fixed for Gradio 4.44.0 + Pydantic compatibility issues
"""

import gradio as gr
import torch
import os
import logging
from PIL import Image
import traceback

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global state
analyzer = None
model_loaded = False

def load_analyzer():
    """Load the analyzer with error handling"""
    global analyzer, model_loaded
    
    if model_loaded:
        return "✅ Model already loaded!"
    
    try:
        # Import with fallback
        try:
            from logit_lens_analyzer import PaliGemma2LogitLensAnalyzer, LogitLensConfig
        except ImportError:
            return "❌ logit_lens_analyzer module not found. Please ensure it's in the same directory."
        
        # Create config with conservative settings
        config = LogitLensConfig(
            model_path="google/paligemma2-3b-pt-224",
            device="cuda" if torch.cuda.is_available() else "cpu",
            dtype="fp16" if torch.cuda.is_available() else "fp32",
            target_layers=list(range(22, 26)),  # Only last few layers
            max_patches_analyze=8,  # Very conservative
            max_text_tokens=3
        )
        
        analyzer = PaliGemma2LogitLensAnalyzer(config)
        analyzer.load_model()
        model_loaded = True
        
        device_info = f"{config.device} ({'GPU' if torch.cuda.is_available() else 'CPU'})"
        return f"✅ Model loaded successfully on {device_info}!"
        
    except Exception as e:
        logger.error(f"Load failed: {e}")
        return f"❌ Load failed: {str(e)[:200]}..."

def run_analysis(image, prompt):
    """Run analysis with comprehensive error handling"""
    
    if not model_loaded or analyzer is None:
        return "❌ Please load the model first!", "", ""
    
    if image is None:
        return "❌ Please upload an image!", "", ""
    
    if not prompt or not prompt.strip():
        return "❌ Please provide a prompt!", "", ""
    
    try:
        # Run analysis
        results = analyzer.analyze_image(image, prompt.strip())
        
        if not results.get('success', False):
            error_msg = results.get('error', 'Unknown error occurred')
            return f"❌ Analysis failed: {error_msg}", "", ""
        
        # Process results safely
        summary = create_summary_safe(results)
        details = create_details_safe(results)
        viz = create_visualization_safe(results)
        
        return summary, details, viz
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        error_trace = traceback.format_exc()
        return f"❌ Error: {str(e)}", f"```\n{error_trace}\n```", ""

def create_summary_safe(results):
    """Create summary with safe access"""
    try:
        stats = results.get('stats', {})
        
        summary = "# 📊 Analysis Complete\n\n"
        summary += f"**Sequence Length:** {stats.get('sequence_length', 'N/A')}\n"
        summary += f"**Vision Tokens:** {stats.get('num_vision_tokens', 'N/A')}\n"
        summary += f"**Text Tokens:** {stats.get('num_text_tokens', 'N/A')}\n"
        summary += f"**Layers Analyzed:** {stats.get('layers_analyzed', 'N/A')}\n\n"
        
        analysis_results = results.get('results', [])
        summary += f"**Total Results:** {len(analysis_results)}\n"
        
        vision_count = len([r for r in analysis_results if r.get('token_type') == 'vision'])
        text_count = len([r for r in analysis_results if r.get('token_type') == 'text'])
        
        summary += f"**Vision Results:** {vision_count}\n"
        summary += f"**Text Results:** {text_count}\n"
        
        return summary
        
    except Exception as e:
        return f"Error creating summary: {str(e)}"

def create_details_safe(results):
    """Create details with safe access"""
    try:
        analysis_results = results.get('results', [])
        
        if not analysis_results:
            return "No detailed results available."
        
        details = "# 🔬 Top Predictions\n\n"
        details += "| Layer | Type | Token | Probability |\n"
        details += "|-------|------|-------|-------------|\n"
        
        count = 0
        for result in analysis_results:
            if count >= 10:  # Limit to top 10
                break
                
            if not result.get('top_tokens'):
                continue
                
            layer = result.get('layer', 'N/A')
            token_type = result.get('token_type', 'N/A')
            
            top_token = result['top_tokens'][0]
            token_text = str(top_token.get('token', ''))[:8]  # Truncate
            token_text = token_text.replace('|', '\\|')  # Escape pipes
            prob = top_token.get('probability', 0)
            
            details += f"| {layer} | {token_type} | `{token_text}` | {prob:.3f} |\n"
            count += 1
        
        return details
        
    except Exception as e:
        return f"Error creating details: {str(e)}"

def create_visualization_safe(results):
    """Create visualization with safe access"""
    try:
        viz_data = results.get('visualizations', {})
        
        if not viz_data:
            return "<p>No visualization data available.</p>"
        
        html = "<div style='text-align: center; padding: 20px;'>"
        html += "<h2>📊 Analysis Visualization</h2>"
        
        if 'layer_evolution' in viz_data:
            html += f"""
            <div style='margin: 20px 0;'>
                <h3>Layer Evolution</h3>
                <img src='data:image/png;base64,{viz_data['layer_evolution']}' 
                     style='max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px;'>
            </div>
            """
        else:
            html += "<p>Visualization generation in progress...</p>"
        
        html += "</div>"
        return html
        
    except Exception as e:
        return f"<p>Error creating visualization: {str(e)}</p>"

# Create interface with maximum compatibility
def create_interface():
    
    # Use simple CSS for compatibility
    css = """
    .container { max-width: 1200px; margin: 0 auto; }
    .status-box { background: #f0f8ff; padding: 10px; border-radius: 5px; }
    .error-box { background: #ffe6e6; padding: 10px; border-radius: 5px; }
    """
    
    with gr.Blocks(css=css, title="PaliGemma2 Logit Lens") as interface:
        
        gr.HTML("""
        <div style='text-align: center; margin: 20px 0;'>
            <h1>🔍 PaliGemma2 Logit Lens Analysis</h1>
            <p>Analyze how PaliGemma2 processes images and text</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # Model loading section
                gr.HTML("<h3>Step 1: Load Model</h3>")
                load_btn = gr.Button("🚀 Load Model", variant="primary", size="lg")
                status_display = gr.Textbox(
                    label="Status", 
                    value="Click 'Load Model' to begin",
                    interactive=False,
                    lines=3
                )
                
                # Input section
                gr.HTML("<h3>Step 2: Provide Input</h3>")
                image_input = gr.Image(
                    label="Upload Image",
                    type="pil",
                    height=200
                )
                
                text_input = gr.Textbox(
                    label="Prompt",
                    value="Describe this image",
                    lines=2
                )
                
                analyze_btn = gr.Button("🔬 Analyze", variant="secondary", size="lg")
        
        # Results section
        gr.HTML("<h3>📊 Results</h3>")
        
        with gr.Tabs():
            with gr.TabItem("📋 Summary"):
                summary_output = gr.Markdown(label="Analysis Summary")
            
            with gr.TabItem("🔍 Details"):
                details_output = gr.Markdown(label="Detailed Results")
            
            with gr.TabItem("📊 Visualization"):
                viz_output = gr.HTML(label="Visualization")
        
        # Instructions
        gr.HTML("""
        <div style='background: #f0f8ff; padding: 15px; margin: 20px 0; border-radius: 8px;'>
            <h4>💡 Instructions:</h4>
            <ol>
                <li><strong>Load Model:</strong> Click "Load Model" and wait 2-3 minutes</li>
                <li><strong>Upload Image:</strong> Choose a clear, interesting image</li>
                <li><strong>Enter Prompt:</strong> Provide a descriptive prompt</li>
                <li><strong>Analyze:</strong> Click "Analyze" and wait for results</li>
            </ol>
            <p><strong>Note:</strong> First analysis may take longer as the model initializes.</p>
        </div>
        """)
        
        # Event handlers
        load_btn.click(
            fn=load_analyzer,
            outputs=[status_display]
        )
        
        analyze_btn.click(
            fn=run_analysis,
            inputs=[image_input, text_input],
            outputs=[summary_output, details_output, viz_output]
        )
    
    return interface

# Main execution
if __name__ == "__main__":
    demo = create_interface()
    
    # Launch with Spaces-compatible settings
    demo.launch(
        server_name="0.0.0.0",
        server_port=7860,
        show_error=True,
        quiet=False
    )
