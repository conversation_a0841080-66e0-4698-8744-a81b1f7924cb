#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for PaliGemma2 Logit Lens Analysis
==============================================

Simple test to verify the application works correctly.
"""

import sys
import os
import torch
from PIL import Image
import numpy as np

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test if all imports work correctly"""
    print("🔍 Testing imports...")
    
    try:
        import gradio as gr
        print("✅ Gradio imported successfully")
    except ImportError as e:
        print(f"❌ Gradio import failed: {e}")
        return False
    
    try:
        from transformers import AutoProcessor, PaliGemmaForConditionalGeneration
        print("✅ Transformers imported successfully")
    except ImportError as e:
        print(f"❌ Transformers import failed: {e}")
        return False
    
    try:
        from logit_lens_analyzer import PaliGemma2LogitLensAnalyzer, LogitLensConfig
        print("✅ Logit lens analyzer imported successfully")
    except ImportError as e:
        print(f"❌ Logit lens analyzer import failed: {e}")
        return False
    
    try:
        import utils
        print("✅ Utils imported successfully")
    except ImportError as e:
        print(f"❌ Utils import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration creation"""
    print("\n🔧 Testing configuration...")
    
    try:
        from logit_lens_analyzer import LogitLensConfig
        
        config = LogitLensConfig()
        print(f"✅ Default config created")
        print(f"   Model: {config.model_path}")
        print(f"   Device: {config.device}")
        print(f"   Target layers: {config.target_layers}")
        
        return True
    except Exception as e:
        print(f"❌ Config creation failed: {e}")
        return False

def test_analyzer_creation():
    """Test analyzer creation (without model loading)"""
    print("\n🤖 Testing analyzer creation...")
    
    try:
        from logit_lens_analyzer import PaliGemma2LogitLensAnalyzer, LogitLensConfig
        
        config = LogitLensConfig(
            target_layers=[20, 21, 22],  # Just a few layers for testing
            max_patches_analyze=4,
            max_text_tokens=2
        )
        
        analyzer = PaliGemma2LogitLensAnalyzer(config)
        print("✅ Analyzer created successfully")
        print(f"   Device: {analyzer.device}")
        print(f"   Config: {analyzer.config}")
        
        return True
    except Exception as e:
        print(f"❌ Analyzer creation failed: {e}")
        return False

def test_gradio_interface():
    """Test Gradio interface creation"""
    print("\n🌐 Testing Gradio interface...")
    
    try:
        # Import the app module
        import app
        
        # Try to create the interface
        demo = app.create_interface()
        print("✅ Gradio interface created successfully")
        
        return True
    except Exception as e:
        print(f"❌ Gradio interface creation failed: {e}")
        return False

def test_utils():
    """Test utility functions"""
    print("\n🛠️ Testing utility functions...")
    
    try:
        import utils
        
        # Test token formatting
        token = utils.format_token_for_display("test|token\nwith|special")
        print(f"✅ Token formatting: '{token}'")
        
        # Test device info
        device_info = utils.get_device_info()
        print(f"✅ Device info: {device_info}")
        
        # Test input validation with dummy data
        dummy_image = Image.new('RGB', (224, 224), color='red')
        valid, msg = utils.validate_inputs(dummy_image, "test prompt")
        print(f"✅ Input validation: {valid}, {msg}")
        
        return True
    except Exception as e:
        print(f"❌ Utils testing failed: {e}")
        return False

def create_test_image():
    """Create a simple test image"""
    print("\n🖼️ Creating test image...")
    
    try:
        # Create a simple test image
        image = Image.new('RGB', (224, 224), color='blue')
        
        # Add some simple patterns
        pixels = image.load()
        for i in range(224):
            for j in range(224):
                if (i + j) % 50 < 25:
                    pixels[i, j] = (255, 255, 255)  # White squares
        
        test_image_path = "test_image.jpg"
        image.save(test_image_path)
        print(f"✅ Test image created: {test_image_path}")
        
        return test_image_path
    except Exception as e:
        print(f"❌ Test image creation failed: {e}")
        return None

def main():
    """Run all tests"""
    print("🚀 Starting PaliGemma2 Logit Lens Analysis Tests")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Analyzer Creation", test_analyzer_creation),
        ("Gradio Interface", test_gradio_interface),
        ("Utilities", test_utils),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Create test image
    test_image_path = create_test_image()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The application should work correctly.")
        print("\n💡 Next steps:")
        print("   1. Upload this folder to Hugging Face Spaces")
        print("   2. Set hardware to T4-small or better")
        print("   3. Wait for the model to load on first run")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
    
    # Cleanup
    if test_image_path and os.path.exists(test_image_path):
        os.remove(test_image_path)
        print(f"🧹 Cleaned up test image: {test_image_path}")

if __name__ == "__main__":
    main()
