# 🔐 Hugging Face Token 设置指南

## 问题说明

PaliGemma2 是一个 **gated model**（受限模型），需要通过 Hugging Face token 进行身份验证才能访问。本指南将详细说明如何正确设置token。

## 🚨 重要安全提醒

**绝对不要在代码中硬编码token！** Hugging Face Space会自动检测并阻止包含硬编码token的部署。

## 📋 设置步骤

### 步骤 1: 获取 Hugging Face Token

1. **登录 Hugging Face**
   - 访问 [Hugging Face](https://huggingface.co/)
   - 登录您的账户

2. **创建 Access Token**
   - 访问 [Token Settings](https://huggingface.co/settings/tokens)
   - 点击 "New token"
   - 设置token名称（例如：`paligemma2-access`）
   - 选择权限：**Read** （读取权限足够）
   - 点击 "Generate a token"
   - **复制并保存token**（格式类似：`hf_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`）

### 步骤 2: 申请模型访问权限

1. **访问模型页面**
   - 前往 [PaliGemma2 模型页面](https://huggingface.co/google/paligemma2-3b-pt-224)

2. **申请访问**
   - 点击 "Request access" 按钮
   - 填写申请表单（通常需要说明使用目的）
   - 等待 Google 团队批准（通常几小时到几天）

### 步骤 3: 在 Hugging Face Space 中设置环境变量

1. **进入 Space 设置**
   - 在您的 Space 页面，点击 "Settings" 标签

2. **添加 Secret**
   - 滚动到 "Repository secrets" 部分
   - 点击 "New secret"
   - 设置：
     - **Name**: `HF_TOKEN`
     - **Value**: 您的完整token（例如：`*************************************`）
   - 点击 "Add secret"

3. **重启 Space**
   - 保存后，Space 会自动重启
   - 新的环境变量将在重启后生效

## 🧪 本地测试

如果您想在本地测试，需要设置环境变量：

### Linux/macOS:
```bash
export HF_TOKEN=your_token_here
python test_token_fix.py
```

### Windows (Command Prompt):
```cmd
set HF_TOKEN=your_token_here
python test_token_fix.py
```

### Windows (PowerShell):
```powershell
$env:HF_TOKEN="your_token_here"
python test_token_fix.py
```

## 🔍 验证设置

运行测试脚本来验证token是否正确设置：

```bash
python test_token_fix.py
```

成功的输出应该类似：
```
Testing access to google/paligemma2-3b-pt-224...
Using token: hf_xxxxxxx...
✅ Success! Token authentication works.
Processor loaded: <class 'transformers.models.paligemma.processing_paligemma.PaliGemmaProcessor'>
```

## ❌ 常见错误及解决方案

### 错误 1: "HF_TOKEN environment variable is not set"
**解决方案**: 确保在 Space secrets 中正确设置了 `HF_TOKEN`

### 错误 2: "401 Client Error" 或 "Access to model is restricted"
**解决方案**: 
- 检查token是否有效
- 确认已申请并获得模型访问权限
- 等待访问权限批准

### 错误 3: "You are trying to access a gated repo"
**解决方案**: 
- 确认已在模型页面申请访问权限
- 等待 Google 团队批准您的访问请求

## 📞 获取帮助

如果遇到问题：
1. 检查 [Hugging Face 文档](https://huggingface.co/docs/hub/security-tokens)
2. 确认模型访问权限状态
3. 验证token格式和权限设置

## 🎯 最终检查清单

- [ ] 已获取有效的 Hugging Face token
- [ ] 已申请并获得 PaliGemma2 模型访问权限
- [ ] 已在 Space secrets 中设置 `HF_TOKEN` 环境变量
- [ ] 代码中没有硬编码任何token
- [ ] 测试脚本运行成功

完成以上所有步骤后，您的 Space 应该能够成功访问 PaliGemma2 模型了！
