# 🚀 PaliGemma2 Logit Lens Analysis - 部署指南

这个指南将帮助您将PaliGemma2 Logit Lens分析工具部署到Hugging Face Spaces。

## 📋 部署前检查

### 1. 文件结构确认
确保您的文件夹包含以下文件：
```
hf_space/
├── app.py                    # 主Gradio应用
├── logit_lens_analyzer.py    # 核心分析逻辑
├── utils.py                  # 辅助函数
├── requirements.txt          # Python依赖
├── README.md                 # Space配置和描述
├── test_app.py              # 测试脚本
└── DEPLOYMENT_GUIDE.md      # 本指南
```

### 2. 本地测试（可选但推荐）
在部署前，您可以在本地测试应用：

```bash
# 进入项目目录
cd hf_space

# 安装依赖（建议使用虚拟环境）
pip install -r requirements.txt

# 运行测试脚本
python test_app.py

# 如果测试通过，可以本地运行应用
python app.py
```

## 🌐 部署到Hugging Face Spaces

### 步骤 1: 创建新的Space
1. 访问 [Hugging Face Spaces](https://huggingface.co/spaces)
2. 点击 "Create new Space"
3. 填写以下信息：
   - **Space name**: `paligemma2-logit-lens-analysis`（或您喜欢的名称）
   - **License**: Apache 2.0
   - **SDK**: Gradio
   - **Hardware**: **T4 small**（重要：免费CPU无法运行此模型）
   - **Visibility**: Public 或 Private（根据需要）

### 步骤 2: 配置Hugging Face Token（重要！）
由于PaliGemma2模型是gated model，需要配置访问token：

1. **获取Token**：
   - 访问 [Hugging Face Settings](https://huggingface.co/settings/tokens)
   - 创建一个新的token或使用现有token
   - 确保token有读取权限

2. **在Space中设置环境变量**：
   - 在您的Space页面，点击 "Settings" 标签
   - 在 "Repository secrets" 部分，添加新的secret：
     - **Name**: `HF_TOKEN`
     - **Value**: 您的Hugging Face token（例如：`*************************************`）
   - 点击 "Add secret"

3. **申请模型访问权限**：
   - 访问 [PaliGemma2模型页面](https://huggingface.co/google/paligemma2-3b-pt-224)
   - 点击 "Request access" 并等待批准

### 步骤 3: 上传文件
有两种方式上传文件：

#### 方式A: 通过Web界面
1. 在创建的Space页面，点击 "Files" 标签
2. 逐个上传所有文件：
   - `app.py`
   - `logit_lens_analyzer.py`
   - `utils.py`
   - `requirements.txt`
   - `README.md`

#### 方式B: 通过Git（推荐）
```bash
# 克隆您的Space仓库
git clone https://huggingface.co/spaces/YOUR_USERNAME/YOUR_SPACE_NAME
cd YOUR_SPACE_NAME

# 复制所有文件到仓库目录
cp /path/to/hf_space/* .

# 提交并推送
git add .
git commit -m "Initial deployment of PaliGemma2 Logit Lens Analysis"
git push
```

### 步骤 3: 配置硬件
1. 在Space页面，点击 "Settings" 标签
2. 在 "Hardware" 部分，选择 **T4 small** 或更好的GPU
3. 点击 "Save"

⚠️ **重要**: 此应用需要GPU支持，免费的CPU硬件无法运行PaliGemma2-3B模型。

### 步骤 4: 等待部署
1. Space会自动开始构建
2. 首次部署可能需要5-10分钟
3. 您可以在 "Logs" 标签中查看构建进度

## 🔧 配置选项

### 环境变量（可选）
如果需要，您可以在Space设置中添加环境变量：

- `TORCH_CACHE_DIR`: 设置PyTorch缓存目录
- `HF_HOME`: 设置Hugging Face缓存目录
- `CUDA_VISIBLE_DEVICES`: 限制可见的GPU设备

### 硬件升级选项
根据使用需求，您可以选择不同的硬件：

| 硬件类型 | GPU内存 | 适用场景 | 价格/小时 |
|---------|---------|----------|-----------|
| T4 small | 16GB | 基础使用 | $0.60 |
| T4 medium | 16GB | 更好性能 | $0.90 |
| A10G small | 24GB | 高性能 | $1.05 |

## 🐛 常见问题排查

### 问题 1: 模型加载失败
**症状**: 出现 "CUDA out of memory" 错误
**解决方案**: 
- 升级到更大的GPU硬件
- 在 `logit_lens_analyzer.py` 中修改 `dtype` 为 `"fp16"` 或 `"bf16"`

### 问题 2: 应用启动缓慢
**症状**: 首次访问需要很长时间
**解决方案**: 
- 这是正常的，模型首次加载需要2-3分钟
- 后续使用会更快

### 问题 3: 分析超时
**症状**: 分析过程中出现超时错误
**解决方案**: 
- 减少 `max_patches_analyze` 参数
- 使用更小的图像
- 升级硬件

### 问题 4: 依赖安装失败
**症状**: 构建过程中出现包安装错误
**解决方案**: 
- 检查 `requirements.txt` 中的版本号
- 确保所有包名拼写正确

## 📊 使用指南

### 首次使用
1. 访问您的Space URL
2. 点击 "Initialize Model" 按钮
3. 等待2-3分钟模型加载完成
4. 上传图像并输入提示词
5. 点击 "Run Analysis" 开始分析

### 最佳实践
- **图像**: 使用清晰、内容丰富的图像（224x224到1024x1024像素）
- **提示词**: 使用描述性的提示，如 "Describe this image" 或 "What do you see?"
- **耐心**: 每次分析需要1-2分钟时间

## 🔄 更新和维护

### 更新代码
```bash
# 在本地修改代码后
git add .
git commit -m "Update: description of changes"
git push
```

### 监控使用情况
- 在Space设置中查看使用统计
- 监控GPU使用时间和费用
- 根据需要调整硬件配置

## 📞 支持和反馈

如果遇到问题：
1. 检查Space的 "Logs" 标签查看错误信息
2. 参考本指南的常见问题部分
3. 在Hugging Face论坛寻求帮助
4. 联系开发团队

## 🎉 部署完成

恭喜！您已经成功部署了PaliGemma2 Logit Lens分析工具。现在您可以：

- 分享Space链接给其他用户
- 嵌入到其他网页中
- 用于研究和教育目的
- 进一步定制和扩展功能

---

*祝您使用愉快！如果这个工具对您有帮助，请考虑给项目点个星⭐*
