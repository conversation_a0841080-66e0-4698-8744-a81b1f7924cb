#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utility functions for PaliGemma2 Logit Lens Analysis
===================================================
"""

import torch
import numpy as np
from typing import List, Dict, Any, Tuple
import logging

logger = logging.getLogger(__name__)

def format_token_for_display(token: str, max_length: int = 15) -> str:
    """Format token text for safe display in markdown/HTML"""
    if not token:
        return ""
    
    # Replace problematic characters
    token = token.replace('|', '\\|')  # Escape pipes for markdown tables
    token = token.replace('\n', '\\n')  # Show newlines
    token = token.replace('\r', '\\r')  # Show carriage returns
    token = token.replace('\t', '\\t')  # Show tabs
    
    # Truncate if too long
    if len(token) > max_length:
        token = token[:max_length-3] + "..."
    
    return token

def calculate_entropy(probabilities: torch.Tensor) -> float:
    """Calculate entropy of probability distribution"""
    # Add small epsilon to avoid log(0)
    eps = 1e-10
    probs = probabilities + eps
    entropy = -torch.sum(probs * torch.log(probs))
    return entropy.item()

def get_top_k_tokens(probabilities: torch.Tensor, tokenizer, k: int = 5) -> List[Dict[str, Any]]:
    """Get top-k tokens with their probabilities"""
    top_probs, top_indices = torch.topk(probabilities, k)
    
    tokens = []
    for i in range(k):
        token_id = top_indices[i].item()
        prob = top_probs[i].item()
        
        try:
            token_text = tokenizer.decode([token_id])
            tokens.append({
                'token': token_text,
                'token_id': token_id,
                'probability': prob,
                'rank': i + 1
            })
        except Exception as e:
            logger.warning(f"Failed to decode token {token_id}: {e}")
            continue
    
    return tokens

def analyze_prediction_confidence(results: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze prediction confidence patterns"""
    if not results:
        return {}
    
    vision_results = [r for r in results if r['token_type'] == 'vision']
    text_results = [r for r in results if r['token_type'] == 'text']
    
    analysis = {}
    
    # Vision token analysis
    if vision_results:
        vision_confidences = [
            r['top_tokens'][0]['probability'] 
            for r in vision_results 
            if r['top_tokens']
        ]
        
        if vision_confidences:
            analysis['vision'] = {
                'mean_confidence': np.mean(vision_confidences),
                'std_confidence': np.std(vision_confidences),
                'max_confidence': np.max(vision_confidences),
                'min_confidence': np.min(vision_confidences),
                'num_samples': len(vision_confidences)
            }
    
    # Text token analysis
    if text_results:
        text_confidences = [
            r['top_tokens'][0]['probability'] 
            for r in text_results 
            if r['top_tokens']
        ]
        
        if text_confidences:
            analysis['text'] = {
                'mean_confidence': np.mean(text_confidences),
                'std_confidence': np.std(text_confidences),
                'max_confidence': np.max(text_confidences),
                'min_confidence': np.min(text_confidences),
                'num_samples': len(text_confidences)
            }
    
    return analysis

def create_layer_summary(results: List[Dict[str, Any]]) -> Dict[int, Dict[str, Any]]:
    """Create summary statistics for each layer"""
    layer_summary = {}
    
    # Group results by layer
    for result in results:
        layer = result['layer']
        if layer not in layer_summary:
            layer_summary[layer] = {
                'vision_results': [],
                'text_results': []
            }
        
        if result['token_type'] == 'vision':
            layer_summary[layer]['vision_results'].append(result)
        else:
            layer_summary[layer]['text_results'].append(result)
    
    # Calculate statistics for each layer
    for layer, data in layer_summary.items():
        # Vision statistics
        if data['vision_results']:
            vision_probs = [
                r['top_tokens'][0]['probability'] 
                for r in data['vision_results'] 
                if r['top_tokens']
            ]
            data['vision_stats'] = {
                'count': len(vision_probs),
                'mean_prob': np.mean(vision_probs) if vision_probs else 0,
                'std_prob': np.std(vision_probs) if vision_probs else 0
            }
        
        # Text statistics
        if data['text_results']:
            text_probs = [
                r['top_tokens'][0]['probability'] 
                for r in data['text_results'] 
                if r['top_tokens']
            ]
            data['text_stats'] = {
                'count': len(text_probs),
                'mean_prob': np.mean(text_probs) if text_probs else 0,
                'std_prob': np.std(text_probs) if text_probs else 0
            }
    
    return layer_summary

def validate_inputs(image, prompt: str) -> Tuple[bool, str]:
    """Validate user inputs"""
    if image is None:
        return False, "Please upload an image"
    
    if not prompt or not prompt.strip():
        return False, "Please provide a text prompt"
    
    if len(prompt.strip()) > 500:
        return False, "Prompt is too long (max 500 characters)"
    
    # Check image properties
    try:
        width, height = image.size
        if width < 32 or height < 32:
            return False, "Image is too small (minimum 32x32 pixels)"
        
        if width > 2048 or height > 2048:
            return False, "Image is too large (maximum 2048x2048 pixels)"
        
    except Exception as e:
        return False, f"Invalid image: {str(e)}"
    
    return True, "Inputs are valid"

def estimate_processing_time(image, num_layers: int, num_patches: int) -> str:
    """Estimate processing time based on inputs"""
    try:
        width, height = image.size
        pixels = width * height
        
        # Base time estimates (in seconds)
        base_time = 30  # Model loading and basic processing
        layer_time = num_layers * 2  # 2 seconds per layer
        patch_time = num_patches * 0.1  # 0.1 seconds per patch
        pixel_factor = pixels / (224 * 224)  # Relative to standard input size
        
        total_time = base_time + layer_time + patch_time * pixel_factor
        
        if total_time < 60:
            return f"~{int(total_time)} seconds"
        else:
            minutes = int(total_time // 60)
            seconds = int(total_time % 60)
            return f"~{minutes}m {seconds}s"
            
    except Exception:
        return "~1-3 minutes"

def create_progress_tracker():
    """Create a progress tracking utility"""
    class ProgressTracker:
        def __init__(self):
            self.current_step = 0
            self.total_steps = 0
            self.step_descriptions = []
        
        def set_steps(self, descriptions: List[str]):
            self.step_descriptions = descriptions
            self.total_steps = len(descriptions)
            self.current_step = 0
        
        def next_step(self) -> Tuple[float, str]:
            if self.current_step < self.total_steps:
                progress = self.current_step / self.total_steps
                description = self.step_descriptions[self.current_step]
                self.current_step += 1
                return progress, description
            else:
                return 1.0, "Complete"
        
        def get_progress(self) -> float:
            return self.current_step / self.total_steps if self.total_steps > 0 else 0
    
    return ProgressTracker()

def safe_tensor_to_numpy(tensor: torch.Tensor) -> np.ndarray:
    """Safely convert tensor to numpy array"""
    try:
        if tensor.is_cuda:
            tensor = tensor.cpu()
        return tensor.detach().numpy()
    except Exception as e:
        logger.warning(f"Failed to convert tensor to numpy: {e}")
        return np.array([])

def cleanup_gpu_memory():
    """Clean up GPU memory"""
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        torch.cuda.synchronize()

def get_device_info() -> Dict[str, Any]:
    """Get device information"""
    info = {
        'cuda_available': torch.cuda.is_available(),
        'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
        'current_device': torch.cuda.current_device() if torch.cuda.is_available() else None,
    }
    
    if torch.cuda.is_available():
        info['device_name'] = torch.cuda.get_device_name()
        info['memory_allocated'] = torch.cuda.memory_allocated() / 1024**2  # MB
        info['memory_reserved'] = torch.cuda.memory_reserved() / 1024**2  # MB
    
    return info
