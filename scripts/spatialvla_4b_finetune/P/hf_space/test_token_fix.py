#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify Hugging Face token integration
"""

import os
import sys
from transformers import AutoProcessor

def test_token_access():
    """Test if we can access the gated model with token"""

    print("🔍 Testing Hugging Face Token Setup")
    print("=" * 50)

    # Get token from environment variable
    hf_token = os.environ.get("HF_TOKEN")

    if not hf_token:
        print("❌ HF_TOKEN environment variable is not set!")
        print("\n🔧 To fix this:")
        print("1. Get a token from: https://huggingface.co/settings/tokens")
        print("2. Set it locally: export HF_TOKEN=your_token_here")
        print("3. Or set it in Space secrets (for Hugging Face Spaces)")
        return False

    print(f"✅ HF_TOKEN found: {hf_token[:10]}...")
    print(f"Testing access to google/paligemma2-3b-pt-224...")

    try:
        # Try to load the processor (this should trigger the authentication)
        processor = AutoProcessor.from_pretrained(
            "google/paligemma2-3b-pt-224",
            token=hf_token
        )
        print("✅ Success! Token authentication works.")
        print(f"Processor loaded: {type(processor)}")
        print("\n🎉 Your setup is ready for the PaliGemma2 model!")
        return True

    except Exception as e:
        error_str = str(e)
        print(f"❌ Failed: {error_str}")

        # Provide specific guidance
        if "401" in error_str or "unauthorized" in error_str.lower():
            print("\n🔧 This error usually means:")
            print("- Your token is invalid or expired")
            print("- You haven't been granted access to the model yet")
            print("\nPlease:")
            print("1. Check your token at: https://huggingface.co/settings/tokens")
            print("2. Request access at: https://huggingface.co/google/paligemma2-3b-pt-224")
            print("3. Wait for Google team approval")

        elif "gated" in error_str.lower():
            print("\n🔧 This model is gated. You need to:")
            print("1. Visit: https://huggingface.co/google/paligemma2-3b-pt-224")
            print("2. Click 'Request access'")
            print("3. Wait for approval from Google team")

        return False

def main():
    """Main function with environment detection"""

    # Detect environment
    is_space = os.environ.get("SPACE_ID") is not None
    is_colab = 'google.colab' in sys.modules

    print(f"Environment: {'Hugging Face Space' if is_space else 'Colab' if is_colab else 'Local'}")

    success = test_token_access()

    if success:
        print("\n✅ All checks passed! You can now run the main app.")
    else:
        print("\n❌ Setup incomplete. Please fix the issues above.")

    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
