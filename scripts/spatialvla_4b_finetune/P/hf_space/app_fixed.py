#!/usr/bin/env python3
"""
PaliGemma2 Logit Lens Analysis - Space Compatible Version
========================================================
"""

import gradio as gr
import torch
import os
import logging
from PIL import Image

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global analyzer
analyzer = None

def load_analyzer():
    """Load the analyzer"""
    global analyzer
    
    if analyzer is not None:
        return "✅ Already loaded!"
    
    try:
        from logit_lens_analyzer import PaliGemma2LogitLensAnalyzer, LogitLensConfig
        
        config = LogitLensConfig(
            model_path="google/paligemma2-3b-pt-224",
            device="cuda" if torch.cuda.is_available() else "cpu",
            dtype="fp16" if torch.cuda.is_available() else "fp32",
            target_layers=list(range(20, 26)),
            max_patches_analyze=16,  # Reduced for stability
            max_text_tokens=4
        )
        
        analyzer = PaliGemma2LogitLensAnalyzer(config)
        analyzer.load_model()
        
        return f"✅ Model loaded on {config.device}!"
        
    except Exception as e:
        logger.error(f"Load failed: {e}")
        return f"❌ Load failed: {str(e)}"

def run_analysis(image, prompt):
    """Run the analysis"""
    
    if analyzer is None:
        return "❌ Please load model first!", "", ""
    
    if image is None:
        return "❌ Please upload an image!", "", ""
    
    if not prompt or not prompt.strip():
        return "❌ Please provide a prompt!", "", ""
    
    try:
        results = analyzer.analyze_image(image, prompt.strip())
        
        if not results.get('success', False):
            error_msg = results.get('error', 'Unknown error')
            return f"❌ Analysis failed: {error_msg}", "", ""
        
        # Format results
        summary = create_summary(results)
        details = create_details(results)
        viz = create_visualization(results)
        
        return summary, details, viz
        
    except Exception as e:
        logger.error(f"Analysis failed: {e}")
        return f"❌ Error: {str(e)}", "", ""

def create_summary(results):
    """Create summary"""
    stats = results.get('stats', {})
    
    return f"""# 📊 Analysis Complete

**Input Info:**
- Sequence Length: {stats.get('sequence_length', 'N/A')}
- Vision Tokens: {stats.get('num_vision_tokens', 'N/A')}
- Text Tokens: {stats.get('num_text_tokens', 'N/A')}
- Layers Analyzed: {stats.get('layers_analyzed', 'N/A')}

**Results:**
- Total Analyses: {len(results.get('results', []))}
- Vision Results: {len([r for r in results.get('results', []) if r.get('token_type') == 'vision'])}
- Text Results: {len([r for r in results.get('results', []) if r.get('token_type') == 'text'])}
"""

def create_details(results):
    """Create detailed results"""
    analysis_results = results.get('results', [])
    
    if not analysis_results:
        return "No results available."
    
    details = "# 🔬 Top Predictions\n\n"
    details += "| Layer | Type | Token | Probability |\n"
    details += "|-------|------|-------|-------------|\n"
    
    for i, result in enumerate(analysis_results[:15]):  # Top 15
        if not result.get('top_tokens'):
            continue
            
        layer = result.get('layer', 'N/A')
        token_type = result.get('token_type', 'N/A')
        top_token = result['top_tokens'][0]
        
        token_text = str(top_token.get('token', '')).replace('|', '\\|')[:10]
        prob = top_token.get('probability', 0)
        
        details += f"| {layer} | {token_type} | `{token_text}` | {prob:.3f} |\n"
    
    return details

def create_visualization(results):
    """Create visualization HTML"""
    viz_data = results.get('visualizations', {})
    
    html = "<div style='text-align: center;'>"
    html += "<h2>📊 Analysis Visualization</h2>"
    
    if 'layer_evolution' in viz_data:
        html += f"""
        <div style='margin: 20px 0;'>
            <h3>Layer Evolution</h3>
            <img src='data:image/png;base64,{viz_data['layer_evolution']}' 
                 style='max-width: 100%; height: auto; border: 1px solid #ddd;'>
        </div>
        """
    else:
        html += "<p>Visualization data not available.</p>"
    
    html += "</div>"
    return html

# Create the interface
def create_interface():
    with gr.Blocks(title="PaliGemma2 Logit Lens") as interface:
        
        gr.HTML("""
        <div style='text-align: center; margin: 20px 0;'>
            <h1>🔍 PaliGemma2 Logit Lens Analysis</h1>
            <p>Analyze how PaliGemma2 processes images and text</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                # Step 1: Load Model
                gr.HTML("<h3>Step 1: Load Model</h3>")
                load_btn = gr.Button("Load Model", variant="primary", size="lg")
                status_box = gr.Textbox(
                    label="Status", 
                    value="Click 'Load Model' to start",
                    interactive=False,
                    lines=2
                )
                
                # Step 2: Input
                gr.HTML("<h3>Step 2: Provide Input</h3>")
                image_input = gr.Image(
                    label="Upload Image",
                    type="pil",
                    height=250
                )
                
                text_input = gr.Textbox(
                    label="Prompt",
                    value="Describe this image",
                    lines=2
                )
                
                analyze_btn = gr.Button("Analyze", variant="secondary", size="lg")
        
        # Results section
        gr.HTML("<h3>📊 Analysis Results</h3>")
        
        with gr.Tabs():
            with gr.TabItem("Summary"):
                summary_out = gr.Markdown(label="Summary")
            
            with gr.TabItem("Details"):
                details_out = gr.Markdown(label="Detailed Results")
            
            with gr.TabItem("Visualization"):
                viz_out = gr.HTML(label="Visualization")
        
        # Event handlers
        load_btn.click(
            fn=load_analyzer,
            outputs=[status_box]
        )
        
        analyze_btn.click(
            fn=run_analysis,
            inputs=[image_input, text_input],
            outputs=[summary_out, details_out, viz_out]
        )
        
        # Instructions
        gr.HTML("""
        <div style='background: #f0f8ff; padding: 15px; margin: 20px 0; border-radius: 8px;'>
            <h4>💡 Instructions:</h4>
            <ol>
                <li>Click "Load Model" and wait 2-3 minutes for initialization</li>
                <li>Upload a clear, interesting image</li>
                <li>Provide a descriptive prompt</li>
                <li>Click "Analyze" and wait for results</li>
            </ol>
            <p><strong>Note:</strong> First analysis may take longer as the model loads.</p>
        </div>
        """)
    
    return interface

# Main execution
if __name__ == "__main__":
    demo = create_interface()
    
    # Launch with proper Space configuration
    demo.launch(
        share=True,
        server_name="0.0.0.0", 
        server_port=7860,
        show_error=True
    )
