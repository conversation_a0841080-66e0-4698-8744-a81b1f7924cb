#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署前检查脚本 - 验证所有设置是否正确
"""

import os
import sys
import re
from pathlib import Path

def check_token_security():
    """检查代码中是否有硬编码的token"""
    print("🔍 检查代码安全性...")
    
    # 检查的文件列表
    files_to_check = [
        "app.py",
        "logit_lens_analyzer.py", 
        "test_token_fix.py",
        "utils.py"
    ]
    
    # token模式（Hugging Face token格式）
    token_pattern = r'hf_[a-zA-Z0-9]{34}'
    
    issues_found = []
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            continue
            
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找硬编码的token
        matches = re.findall(token_pattern, content)
        if matches:
            issues_found.append(f"❌ {file_path}: 发现硬编码token")
    
    if issues_found:
        print("🚨 安全问题发现:")
        for issue in issues_found:
            print(f"  {issue}")
        print("\n请移除所有硬编码的token后再部署！")
        return False
    else:
        print("✅ 代码安全检查通过 - 未发现硬编码token")
        return True

def check_environment_setup():
    """检查环境变量设置"""
    print("\n🔧 检查环境变量...")
    
    hf_token = os.environ.get("HF_TOKEN")
    
    if not hf_token:
        print("⚠️  HF_TOKEN 环境变量未设置")
        print("   本地测试时请设置: export HF_TOKEN=your_token_here")
        print("   Space部署时请在Settings中添加secret")
        return False
    
    if not hf_token.startswith("hf_"):
        print("❌ HF_TOKEN 格式不正确（应以 'hf_' 开头）")
        return False
    
    if len(hf_token) != 37:  # hf_ + 34 characters
        print("❌ HF_TOKEN 长度不正确")
        return False
    
    print(f"✅ HF_TOKEN 已设置且格式正确: {hf_token[:10]}...")
    return True

def check_required_files():
    """检查必需文件是否存在"""
    print("\n📁 检查必需文件...")
    
    required_files = [
        "app.py",
        "logit_lens_analyzer.py",
        "requirements.txt",
        "README.md"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ 缺少必需文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    print("✅ 所有必需文件都存在")
    return True

def check_requirements():
    """检查requirements.txt内容"""
    print("\n📦 检查依赖配置...")
    
    if not os.path.exists("requirements.txt"):
        print("❌ requirements.txt 不存在")
        return False
    
    with open("requirements.txt", 'r') as f:
        requirements = f.read()
    
    required_packages = [
        "torch",
        "transformers", 
        "gradio",
        "pillow",
        "numpy",
        "matplotlib"
    ]
    
    missing_packages = []
    for package in required_packages:
        if package.lower() not in requirements.lower():
            missing_packages.append(package)
    
    if missing_packages:
        print("⚠️  可能缺少的依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
    else:
        print("✅ 主要依赖包都已包含")
    
    return True

def main():
    """主检查函数"""
    print("🚀 Hugging Face Space 部署前检查")
    print("=" * 50)
    
    checks = [
        ("代码安全性", check_token_security),
        ("必需文件", check_required_files), 
        ("依赖配置", check_requirements),
        ("环境变量", check_environment_setup)
    ]
    
    all_passed = True
    
    for check_name, check_func in checks:
        try:
            result = check_func()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name} 检查失败: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    
    if all_passed:
        print("🎉 所有检查通过！可以安全部署到 Hugging Face Space")
        print("\n📋 部署步骤:")
        print("1. 在 HF Space 中设置 HF_TOKEN secret")
        print("2. 上传所有文件")
        print("3. 选择 T4 small 或更好的硬件")
        print("4. 等待部署完成")
    else:
        print("⚠️  发现问题，请修复后再部署")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
