#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Logit Lens Analysis - Simplified Space Version
========================================================

Simplified version for better Hugging Face Space compatibility.
"""

import gradio as gr
import torch
import numpy as np
from PIL import Image
import logging
import traceback
import os

# Import our analyzer
from logit_lens_analyzer import PaliGemma2LogitLensAnalyzer, LogitLensConfig

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global analyzer instance
analyzer = None

def initialize_analyzer():
    """Initialize the analyzer"""
    global analyzer
    
    if analyzer is not None:
        return "✅ Analyzer already initialized!"
    
    try:
        config = LogitLensConfig(
            model_path="google/paligemma2-3b-pt-224",
            device="cuda" if torch.cuda.is_available() else "cpu",
            dtype="fp16" if torch.cuda.is_available() else "fp32",
            target_layers=list(range(20, 26)),  # Focus on last 6 layers
            max_patches_analyze=32,
            max_text_tokens=6
        )
        
        analyzer = PaliGemma2LogitLensAnalyzer(config)
        analyzer.load_model()
        
        device_info = f"Device: {config.device}"
        if torch.cuda.is_available():
            device_info += f" ({torch.cuda.get_device_name()})"
        
        return f"✅ Model loaded successfully!\n{device_info}\nLayers: {config.target_layers}"
        
    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        return f"❌ Initialization failed: {str(e)}"

def analyze_image(image, prompt):
    """Main analysis function"""
    
    if analyzer is None:
        return "❌ Please initialize the model first!", "", ""
    
    if image is None:
        return "❌ Please upload an image!", "", ""
    
    if not prompt.strip():
        return "❌ Please provide a prompt!", "", ""
    
    try:
        # Run analysis
        results = analyzer.analyze_image(image, prompt)
        
        if not results['success']:
            return f"❌ Analysis failed: {results.get('error', 'Unknown error')}", "", ""
        
        # Format results
        summary = format_summary(results)
        detailed = format_detailed(results)
        viz_html = format_visualization(results)
        
        return summary, detailed, viz_html
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        return f"❌ Analysis error: {str(e)}", "", ""

def format_summary(results):
    """Format analysis summary"""
    stats = results['stats']
    
    summary = f"""# 📊 Analysis Summary

## Input Information
- **Sequence Length**: {stats['sequence_length']} tokens
- **Vision Tokens**: {stats['num_vision_tokens']}
- **Text Tokens**: {stats['num_text_tokens']}
- **Layers Analyzed**: {stats['layers_analyzed']}

## Results Overview
- **Total Analyses**: {len(results['results'])}
- **Vision Analyses**: {len([r for r in results['results'] if r['token_type'] == 'vision'])}
- **Text Analyses**: {len([r for r in results['results'] if r['token_type'] == 'text'])}
"""
    
    return summary

def format_detailed(results):
    """Format detailed results"""
    if not results['results']:
        return "No detailed results available."
    
    table_md = """# 🔬 Detailed Results

| Layer | Type | Position | Top Token | Probability |
|-------|------|----------|-----------|-------------|
"""
    
    for result in results['results'][:20]:  # Top 20 results
        if not result['top_tokens']:
            continue
            
        layer = result['layer']
        token_type = result['token_type']
        position = result['position']
        top_token = result['top_tokens'][0]
        
        token_text = top_token['token'].replace('|', '\\|')[:15]
        prob = top_token['probability']
        
        table_md += f"| {layer} | {token_type} | {position} | `{token_text}` | {prob:.3f} |\n"
    
    return table_md

def format_visualization(results):
    """Format visualization"""
    viz_data = results.get('visualizations', {})
    
    if 'error' in viz_data:
        return f"<p>❌ Visualization error: {viz_data['error']}</p>"
    
    html = "<div><h2>📊 Visualization Results</h2>"
    
    if 'layer_evolution' in viz_data:
        html += f"""
        <div style="text-align: center; margin: 20px 0;">
            <h3>Layer Evolution Analysis</h3>
            <img src="data:image/png;base64,{viz_data['layer_evolution']}" 
                 style="max-width: 100%; height: auto;">
        </div>
        """
    
    html += "</div>"
    return html

# Create interface
with gr.Blocks(title="🔍 PaliGemma2 Logit Lens Analysis") as demo:
    
    gr.HTML("""
    <div style="text-align: center; margin-bottom: 30px;">
        <h1>🔍 PaliGemma2 Logit Lens Analysis</h1>
        <p>Explore how PaliGemma2 processes images and text</p>
    </div>
    """)
    
    with gr.Row():
        with gr.Column(scale=1):
            gr.HTML("<h3>🚀 Step 1: Initialize</h3>")
            init_btn = gr.Button("Initialize Model", variant="primary")
            init_status = gr.Textbox(
                label="Status",
                value="Click 'Initialize Model' to start",
                interactive=False,
                lines=3
            )
            
            gr.HTML("<h3>📤 Step 2: Input</h3>")
            image_input = gr.Image(
                label="Upload Image",
                type="pil"
            )
            
            prompt_input = gr.Textbox(
                label="Prompt",
                value="Describe this image",
                lines=2
            )
            
            analyze_btn = gr.Button("🔍 Analyze", variant="secondary")
    
    with gr.Row():
        with gr.Column():
            gr.HTML("<h3>📊 Results</h3>")
            
            with gr.Tabs():
                with gr.TabItem("📋 Summary"):
                    summary_output = gr.Markdown()
                
                with gr.TabItem("🔬 Details"):
                    detailed_output = gr.Markdown()
                
                with gr.TabItem("📈 Visualization"):
                    viz_output = gr.HTML()
    
    # Event handlers
    init_btn.click(
        fn=initialize_analyzer,
        outputs=[init_status]
    )
    
    analyze_btn.click(
        fn=analyze_image,
        inputs=[image_input, prompt_input],
        outputs=[summary_output, detailed_output, viz_output]
    )

if __name__ == "__main__":
    # Check if running in Space
    is_space = os.environ.get("SPACE_ID") is not None
    
    if is_space:
        demo.launch(share=True)
    else:
        demo.launch(server_name="0.0.0.0", server_port=None)
