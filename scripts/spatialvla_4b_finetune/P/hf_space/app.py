#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PaliGemma2 Logit Lens Analysis - Gradio App
==========================================

Interactive web interface for PaliGemma2 model interpretability analysis.
"""

import gradio as gr
import torch
import numpy as np
from PIL import Image
import logging
import traceback
import os
import tempfile
from typing import Optional, Tuple, Dict, Any

# Import our analyzer
from logit_lens_analyzer import PaliGemma2LogitLensAnalyzer, LogitLensConfig

# 设置临时目录权限 - 适配Hugging Face Space
if not os.environ.get("SPACE_ID"):  # 本地运行时
    temp_dir = os.path.join(os.getcwd(), "temp")
    os.makedirs(temp_dir, exist_ok=True)
    os.chmod(temp_dir, 0o755)

    # 设置Gradio相关的环境变量
    os.environ["GRADIO_TEMP_DIR"] = temp_dir
    os.environ["TMPDIR"] = temp_dir
    os.environ["TMP"] = temp_dir
    os.environ["TEMP"] = temp_dir

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global analyzer instance
analyzer = None

def initialize_analyzer(progress=gr.Progress()):
    """Initialize the analyzer with progress tracking"""
    global analyzer
    
    if analyzer is not None:
        return "✅ Analyzer already initialized!"
    
    try:
        progress(0.1, desc="Setting up configuration...")
        
        config = LogitLensConfig(
            model_path="google/paligemma2-3b-pt-224",
            device="cuda" if torch.cuda.is_available() else "cpu",
            dtype="fp16" if torch.cuda.is_available() else "fp32",
            target_layers=list(range(20, 26)),  # Focus on last 6 layers for speed
            max_patches_analyze=32,  # Reduced for web performance
            max_text_tokens=6
        )
        
        progress(0.3, desc="Creating analyzer...")
        analyzer = PaliGemma2LogitLensAnalyzer(config)
        
        progress(0.5, desc="Loading model (this may take a few minutes)...")
        
        def progress_callback(msg):
            progress(0.5 + 0.4 * (hash(msg) % 100) / 100, desc=msg)
        
        analyzer.load_model(progress_callback)
        
        progress(1.0, desc="✅ Ready for analysis!")
        
        device_info = f"Device: {config.device}"
        if torch.cuda.is_available():
            device_info += f" ({torch.cuda.get_device_name()})"
        
        return f"✅ Model loaded successfully!\n{device_info}\nLayers to analyze: {config.target_layers}"
        
    except Exception as e:
        logger.error(f"Initialization failed: {e}")
        return f"❌ Initialization failed: {str(e)}"

def analyze_image(image, prompt, progress=gr.Progress()):
    """Main analysis function"""
    
    if analyzer is None:
        return "❌ Please initialize the model first!", "", ""
    
    if image is None:
        return "❌ Please upload an image!", "", ""
    
    if not prompt.strip():
        return "❌ Please provide a prompt!", "", ""
    
    try:
        progress(0.1, desc="Starting analysis...")
        
        def progress_callback(msg):
            # Map progress messages to progress values
            progress_map = {
                "Preprocessing input...": 0.2,
                "Running model inference...": 0.4,
                "Analyzing token positions...": 0.6,
                "Running logit lens analysis...": 0.8,
                "Generating visualizations...": 0.9
            }
            prog_val = progress_map.get(msg, 0.5)
            progress(prog_val, desc=msg)
        
        # Run analysis
        results = analyzer.analyze_image(image, prompt, progress_callback)
        
        progress(1.0, desc="✅ Analysis complete!")
        
        if not results['success']:
            return f"❌ Analysis failed: {results.get('error', 'Unknown error')}", "", ""
        
        # Format results
        summary = format_analysis_summary(results)
        detailed_results = format_detailed_results(results)
        visualization_html = create_visualization_html(results)
        
        return summary, detailed_results, visualization_html
        
    except Exception as e:
        logger.error(f"Analysis error: {e}")
        traceback.print_exc()
        return f"❌ Analysis error: {str(e)}", "", ""

def format_analysis_summary(results: Dict[str, Any]) -> str:
    """Format analysis summary"""
    stats = results['stats']
    
    summary = f"""
# 📊 Analysis Summary

## 🔍 Input Information
- **Sequence Length**: {stats['sequence_length']} tokens
- **Vision Tokens**: {stats['num_vision_tokens']} (16x16 patches)
- **Text Tokens**: {stats['num_text_tokens']}
- **Layers Analyzed**: {stats['layers_analyzed']}

## 📈 Results Overview
- **Total Analyses**: {len(results['results'])} position-layer combinations
- **Vision Analyses**: {len([r for r in results['results'] if r['token_type'] == 'vision'])}
- **Text Analyses**: {len([r for r in results['results'] if r['token_type'] == 'text'])}

## 🎯 Key Findings
"""
    
    # Add some key insights
    vision_results = [r for r in results['results'] if r['token_type'] == 'vision']
    if vision_results:
        # Find most confident predictions
        max_conf = max(r['top_tokens'][0]['probability'] for r in vision_results if r['top_tokens'])
        min_conf = min(r['top_tokens'][0]['probability'] for r in vision_results if r['top_tokens'])
        avg_conf = np.mean([r['top_tokens'][0]['probability'] for r in vision_results if r['top_tokens']])
        
        summary += f"""
- **Vision Token Confidence Range**: {min_conf:.3f} - {max_conf:.3f}
- **Average Confidence**: {avg_conf:.3f}
"""
    
    return summary

def format_detailed_results(results: Dict[str, Any]) -> str:
    """Format detailed results as markdown table"""
    
    if not results['results']:
        return "No detailed results available."
    
    # Create table for top results
    table_md = """
# 🔬 Detailed Analysis Results

## Top Vision Token Predictions

| Layer | Patch | Position | Top Token | Probability | Token 2 | Prob 2 |
|-------|-------|----------|-----------|-------------|---------|--------|
"""
    
    vision_results = [r for r in results['results'] if r['token_type'] == 'vision']
    # Sort by probability and take top 20
    vision_results.sort(key=lambda x: x['top_tokens'][0]['probability'] if x['top_tokens'] else 0, reverse=True)
    
    for result in vision_results[:20]:
        if not result['top_tokens']:
            continue
            
        layer = result['layer']
        patch_id = result.get('patch_id', 'N/A')
        position = result['position']
        
        top1 = result['top_tokens'][0]
        top2 = result['top_tokens'][1] if len(result['top_tokens']) > 1 else {'token': '-', 'probability': 0}
        
        # Clean token text for display
        token1_clean = top1['token'].replace('|', '\\|').replace('\n', '\\n')[:15]
        token2_clean = top2['token'].replace('|', '\\|').replace('\n', '\\n')[:15]
        
        table_md += f"| {layer} | {patch_id} | {position} | `{token1_clean}` | {top1['probability']:.3f} | `{token2_clean}` | {top2['probability']:.3f} |\n"
    
    # Add text results if available
    text_results = [r for r in results['results'] if r['token_type'] == 'text']
    if text_results:
        table_md += """

## Text Token Predictions

| Layer | Position | Top Token | Probability | Token 2 | Prob 2 |
|-------|----------|-----------|-------------|---------|--------|
"""
        
        for result in text_results[:10]:
            if not result['top_tokens']:
                continue
                
            layer = result['layer']
            position = result['position']
            
            top1 = result['top_tokens'][0]
            top2 = result['top_tokens'][1] if len(result['top_tokens']) > 1 else {'token': '-', 'probability': 0}
            
            token1_clean = top1['token'].replace('|', '\\|').replace('\n', '\\n')[:15]
            token2_clean = top2['token'].replace('|', '\\|').replace('\n', '\\n')[:15]
            
            table_md += f"| {layer} | {position} | `{token1_clean}` | {top1['probability']:.3f} | `{token2_clean}` | {top2['probability']:.3f} |\n"
    
    return table_md

def create_visualization_html(results: Dict[str, Any]) -> str:
    """Create HTML visualization"""
    
    viz_data = results.get('visualizations', {})
    
    if 'error' in viz_data:
        return f"<p>❌ Visualization error: {viz_data['error']}</p>"
    
    html = """
    <div style="font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto;">
        <h2>📊 Visualization Results</h2>
    """
    
    if 'layer_evolution' in viz_data:
        html += f"""
        <div style="margin: 20px 0; text-align: center;">
            <h3>Layer Evolution Analysis</h3>
            <img src="data:image/png;base64,{viz_data['layer_evolution']}" 
                 style="max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 8px;">
            <p style="color: #666; font-size: 14px; margin-top: 10px;">
                This plot shows how token prediction confidence evolves across different transformer layers.
            </p>
        </div>
        """
    
    html += """
        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 20px;">
            <h4>💡 How to Interpret</h4>
            <ul style="color: #555;">
                <li><strong>Higher confidence</strong> indicates the model is more certain about its predictions</li>
                <li><strong>Vision tokens</strong> represent 16x16 image patches</li>
                <li><strong>Layer progression</strong> shows how understanding develops through the network</li>
                <li><strong>Confidence changes</strong> reveal where the model "makes decisions"</li>
            </ul>
        </div>
    </div>
    """
    
    return html

# Create Gradio interface
def create_interface():
    """Create the Gradio interface"""
    
    with gr.Blocks(
        title="🔍 PaliGemma2 Logit Lens Analysis",
        theme=gr.themes.Soft(),
        css="""
        .gradio-container {
            max-width: 1200px !important;
        }
        .main-header {
            text-align: center;
            margin-bottom: 30px;
        }
        """
    ) as demo:
        
        gr.HTML("""
        <div class="main-header">
            <h1>🔍 PaliGemma2 Logit Lens Analysis</h1>
            <p>Explore how PaliGemma2 processes images and text through interactive model interpretability</p>
        </div>
        """)
        
        with gr.Row():
            with gr.Column(scale=1):
                gr.HTML("<h3>🚀 Step 1: Initialize Model</h3>")
                init_btn = gr.Button("Initialize Model", variant="primary", size="lg")
                init_status = gr.Textbox(
                    label="Initialization Status",
                    value="Click 'Initialize Model' to start",
                    interactive=False,
                    lines=3
                )
                
                gr.HTML("<h3>📤 Step 2: Upload & Configure</h3>")
                image_input = gr.Image(
                    label="Upload Image",
                    type="pil",
                    sources=["upload"],
                    height=300
                )
                
                prompt_input = gr.Textbox(
                    label="Prompt",
                    placeholder="Describe this image",
                    value="Describe this image",
                    lines=2
                )
                
                analyze_btn = gr.Button("🔍 Run Analysis", variant="secondary", size="lg")
        
        with gr.Row():
            with gr.Column():
                gr.HTML("<h3>📊 Analysis Results</h3>")
                
                with gr.Tabs():
                    with gr.TabItem("📋 Summary"):
                        summary_output = gr.Markdown(label="Analysis Summary")
                    
                    with gr.TabItem("🔬 Detailed Results"):
                        detailed_output = gr.Markdown(label="Detailed Results")
                    
                    with gr.TabItem("📈 Visualizations"):
                        viz_output = gr.HTML(label="Visualizations")
        
        # Event handlers
        init_btn.click(
            fn=initialize_analyzer,
            outputs=[init_status]
        )
        
        analyze_btn.click(
            fn=analyze_image,
            inputs=[image_input, prompt_input],
            outputs=[summary_output, detailed_output, viz_output]
        )
        
        # Add examples
        gr.HTML("<h3>💡 Tips</h3>")
        gr.HTML("""
        <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; margin: 10px 0;">
            <ul>
                <li><strong>First time?</strong> Click "Initialize Model" and wait for it to load (2-3 minutes)</li>
                <li><strong>Best results:</strong> Use clear, detailed images with interesting content</li>
                <li><strong>Prompts:</strong> Try "Describe this image", "What do you see?", or specific questions</li>
                <li><strong>Analysis time:</strong> Each analysis takes 1-2 minutes depending on image complexity</li>
            </ul>
        </div>
        """)
    
    return demo

if __name__ == "__main__":
    # 检测运行环境
    is_space = os.environ.get("SPACE_ID") is not None

    if not is_space:  # 本地运行时才尝试修改权限
        try:
            import stat
            os.chmod("/tmp", stat.S_IRWXU | stat.S_IRWXG | stat.S_IRWXO | stat.S_ISVTX)
        except:
            pass  # 如果没有权限修改/tmp，忽略错误

    # Create and launch the interface
    demo = create_interface()

    # 根据环境调整启动参数
    if is_space:
        # Hugging Face Space环境
        demo.launch(
            share=True,  # Space环境需要share=True
            server_name="0.0.0.0",
            server_port=7860,
            show_error=True
        )
    else:
        # 本地开发环境
        demo.launch(
            server_name="0.0.0.0",
            server_port=None,
            share=False
        )
