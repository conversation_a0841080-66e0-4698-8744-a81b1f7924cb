#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Attention Knockout Diagnostic Script - 论文方法实现版本
============================================================

基于论文 "Towards Interpreting Visual Information Processing in Vision-Language Models" 
(https://arxiv.org/pdf/2410.07149) 的attention knockout方法重新实现。

主要改进：
1. 使用SimplerEnv真实图像数据（参考spatialvla_visual_interpretation_analysis.py）
2. 严格按照论文中的attention knockout方法实现
3. 针对特定区域（Gripper、Object、Background）进行attention权重阻断

论文中的Attention Knockout方法：
- 通过修改attention mask在特定层阻断特定token间的attention流
- 测试不同层窗口（early, early-mid, mid, mid-late, late layers）
- 分析从visual tokens到last token position的信息流

作者: Assistant
日期: 2025-01-14
"""

import os
import sys
import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging
from datetime import datetime
from dataclasses import dataclass, field
import copy
import json
from pathlib import Path
from transformers import HfArgumentParser, set_seed
from PIL import Image
import cv2
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根路径
project_root = "/home/<USER>/claude"
if project_root not in sys.path:
    sys.path.append(project_root)
    logger.info(f"项目根路径已添加到sys.path: {project_root}")

# 添加SpatialVLA路径
spatialvla_path = "/home/<USER>/claude/SpatialVLA"
if spatialvla_path not in sys.path:
    sys.path.append(spatialvla_path)
    logger.info(f"SpatialVLA路径已添加到sys.path: {spatialvla_path}")

# 导入SimplerEnv
try:
    import simpler_env
    from simpler_env.utils.env.observation_utils import get_image_from_maniskill2_obs_dict
    from simpler_env.policies.spatialvla.spatialvla_model import SpatialVLAInference
    logger.info("成功导入SimplerEnv组件。")
except ImportError as e:
    logger.warning(f"导入SimplerEnv失败: {e}")

# 导入SpatialVLA组件
try:
    from transformers import HfArgumentParser, set_seed
    from model import SpatialVLAConfig, SpatialVLAForConditionalGeneration, SpatialVLAProcessor, SpatialActionTokenizer
    logger.info("成功导入SpatialVLA模型组件。")
except ImportError as e:
    logger.error(f"导入SpatialVLA组件失败: {e}")
    logger.info("请确保SpatialVLA已正确安装")
    sys.exit(1)

# 设置SAPIEN
try:
    import sapien.core as sapien
    sapien.render_config.rt_use_denoiser = False
    logger.info("SAPIEN配置成功。")
except ImportError:
    logger.warning("无法导入SAPIEN，某些功能可能受限")

import warnings
warnings.filterwarnings("ignore")

@dataclass 
class AttentionKnockoutConfig:
    """Attention Knockout诊断配置（严格按照论文方法）"""
    
    # 模型配置
    model_path: str = "IPEC-COMMUNITY/spatialvla-4b-224-pt"
    device: str = "cuda:0"
    dtype: str = "bf16"
    
    # SimplerEnv配置（使用真实环境数据）
    task_name: str = "google_robot_pick_coke_can"
    seed: int = 1234
    policy_setup: str = "google_robot"
    action_scale: float = 1.0
    action_ensemble_temp: float = -0.8
    
    # Attention Knockout配置（基于论文）
    layer_windows: List[Tuple[int, int]] = field(default_factory=lambda: [
        (1, 5),     # early layers  
        (6, 10),    # early-mid layers  
        (11, 15),   # mid layers
        (16, 20),   # mid-late layers
        (21, 26)    # late layers (SpatialVLA有26层language layers)
    ])
    
    # 测试区域（参考spatialvla_visual_interpretation_analysis.py但做区分）
    test_regions: List[str] = field(default_factory=lambda: [
        "target_object",    # 改为target_object以匹配detect_key_objects的输出
        "gripper",          # 机械爪区域
        "background"        # 背景区域
    ])
    
    # 测试模式（基于论文但适配区域）
    knockout_patterns: List[str] = field(default_factory=lambda: [
        "region_to_last",                   # 区域到最后token
        "region_with_buffer_to_last",       # 区域+缓冲到最后token  
        "all_except_region_to_last",        # 除区域外所有到最后token
        # 移除所有无效的*_to_last_visual_row模式
    ])
    
    # 输出配置
    output_dir: str = "./attention_knockout_diagnostic_output"
    save_images: bool = True  # 保存真实图像
    save_intermediate: bool = True
    
    def __post_init__(self):
        os.makedirs(self.output_dir, exist_ok=True)
        if self.save_images:
            os.makedirs(os.path.join(self.output_dir, "real_images"), exist_ok=True)


class PaperBasedAttentionKnockout:
    """基于论文方法的Attention Knockout实现"""
    
    def __init__(self, config: AttentionKnockoutConfig):
        self.config = config
        self.device = torch.device(config.device)
        
        # 设置随机种子
        set_seed(config.seed)
        
        # 初始化模型组件
        self.model = None
        self.processor = None
        self.env = None
        self.spatialvla_model = None
        
        # 数据存储
        self.current_instruction = None
        self.current_image = None
        self.current_inputs = None
        self.baseline_outputs = None
        self.key_objects = None  # 存储检测到的关键物体
        
        # 结果存储
        self.knockout_results = {}
        self.saved_images = []
        
        logger.info("基于论文的Attention Knockout诊断器初始化完成")
    
    def setup_model(self) -> bool:
        """设置SpatialVLA模型"""
        logger.info("正在加载SpatialVLA模型...")
        
        try:
            # 加载模型配置
            self.model_config = SpatialVLAConfig.from_pretrained(self.config.model_path)
            
            # 加载模型
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            torch_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)
            
            self.model = SpatialVLAForConditionalGeneration.from_pretrained(
                self.config.model_path,
                torch_dtype=torch_dtype,
                device_map=self.device,
                trust_remote_code=True
            )
            
            # 加载处理器
            self.processor = SpatialVLAProcessor.from_pretrained(
                self.config.model_path,
                trust_remote_code=True
            )
            
            # 设置为评估模式
            self.model.eval()
            
            logger.info(f"模型加载成功: {len(self.model.language_model.model.layers)}层")
            return True
            
        except Exception as e:
            logger.error(f"模型加载失败: {e}")
            return False
    
    def setup_simpler_env(self) -> Tuple[Any, Any]:
        """设置SimplerEnv环境（参考spatialvla_visual_interpretation_analysis.py）"""
        logger.info(f"正在设置SimplerEnv任务: {self.config.task_name}")
        
        try:
            # 创建环境
            self.env = simpler_env.make(self.config.task_name)
            logger.info("SimplerEnv环境创建成功")
            
            # 重置环境
            obs, reset_info = self.env.reset(seed=self.config.seed)
            
            # 设置指令（根据任务）
            instruction_map = {
                "google_robot_pick_coke_can": "Pick up the coke can",
                "google_robot_pick_horizontal_coke_can": "Pick up the horizontal coke can", 
                "google_robot_move_near": "Move near the object",
                "google_robot_pick_vertical_coke_can": "Pick up the vertical coke can"
            }
            
            self.current_instruction = instruction_map.get(
                self.config.task_name, 
                "Pick up the coke can"
            )
            
            logger.info(f"环境设置完成 - 任务: {self.config.task_name}, 指令: {self.current_instruction}")
            return obs, reset_info
            
        except Exception as e:
            logger.error(f"SimplerEnv环境设置失败: {e}")
            return None, None
    
    def create_spatialvla_model(self) -> bool:
        """创建SpatialVLA推理模型（参考spatialvla_visual_interpretation_analysis.py）"""
        try:
            self.spatialvla_model = SpatialVLAInference(
                saved_model_path=self.config.model_path,
                policy_setup=self.config.policy_setup,
                action_scale=self.config.action_scale,
                action_ensemble_temp=self.config.action_ensemble_temp
            )
            logger.info("SpatialVLA推理模型创建成功")
            return True
        except Exception as e:
            logger.error(f"SpatialVLA推理模型创建失败: {e}")
            return False
    
    def get_overhead_camera_data(self, obs) -> Tuple[Optional[np.ndarray], Optional[np.ndarray], bool]:
        """获取overhead_camera的RGB和分割数据（完全参考spatialvla_visual_interpretation_analysis.py）"""
        if not isinstance(obs, dict) or "image" not in obs:
            return None, None, False
        
        if "overhead_camera" not in obs["image"]:
            return None, None, False
        
        cam_data = obs["image"]["overhead_camera"]
        rgb = cam_data.get("rgb")
        segmentation = cam_data.get("Segmentation")
        
        if rgb is None or segmentation is None:
            return None, None, False
        
        # 转换RGB格式
        if rgb.max() <= 1.0:
            rgb = (rgb * 255).astype(np.uint8)
        if len(rgb.shape) == 3 and rgb.shape[2] == 4:
            rgb = rgb[:, :, :3]
        
        logger.info(f"获取overhead camera数据成功: RGB shape={rgb.shape}")
        return rgb, segmentation, True

    def detect_key_objects(self, segmentation: np.ndarray, rgb_image: np.ndarray) -> Dict[str, Any]:
        """检测关键物体：目标物体、机械爪、背景区域（完全参考spatialvla_visual_interpretation_analysis.py）"""
        if len(segmentation.shape) < 3:
            return {"target_object": None, "gripper": None, "background": None}
        
        mesh_ids = segmentation[:, :, 0]
        unique_ids = np.unique(mesh_ids)
        height, width = mesh_ids.shape
        center_y, center_x = height // 2, width // 2
        
        logger.info(f"图像尺寸={height}x{width}, 检测到 {len(unique_ids)} 个唯一ID")
        
        results = {"target_object": None, "gripper": None, "background": None}
        
        # 分析每个ID的详细信息
        id_analysis = {}
        for uid in unique_ids:
            if uid == 0:
                continue
                
            mask = (mesh_ids == uid)
            pixel_count = np.sum(mask)
            percentage = pixel_count / mesh_ids.size * 100
            
            if pixel_count > 0:
                y_coords, x_coords = np.where(mask)
                centroid_y = np.mean(y_coords)
                centroid_x = np.mean(x_coords)
                dist_to_center = np.sqrt((centroid_y - center_y)**2 + (centroid_x - center_x)**2)
                
                id_analysis[int(uid)] = {
                    'pixels': int(pixel_count),
                    'percentage': float(percentage),
                    'centroid': (float(centroid_y), float(centroid_x)),
                    'dist_to_center': float(dist_to_center),
                    'mask': mask
                }
        
        # 按像素数量排序
        sorted_objects = sorted(id_analysis.items(), key=lambda x: x[1]['pixels'], reverse=True)
        
        # 检测目标物体：选择最靠近中心且像素适中的物体
        target_candidates = []
        for uid, info in sorted_objects:
            if (info['pixels'] > 500 and  # 最小像素要求
                info['pixels'] < 50000 and  # 最大像素要求（排除背景）
                info['dist_to_center'] < 150):  # 距离中心限制
                
                score = info['pixels'] / (info['dist_to_center'] + 1)
                target_candidates.append({
                    'id': int(uid),
                    'mask': info['mask'],
                    'info': info,
                    'score': score
                })
        
        if target_candidates:
            target_candidates.sort(key=lambda x: x['score'], reverse=True)
            results["target_object"] = target_candidates[0]
            logger.info(f"选择目标物体: ID={target_candidates[0]['id']}, 像素={target_candidates[0]['info']['pixels']}")
        
        # 检测机械爪：选择在边缘的较小物体
        gripper_candidates = []
        for uid, info in sorted_objects:
            if (info['pixels'] > 100 and  # 最小像素要求
                info['pixels'] < 10000):  # 机械爪不会太大
                
                # 检查是否在边缘
                near_edge = (info['centroid'][1] < 80 or info['centroid'][1] > width-80 or 
                           info['centroid'][0] < 80 or info['centroid'][0] > height-80)
                
                if near_edge:
                    gripper_candidates.append({
                        'id': int(uid),
                        'mask': info['mask'],
                        'info': info
                    })
        
        if gripper_candidates:
            # 合并所有机械爪候选
            combined_mask = np.zeros_like(mesh_ids, dtype=bool)
            gripper_ids = []
            total_pixels = 0
            
            for candidate in gripper_candidates:
                combined_mask |= candidate['mask']
                gripper_ids.append(candidate['id'])
                total_pixels += candidate['info']['pixels']
            
            results["gripper"] = {
                'ids': gripper_ids,
                'mask': combined_mask,
                'pixels': int(total_pixels)
            }
            logger.info(f"检测到机械爪: IDs={gripper_ids}, 总像素={total_pixels}")
        
        # 创建背景区域（排除目标物体和机械爪）
        background_mask = np.ones_like(mesh_ids, dtype=bool)
        if results["target_object"]:
            background_mask &= ~results["target_object"]["mask"]
        if results["gripper"]:
            background_mask &= ~results["gripper"]["mask"]
        
        background_pixels = np.sum(background_mask)
        if background_pixels > 0:
            results["background"] = {
                'mask': background_mask,
                'pixels': int(background_pixels)
            }
            logger.info(f"背景区域像素: {background_pixels}")
        
        # === 添加详细的调试输出 ===
        logger.info("🔍 === 区域检测详细调试信息 ===")
        logger.info(f"📊 所有分割ID统计: {dict(sorted([(uid, info['pixels']) for uid, info in id_analysis.items()], key=lambda x: x[1], reverse=True))}")
        
        # Target Object调试信息
        if results["target_object"]:
            target_info = results["target_object"]["info"]
            target_mask = results["target_object"]["mask"]
            target_patches = self.get_patches_from_mask(target_mask)
            logger.info(f"🎯 Target Object详情:")
            logger.info(f"   - ID: {results['target_object']['id']}")
            logger.info(f"   - 像素数: {target_info['pixels']}")
            logger.info(f"   - 覆盖率: {target_info['percentage']:.2f}%")
            logger.info(f"   - 质心: ({target_info['centroid'][0]:.1f}, {target_info['centroid'][1]:.1f})")
            logger.info(f"   - 距中心距离: {target_info['dist_to_center']:.1f}")
            logger.info(f"   - 对应patches: {len(target_patches)} 个 - {target_patches[:10]}")
        else:
            logger.info("❌ 未检测到Target Object")
        
        # Gripper调试信息
        if results["gripper"]:
            gripper_mask = results["gripper"]["mask"]
            gripper_patches = self.get_patches_from_mask(gripper_mask)
            logger.info(f"🤖 Gripper详情:")
            logger.info(f"   - IDs: {results['gripper']['ids']}")
            logger.info(f"   - 总像素数: {results['gripper']['pixels']}")
            logger.info(f"   - 对应patches: {len(gripper_patches)} 个 - {gripper_patches[:10]}")
        else:
            logger.info("❌ 未检测到Gripper")
        
        # Background调试信息
        if results["background"]:
            background_mask = results["background"]["mask"]
            background_patches = self.get_patches_from_mask(background_mask)
            logger.info(f"🌅 Background详情:")
            logger.info(f"   - 像素数: {results['background']['pixels']}")
            logger.info(f"   - 覆盖率: {results['background']['pixels'] / (height * width) * 100:.2f}%")
            logger.info(f"   - 对应patches: {len(background_patches)} 个 - {background_patches[:10]}")
        else:
            logger.info("❌ 未检测到Background")
        
        # === 新增：可视化并保存区域mask图 ===
        self.visualize_and_save_region_masks(rgb_image, results)
        
        return results

    def visualize_and_save_region_masks(self, rgb_image: np.ndarray, detection_results: Dict[str, Any]):
        """可视化并保存区域mask图"""
        logger.info("🎨 开始生成区域mask可视化图...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") 
        
        # 创建可视化目录
        viz_dir = os.path.join(self.config.output_dir, "region_masks")
        os.makedirs(viz_dir, exist_ok=True)
        
        # 设置颜色
        colors = {
            'target_object': (255, 0, 0),    # 红色
            'gripper': (0, 255, 0),          # 绿色  
            'background': (0, 0, 255)        # 蓝色
        }
        
        try:
            # 1. 创建综合可视化图（所有区域在一张图上）
            combined_image = rgb_image.copy()
            legend_info = []
            
            for region_name, region_data in detection_results.items():
                if region_data is None:
                    continue
                    
                mask = region_data['mask']
                color = colors.get(region_name, (128, 128, 128))
                
                # 在原图上叠加彩色mask
                mask_overlay = np.zeros_like(rgb_image)
                mask_overlay[mask] = color
                
                # 混合原图和mask
                alpha = 0.4  # 透明度
                combined_image = cv2.addWeighted(combined_image, 1-alpha, mask_overlay, alpha, 0)
                
                # 添加轮廓
                contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                cv2.drawContours(combined_image, contours, -1, color, 2)
                
                # 记录图例信息
                if region_name == 'target_object':
                    legend_info.append(f"Target Object (Red): ID={region_data.get('id', 'N/A')}, {np.sum(mask)} pixels")
                elif region_name == 'gripper':
                    legend_info.append(f"Gripper (Green): IDs={region_data.get('ids', 'N/A')}, {np.sum(mask)} pixels")
                elif region_name == 'background':
                    legend_info.append(f"Background (Blue): {np.sum(mask)} pixels")
            
            # 添加图例文字
            y_offset = 30
            for i, info in enumerate(legend_info):
                cv2.putText(combined_image, info, (10, y_offset + i * 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(combined_image, info, (10, y_offset + i * 25), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 0, 0), 1)
            
            # 保存综合图
            combined_path = os.path.join(viz_dir, f"combined_regions_{timestamp}.png")
            cv2.imwrite(combined_path, cv2.cvtColor(combined_image, cv2.COLOR_RGB2BGR))
            logger.info(f"✅ 综合区域图已保存: {combined_path}")
            
            # 2. 为每个区域创建单独的mask可视化图
            for region_name, region_data in detection_results.items():
                if region_data is None:
                    continue
                
                mask = region_data['mask']
                
                # 创建单个区域的可视化
                single_region_image = rgb_image.copy()
                color = colors.get(region_name, (128, 128, 128))
                
                # 将mask区域高亮显示
                mask_overlay = np.zeros_like(rgb_image)
                mask_overlay[mask] = color
                single_region_image = cv2.addWeighted(single_region_image, 0.7, mask_overlay, 0.3, 0)
                
                # 添加轮廓
                contours, _ = cv2.findContours(mask.astype(np.uint8), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                cv2.drawContours(single_region_image, contours, -1, color, 3)
                
                # 添加标题
                title = f"{region_name.title()}: {np.sum(mask)} pixels"
                cv2.putText(single_region_image, title, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 3)
                cv2.putText(single_region_image, title, (10, 30), 
                           cv2.FONT_HERSHEY_SIMPLEX, 1, color, 2)
                
                # 保存单个区域图
                single_path = os.path.join(viz_dir, f"{region_name}_{timestamp}.png")
                cv2.imwrite(single_path, cv2.cvtColor(single_region_image, cv2.COLOR_RGB2BGR))
                logger.info(f"✅ {region_name}区域图已保存: {single_path}")
            
            # 3. 创建纯mask图（黑白显示）
            fig, axes = plt.subplots(2, 2, figsize=(12, 10))
            fig.suptitle(f'Region Masks Visualization - {timestamp}', fontsize=14)
            
            # 原图
            axes[0, 0].imshow(rgb_image)
            axes[0, 0].set_title('Original Image')
            axes[0, 0].axis('off')
            
            # 各区域mask
            region_titles = ['Target Object', 'Gripper', 'Background']
            region_keys = ['target_object', 'gripper', 'background']
            
            for i, (region_key, title) in enumerate(zip(region_keys, region_titles)):
                row = (i + 1) // 2
                col = (i + 1) % 2
                
                if detection_results.get(region_key) is not None:
                    mask = detection_results[region_key]['mask']
                    axes[row, col].imshow(mask, cmap='gray')
                    axes[row, col].set_title(f'{title}\n({np.sum(mask)} pixels)')
                else:
                    axes[row, col].text(0.5, 0.5, f'No {title}\nDetected', 
                                      ha='center', va='center', fontsize=12)
                    axes[row, col].set_title(f'{title}\n(Not detected)')
                
                axes[row, col].axis('off')
            
            plt.tight_layout()
            mask_grid_path = os.path.join(viz_dir, f"mask_grid_{timestamp}.png")
            plt.savefig(mask_grid_path, dpi=200, bbox_inches='tight')
            plt.close()
            logger.info(f"✅ Mask网格图已保存: {mask_grid_path}")
            
            logger.info(f"🎨 区域mask可视化完成，保存在目录: {viz_dir}")
            
        except Exception as e:
            logger.error(f"❌ 区域mask可视化失败: {e}")
            import traceback
            traceback.print_exc()

    def get_patches_from_mask(self, mask: np.ndarray, patch_size: int = 16) -> List[int]:
        """从空间掩码获取对应的patch索引（参考spatialvla_visual_interpretation_analysis.py）"""
        height, width = mask.shape
        patch_height = height / patch_size
        patch_width = width / patch_size
        
        # 获取相关patches
        relevant_patches = set()
        y_coords, x_coords = np.where(mask)
        
        for y, x in zip(y_coords, x_coords):
            patch_row = min(int(y / patch_height), patch_size - 1)
            patch_col = min(int(x / patch_width), patch_size - 1)
            patch_id = patch_row * patch_size + patch_col
            relevant_patches.add(patch_id)
        
        return sorted(list(relevant_patches))
    
    def map_patches_to_tokens(self, patch_indices: List[int], visual_positions: List[int]) -> List[int]:
        """将patch索引映射到视觉token位置（参考spatialvla_visual_interpretation_analysis.py）"""
        if not patch_indices or not visual_positions:
            return []
        
        logger.info(f"🔗 映射patches到tokens:")
        logger.info(f"   输入patch索引: {patch_indices}")
        logger.info(f"   可用visual位置: {len(visual_positions)}")
        
        mapped_positions = []
        for patch_id in patch_indices:
            # 直接使用patch_id作为visual_positions的索引
            # 因为visual_positions包含256个位置，对应16x16的patches
            if patch_id < len(visual_positions):
                token_pos = visual_positions[patch_id]
                mapped_positions.append(token_pos)
                logger.info(f"   ✓ Patch {patch_id} -> Token位置 {token_pos}")
            else:
                logger.warning(f"   ⚠️ Patch {patch_id} 超出范围({len(visual_positions)})")
        
        logger.info(f"🎯 映射完成: {len(mapped_positions)}/{len(patch_indices)} patches成功映射")
        return mapped_positions

    def preprocess_inputs(self) -> bool:
        """预处理输入数据"""
        logger.info("正在预处理输入数据...")
        
        if self.current_image is None or self.current_instruction is None:
            logger.error("缺少图像或指令数据")
            return False
        
        try:
            # 预处理输入
            inputs = self.processor(
                text=self.current_instruction,
                images=self.current_image,
                return_tensors="pt"
            )
            
            # 移动到正确设备并确保数据类型一致
            dtype_map = {
                "bf16": torch.bfloat16,
                "fp16": torch.float16,
                "fp32": torch.float32
            }
            target_dtype = dtype_map.get(self.config.dtype, torch.bfloat16)
            
            # 确保所有输入tensor都使用正确的数据类型和设备
            for key, value in inputs.items():
                if torch.is_tensor(value):
                    if value.dtype.is_floating_point:
                        inputs[key] = value.to(device=self.device, dtype=target_dtype)
                    else:
                        inputs[key] = value.to(device=self.device)
                else:
                    inputs[key] = value
            
            self.current_inputs = inputs
            
            logger.info(f"输入预处理完成，数据类型已转换为: {target_dtype}")
            logger.info(f"输入keys: {list(inputs.keys())}")
            if 'input_ids' in inputs:
                logger.info(f"序列长度: {inputs['input_ids'].shape[1]}")
            if 'pixel_values' in inputs:
                logger.info(f"pixel_values类型: {inputs['pixel_values'].dtype}, 设备: {inputs['pixel_values'].device}")
            if 'intrinsic' in inputs:
                logger.info(f"intrinsic类型: {inputs['intrinsic'].dtype}, 设备: {inputs['intrinsic'].device}")
            
            return True
            
        except Exception as e:
            logger.error(f"输入预处理失败: {e}")
            return False

    def extract_visual_tokens_and_positions(self, inputs: Dict[str, torch.Tensor]) -> List[int]:
        """提取视觉token位置（参考spatialvla_visual_interpretation_analysis.py）"""
        if 'input_ids' not in inputs:
            logger.warning("输入中没有input_ids")
            return []
        
        input_ids = inputs['input_ids'][0]  # [seq_len]
        
        # 查找<image> token (ID: 257152)
        image_token_id = 257152
        image_positions = (input_ids == image_token_id).nonzero(as_tuple=True)[0]
        
        if len(image_positions) == 0:
            logger.warning("未找到<image> token")
            return []
        
        # SpatialVLA中，<image> token后面紧跟256个视觉token
        image_start = image_positions[0].item()
        visual_positions = list(range(image_start + 1, image_start + 257))  # 256个视觉token
        
        # 验证位置范围
        seq_len = input_ids.shape[0]
        visual_positions = [pos for pos in visual_positions if pos < seq_len]
        
        logger.info(f"找到 {len(visual_positions)} 个视觉token位置")
        logger.info(f"视觉token位置范围: {min(visual_positions)} - {max(visual_positions)}")
        
        return visual_positions

    def get_baseline_prediction(self) -> bool:
        """获取基线预测结果"""
        logger.info("正在获取基线预测结果...")
        
        if self.current_inputs is None:
            logger.error("输入数据未预处理")
            return False
        
        try:
            with torch.no_grad():
                outputs = self.model(**self.current_inputs)
                self.baseline_outputs = outputs
                
                # 获取预测的token
                logits = outputs.logits[0, -1, :]  # 最后一个位置的logits
                predicted_token_id = torch.argmax(logits).item()
                
                logger.info(f"基线预测获取成功，预测token ID: {predicted_token_id}")
                return True
                
        except Exception as e:
            logger.error(f"基线预测获取失败: {e}")
            return False

    def save_real_image(self, rgb_image: np.ndarray, timestamp: str) -> str:
        """保存真实图像"""
        if not self.config.save_images:
            return ""
        
        image_path = os.path.join(
            self.config.output_dir, 
            "real_images", 
            f"real_image_{timestamp}.png"
        )
        
        try:
            # 确保图像格式正确
            if rgb_image.dtype != np.uint8:
                rgb_image = (rgb_image * 255).astype(np.uint8) if rgb_image.max() <= 1.0 else rgb_image.astype(np.uint8)
            
            # 保存图像
            cv2.imwrite(image_path, cv2.cvtColor(rgb_image, cv2.COLOR_RGB2BGR))
            logger.info(f"真实图像已保存: {image_path}")
            self.saved_images.append(image_path)
            return image_path
            
        except Exception as e:
            logger.error(f"保存真实图像失败: {e}")
            return ""

    def apply_attention_knockout(self, from_positions: List[int], 
                                to_positions: List[int],
                                layer_start: int, layer_end: int) -> Dict[str, Any]:
        """应用Attention Knockout（基于论文方法的真正实现）"""
        logger.info(f"应用Attention Knockout: {len(from_positions)} -> {len(to_positions)} 位置，层{layer_start}-{layer_end}")
        
        # 保存原始模型状态
        original_prediction = None
        original_logits = None
        original_probs = None
        
        if self.baseline_outputs is not None:
            original_logits = self.baseline_outputs.logits[0, -1, :]
            original_prediction = torch.argmax(original_logits)
            original_probs = F.softmax(original_logits, dim=-1)
        
        # 存储hooks
        hooks = []
        
        try:
            # 创建attention knockout hook
            hook_activation_count = 0  # 添加hook激活计数器
            
            def create_attention_knockout_hook(layer_idx):
                def attention_knockout_hook(module, input, output):
                    nonlocal hook_activation_count
                    hook_activation_count += 1
                    
                    # 检查是否在目标层范围内
                    if not (layer_start <= layer_idx <= layer_end):
                        logger.debug(f"🔄 Hook Layer {layer_idx}: 不在目标范围内 ({layer_start}-{layer_end})")
                        return output
                    
                    logger.debug(f"🎯 Hook Layer {layer_idx}: 正在处理attention knockout")
                    
                    try:
                        # 获取attention输出
                        if isinstance(output, tuple) and len(output) >= 2:
                            hidden_states = output[0]
                            attention_weights = output[1]
                            
                            logger.debug(f"   - hidden_states shape: {hidden_states.shape if hidden_states is not None else None}")
                            logger.debug(f"   - attention_weights shape: {attention_weights.shape if attention_weights is not None else None}")
                            
                            if attention_weights is not None:
                                # 记录原始attention权重的统计信息
                                original_attention_stats = {
                                    'min': attention_weights.min().item(),
                                    'max': attention_weights.max().item(),
                                    'mean': attention_weights.mean().item(),
                                    'std': attention_weights.std().item()
                                }
                                
                                # 检查attention weights的维度
                                attention_shape = attention_weights.shape
                                logger.debug(f"   - attention_weights维度: {attention_shape}")
                                
                                # 修改attention权重：将指定连接设为-infinity
                                modified_weights = attention_weights.clone()
                                
                                modifications_made = 0
                                for from_pos in from_positions:
                                    for to_pos in to_positions:
                                        # 根据实际维度进行修改
                                        if len(attention_shape) == 4:
                                            # 4维: (batch, head, seq_len, seq_len)
                                            if (from_pos < modified_weights.size(-2) and 
                                                to_pos < modified_weights.size(-1)):
                                                # 检查原始值
                                                original_value = modified_weights[:, :, from_pos, to_pos].clone()
                                                
                                                # 阻断 from_pos -> to_pos 的attention
                                                modified_weights[:, :, from_pos, to_pos] = float('-inf')
                                                modifications_made += 1
                                                
                                                # 记录第一个修改的样本
                                                if modifications_made == 1:
                                                    logger.debug(f"   - 修改示例(4D): [{from_pos}, {to_pos}] 从 {original_value[0, 0].item():.6f} 改为 -inf")
                                        
                                        elif len(attention_shape) == 3:
                                            # 3维: (batch, seq_len, seq_len) 或 (head, seq_len, seq_len)
                                            if (from_pos < modified_weights.size(-2) and 
                                                to_pos < modified_weights.size(-1)):
                                                # 检查原始值
                                                original_value = modified_weights[:, from_pos, to_pos].clone()
                                                
                                                # 阻断 from_pos -> to_pos 的attention
                                                modified_weights[:, from_pos, to_pos] = float('-inf')
                                                modifications_made += 1
                                                
                                                # 记录第一个修改的样本
                                                if modifications_made == 1:
                                                    logger.debug(f"   - 修改示例(3D): [{from_pos}, {to_pos}] 从 {original_value[0].item():.6f} 改为 -inf")
                                        
                                        else:
                                            logger.warning(f"   - 不支持的attention维度: {attention_shape}")
                                            break
                                
                                # 重新归一化
                                modified_weights = F.softmax(modified_weights, dim=-1)
                                
                                # 记录修改后的统计信息
                                modified_attention_stats = {
                                    'min': modified_weights.min().item(),
                                    'max': modified_weights.max().item(),
                                    'mean': modified_weights.mean().item(),
                                    'std': modified_weights.std().item()
                                }
                                
                                logger.debug(f"   - 进行了 {modifications_made} 个attention修改")
                                logger.debug(f"   - 原始attention统计: {original_attention_stats}")
                                logger.debug(f"   - 修改后attention统计: {modified_attention_stats}")
                                
                                # 检查修改是否真的生效
                                attention_changed = not torch.allclose(attention_weights, modified_weights, atol=1e-6)
                                logger.debug(f"   - Attention权重是否真的改变: {attention_changed}")
                                
                                # 重新计算hidden states（如果可能）
                                # 这里简化实现：直接在hidden states上应用影响
                                influence_factor = 0.1  # 影响程度
                                original_hidden_stats = {
                                    'min': hidden_states.min().item(),
                                    'max': hidden_states.max().item(),
                                    'mean': hidden_states.mean().item(),
                                    'std': hidden_states.std().item()
                                }
                                
                                modified_positions = 0
                                for from_pos in from_positions:
                                    if from_pos < hidden_states.size(1):
                                        hidden_states[:, from_pos, :] *= (1.0 - influence_factor)
                                        modified_positions += 1
                                
                                modified_hidden_stats = {
                                    'min': hidden_states.min().item(),
                                    'max': hidden_states.max().item(),
                                    'mean': hidden_states.mean().item(),
                                    'std': hidden_states.std().item()
                                }
                                
                                logger.debug(f"   - 修改了 {modified_positions} 个hidden state位置")
                                logger.debug(f"   - 原始hidden统计: {original_hidden_stats}")
                                logger.debug(f"   - 修改后hidden统计: {modified_hidden_stats}")
                                
                                return (hidden_states, modified_weights) + output[2:]
                            else:
                                logger.debug(f"   - attention_weights为None，跳过修改")
                            
                        return output
                        
                    except Exception as e:
                        logger.warning(f"❌ Layer {layer_idx} Attention knockout hook failed: {e}")
                        return output
                
                return attention_knockout_hook
            
            # 注册hooks到指定层
            layer_count = 0
            for name, module in self.model.named_modules():
                if 'self_attn' in name and 'language_model' in name:
                    # 提取层号
                    layer_match = re.search(r'layers\.(\d+)\.', name)
                    if layer_match:
                        layer_num = int(layer_match.group(1))
                        if layer_start <= layer_num <= layer_end:
                            hook = module.register_forward_hook(
                                create_attention_knockout_hook(layer_num)
                            )
                            hooks.append(hook)
                            layer_count += 1
            
            logger.info(f"已在{layer_count}个层上注册attention knockout hooks")
            
            # === 检查hook激活和模型状态 ===
            logger.info(f"🔧 === Hook激活状态检查 ===")
            logger.info(f"📊 注册的hook数量: {len(hooks)}")
            logger.info(f"📊 预期激活hook的层数: {layer_count}")
            
            # 执行推理
            with torch.no_grad():
                knockout_outputs = self.model(**self.current_inputs)
                knockout_logits = knockout_outputs.logits[0, -1, :]
                knockout_prediction = torch.argmax(knockout_logits)
                knockout_probs = F.softmax(knockout_logits, dim=-1)
            
            # === Hook激活统计 ===
            logger.info(f"🔧 Hook激活统计:")
            logger.info(f"   - 总hook激活次数: {hook_activation_count}")
            logger.info(f"   - 预期激活次数: {layer_count} (如果每层只激活一次)")
            logger.info(f"   - 激活异常: {'是' if hook_activation_count != layer_count else '否'}")
            
            # === 深度模型状态对比 ===
            logger.info(f"🔍 === 深度模型状态对比 ===")
            
            # 检查logits的详细差异
            if original_logits is not None:
                logits_diff = torch.abs(original_logits - knockout_logits)
                logits_max_diff = logits_diff.max().item()
                logits_mean_diff = logits_diff.mean().item()
                logits_identical = torch.allclose(original_logits, knockout_logits, atol=1e-6)
                
                logger.info(f"📊 Logits对比:")
                logger.info(f"   - 最大差异: {logits_max_diff:.8f}")
                logger.info(f"   - 平均差异: {logits_mean_diff:.8f}")
                logger.info(f"   - 是否完全相同: {logits_identical}")
                logger.info(f"   - 原始logits前5个值: {original_logits[:5].tolist()}")
                logger.info(f"   - Knockout logits前5个值: {knockout_logits[:5].tolist()}")
                
                # 检查概率分布的差异
                probs_diff = torch.abs(original_probs - knockout_probs)
                probs_max_diff = probs_diff.max().item()
                probs_mean_diff = probs_diff.mean().item()
                probs_identical = torch.allclose(original_probs, knockout_probs, atol=1e-6)
                
                logger.info(f"📊 概率分布对比:")
                logger.info(f"   - 最大差异: {probs_max_diff:.8f}")
                logger.info(f"   - 平均差异: {probs_mean_diff:.8f}")
                logger.info(f"   - 是否完全相同: {probs_identical}")
                
                # 检查是否有任何模型状态真的改变
                if logits_identical and probs_identical:
                    logger.warning(f"🚨 警告: Logits和概率分布完全相同！Attention knockout可能没有生效！")
                    logger.warning(f"   可能的原因:")
                    logger.warning(f"   1. Hook没有正确激活")
                    logger.warning(f"   2. 修改的attention连接对最终输出没有影响")
                    logger.warning(f"   3. 模型架构与预期不符")
                    logger.warning(f"   4. Hook修改被后续层覆盖")
                else:
                    logger.info(f"✅ 确认: 模型状态确实发生了变化")
            
            # === 检查中间状态变化 ===
            # 运行一次原始推理来对比中间状态
            logger.info(f"🔍 运行原始推理进行中间状态对比...")
            original_intermediate_hook_count = 0
            
            def intermediate_check_hook(module, input, output):
                nonlocal original_intermediate_hook_count
                original_intermediate_hook_count += 1
                return output
            
            # 注册检查hook
            check_hooks = []
            for name, module in self.model.named_modules():
                if 'self_attn' in name and 'language_model' in name:
                    layer_match = re.search(r'layers\.(\d+)\.', name)
                    if layer_match:
                        layer_num = int(layer_match.group(1))
                        if layer_start <= layer_num <= layer_end:
                            hook = module.register_forward_hook(intermediate_check_hook)
                            check_hooks.append(hook)
            
            # 运行原始推理
            with torch.no_grad():
                original_check_outputs = self.model(**self.current_inputs)
            
            # 清理检查hooks
            for hook in check_hooks:
                hook.remove()
            
            logger.info(f"🔍 原始推理中间状态检查:")
            logger.info(f"   - 中间hook激活次数: {original_intermediate_hook_count}")
            logger.info(f"   - 与knockout推理hook激活对比: {original_intermediate_hook_count} vs {hook_activation_count}")
            
            # === 详细的计算过程记录 ===
            metrics = {}
            calculation_details = {}
            
            if original_prediction is not None:
                # 基础计算
                prediction_changed = (knockout_prediction != original_prediction).item()
                
                # KL散度
                kl_div = F.kl_div(
                    F.log_softmax(knockout_logits, dim=-1),
                    original_probs,
                    reduction='sum'
                ).item()
                
                # 概率相关计算
                original_top_prob = original_probs[original_prediction].item()
                knockout_top_prob = knockout_probs[original_prediction].item()
                prob_drop = original_top_prob - knockout_top_prob
                
                # 性能比率（论文中的核心指标）
                performance_ratio = knockout_top_prob / original_top_prob if original_top_prob > 0 else 1.0
                
                # === 获取top tokens进行详细分析 ===
                original_top_values, original_top_indices = torch.topk(original_probs, 10)
                knockout_top_values, knockout_top_indices = torch.topk(knockout_probs, 10)
                
                # 解码tokens
                original_top_tokens = []
                knockout_top_tokens = []
                
                try:
                    from transformers import AutoTokenizer
                    tokenizer = self.processor.tokenizer
                    
                    for i in range(10):
                        # 原始top tokens
                        token_id = original_top_indices[i].item()
                        prob = original_top_values[i].item()
                        try:
                            token_text = tokenizer.decode([token_id])
                            original_top_tokens.append({
                                'token_id': token_id,
                                'token_text': token_text.strip(),
                                'probability': prob
                            })
                        except:
                            original_top_tokens.append({
                                'token_id': token_id,
                                'token_text': f'<{token_id}>',
                                'probability': prob
                            })
                        
                        # knockout后top tokens
                        token_id = knockout_top_indices[i].item()
                        prob = knockout_top_values[i].item()
                        try:
                            token_text = tokenizer.decode([token_id])
                            knockout_top_tokens.append({
                                'token_id': token_id,
                                'token_text': token_text.strip(),
                                'probability': prob
                            })
                        except:
                            knockout_top_tokens.append({
                                'token_id': token_id,
                                'token_text': f'<{token_id}>',
                                'probability': prob
                            })
                    
                except Exception as e:
                    logger.warning(f"Token解码失败: {e}")
                
                # 详细计算记录
                calculation_details = {
                    'layer_window': f"{layer_start}-{layer_end}",
                    'from_positions_count': len(from_positions),
                    'to_positions_count': len(to_positions),
                    'from_positions_sample': from_positions[:5],  # 前5个示例
                    'to_positions_sample': to_positions[:5],
                    
                    # 原始输出
                    'original_prediction_id': original_prediction.item(),
                    'original_top_prob': original_top_prob,
                    'original_top_tokens': original_top_tokens,
                    
                    # knockout后输出
                    'knockout_prediction_id': knockout_prediction.item(),
                    'knockout_top_prob': knockout_top_prob,
                    'knockout_top_tokens': knockout_top_tokens,
                    
                    # 计算过程
                    'prob_drop': prob_drop,
                    'performance_ratio_calculation': f"{knockout_top_prob:.6f} / {original_top_prob:.6f} = {performance_ratio:.6f}",
                    'kl_divergence': kl_div,
                    'prediction_changed': prediction_changed
                }
                
                metrics = {
                    'original_prediction': original_prediction.item(),
                    'knockout_prediction': knockout_prediction.item(),
                    'prediction_changed': prediction_changed,
                    'kl_divergence': kl_div,
                    'probability_drop': prob_drop,
                    'performance_ratio': performance_ratio,
                    'original_prob': original_top_prob,
                    'knockout_prob': knockout_top_prob,
                    'calculation_details': calculation_details
                }
                
                # === 详细日志输出 ===
                logger.info(f"🧮 === Attention Knockout计算详情 ===")
                logger.info(f"📊 层窗口: {layer_start}-{layer_end}")
                logger.info(f"🔗 from_positions: {len(from_positions)} 个 (样本: {from_positions[:5]})")
                logger.info(f"🎯 to_positions: {len(to_positions)} 个 (样本: {to_positions[:5]})")
                logger.info(f"")
                logger.info(f"📈 原始预测:")
                logger.info(f"   Token ID: {original_prediction.item()}")
                logger.info(f"   概率: {original_top_prob:.6f}")
                logger.info(f"   Token文本: '{original_top_tokens[0]['token_text'] if original_top_tokens else 'N/A'}'")
                logger.info(f"")
                logger.info(f"📉 Knockout后预测:")
                logger.info(f"   Token ID: {knockout_prediction.item()}")  
                logger.info(f"   概率: {knockout_top_prob:.6f}")
                logger.info(f"   Token文本: '{knockout_top_tokens[0]['token_text'] if knockout_top_tokens else 'N/A'}'")
                logger.info(f"")
                logger.info(f"🔢 Performance Ratio计算:")
                logger.info(f"   公式: knockout_prob / original_prob")
                logger.info(f"   计算: {knockout_top_prob:.6f} / {original_top_prob:.6f} = {performance_ratio:.6f}")
                logger.info(f"   预测是否改变: {'是' if prediction_changed else '否'}")
                logger.info(f"   概率下降: {prob_drop:.6f}")
                logger.info(f"   KL散度: {kl_div:.6f}")
                
                # 显示top-5 tokens对比
                logger.info(f"")
                logger.info(f"🏆 Top-5 Tokens对比:")
                logger.info(f"{'Rank':<4} {'Original':<20} {'Prob':<10} {'Knockout':<20} {'Prob':<10}")
                logger.info(f"{'-'*70}")
                for i in range(min(5, len(original_top_tokens), len(knockout_top_tokens))):
                    orig = original_top_tokens[i]
                    knock = knockout_top_tokens[i]
                    logger.info(f"{i+1:<4} {orig['token_text'][:18]:<20} {orig['probability']:<10.4f} {knock['token_text'][:18]:<20} {knock['probability']:<10.4f}")
            
            # 保存详细计算结果到文件
            self.save_calculation_details(calculation_details, layer_start, layer_end)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Attention Knockout执行失败: {e}")
            return {'error': str(e)}
            
        finally:
            # 清理hooks
            for hook in hooks:
                hook.remove()

    def save_calculation_details(self, details: Dict[str, Any], layer_start: int, layer_end: int):
        """保存详细的计算过程到文件"""
        if not details:
            return
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        details_dir = os.path.join(self.config.output_dir, "calculation_details")
        os.makedirs(details_dir, exist_ok=True)
        
        filename = f"knockout_details_layers_{layer_start}_{layer_end}_{timestamp}.json"
        filepath = os.path.join(details_dir, filename)
        
        try:
            # 确保所有数据都是可序列化的
            serializable_details = self._make_serializable(details)
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_details, f, indent=2, ensure_ascii=False)
            
            logger.info(f"💾 计算详情已保存: {filepath}")
            
        except Exception as e:
            logger.error(f"保存计算详情失败: {e}")

    def run_region_based_knockout_analysis(self) -> Dict[str, Any]:
        """运行基于区域的knockout分析"""
        logger.info("开始基于区域的Attention Knockout分析...")
        
        if self.key_objects is None:
            logger.error("没有检测到关键物体")
            return {}
        
        # 提取视觉token位置
        visual_positions = self.extract_visual_tokens_and_positions(self.current_inputs)
        if not visual_positions:
            logger.error("未找到视觉token位置")
            return {}
        
        results = {}
        
        # 对每个区域进行测试
        for region_name in self.config.test_regions:
            if region_name not in self.key_objects or self.key_objects[region_name] is None:
                logger.warning(f"区域{region_name}未检测到")
                continue
            
            logger.info(f"分析区域: {region_name}")
            
            # 获取区域对应的token位置
            region_mask = self.key_objects[region_name]['mask']
            patch_indices = self.get_patches_from_mask(region_mask)
            region_token_positions = self.map_patches_to_tokens(patch_indices, visual_positions)
            
            # === 添加详细的token映射调试信息 ===
            logger.info(f"🔗 === {region_name} Token映射调试信息 ===")
            logger.info(f"📏 region_mask形状: {region_mask.shape}")
            logger.info(f"📊 region_mask覆盖像素: {np.sum(region_mask)} / {region_mask.size} ({np.sum(region_mask)/region_mask.size*100:.2f}%)")
            logger.info(f"🎯 patch_indices: {len(patch_indices)} 个 - {patch_indices}")
            logger.info(f"🔗 映射结果: {len(region_token_positions)} 个token位置 - {region_token_positions}")
            
            if not region_token_positions:
                logger.warning(f"区域{region_name}没有对应的token位置")
                continue
            
            # 对每个knockout模式进行测试
            region_results = {}
            
            for pattern in self.config.knockout_patterns:
                logger.info(f"  测试模式: {pattern}")
                
                # 根据模式确定source和target位置
                from_positions, to_positions = self._get_knockout_positions(
                    pattern, region_token_positions, visual_positions
                )
                
                if not from_positions or not to_positions:
                    logger.warning(f"模式{pattern}的位置为空")
                    continue
                
                # 对每个层窗口进行测试
                pattern_results = {}
                
                for layer_start, layer_end in self.config.layer_windows:
                    logger.info(f"    层窗口: {layer_start}-{layer_end}")
                    
                    metrics = self.apply_attention_knockout(
                        from_positions, to_positions, layer_start, layer_end
                    )
                    
                    window_name = f"layers_{layer_start}_{layer_end}"
                    pattern_results[window_name] = metrics
                
                region_results[pattern] = pattern_results
            
            results[region_name] = region_results
        
        return results

    def _get_knockout_positions(self, pattern: str, region_positions: List[int], 
                              visual_positions: List[int]) -> Tuple[List[int], List[int]]:
        """根据knockout模式获取源和目标位置"""
        from_positions = []
        to_positions = []
        
        # 获取序列长度用于计算最后位置
        seq_len = self.current_inputs['input_ids'].shape[1]
        last_position = seq_len - 1
        
        # 计算最后视觉行位置（假设是16x16 patches的最后一行）
        last_visual_row = []
        for i in range(240, 256):  # 最后一行的16个patches
            if i < len(visual_positions):
                last_visual_row.append(visual_positions[i])
        
        # === 添加详细的调试输出 ===
        logger.info(f"🔗 === Knockout Position计算调试信息 ===")
        logger.info(f"📏 序列长度: {seq_len}")
        logger.info(f"📍 last_position: {last_position}")
        logger.info(f"🎯 visual_positions总数: {len(visual_positions)}")
        logger.info(f"🎯 visual_positions范围: {min(visual_positions)} - {max(visual_positions)} (前10个: {visual_positions[:10]})")
        logger.info(f"📊 region_positions: {len(region_positions)} 个 - {region_positions}")
        logger.info(f"🔚 last_visual_row计算: patches 240-255 -> {len(last_visual_row)} tokens")
        logger.info(f"🔚 last_visual_row positions: {last_visual_row}")
        logger.info(f"⚙️ 当前pattern: {pattern}")
        
        if pattern == "region_to_last":
            # 区域 -> 最后位置 
            from_positions = region_positions
            to_positions = [last_position]
            
        elif pattern == "region_with_buffer_to_last":
            # 区域+缓冲 -> 最后位置
            buffer_positions = []
            for pos in region_positions:
                for offset in [-1, 0, 1]:
                    buf_pos = pos + offset
                    if 0 <= buf_pos < len(visual_positions) and buf_pos not in buffer_positions:
                        buffer_positions.append(buf_pos)
            from_positions = buffer_positions
            to_positions = [last_position]
            
        elif pattern == "all_except_region_to_last":
            # 除区域外所有 -> 最后位置
            all_visual = list(range(len(visual_positions)))
            from_positions = [pos for pos in all_visual if pos not in region_positions]
            to_positions = [last_position]
            
        else:
            logger.warning(f"🚨 未知的knockout模式: {pattern}")
            return [], []
        
        # === 输出最终结果 ===
        logger.info(f"✅ Pattern '{pattern}' 结果:")
        logger.info(f"   - from_positions: {len(from_positions)} 个 - {from_positions[:10] if len(from_positions) > 10 else from_positions}")
        logger.info(f"   - to_positions: {len(to_positions)} 个 - {to_positions}")
        
        # 检查位置有效性
        max_seq_pos = seq_len - 1
        invalid_from = [pos for pos in from_positions if pos > max_seq_pos]
        invalid_to = [pos for pos in to_positions if pos > max_seq_pos]
        
        if invalid_from:
            logger.warning(f"⚠️ 无效的from_positions (超出序列长度{seq_len}): {invalid_from}")
        if invalid_to:
            logger.warning(f"⚠️ 无效的to_positions (超出序列长度{seq_len}): {invalid_to}")
        
        # 检查空集合
        if not from_positions:
            logger.warning(f"⚠️ from_positions为空!")
        if not to_positions:
            logger.warning(f"⚠️ to_positions为空!")
        
        return from_positions, to_positions

    def create_paper_style_visualization(self, save_path: str):
        """创建论文风格的可视化（英文表头）"""
        logger.info("创建论文风格的可视化...")
        
        if not self.knockout_results:
            logger.warning("没有knockout结果可视化")
            return
        
        # 创建大图
        fig = plt.figure(figsize=(20, 16))
        
        # 创建子图布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. 性能比率热力图
        ax1 = fig.add_subplot(gs[0, :])
        self._plot_performance_heatmap(ax1)
        ax1.set_title('Performance Ratio by Region and Layer Window', fontsize=14, fontweight='bold')
        
        # 2. 各区域性能对比
        ax2 = fig.add_subplot(gs[1, 0])
        self._plot_region_comparison(ax2)
        ax2.set_title('Average Performance Drop\nby Region', fontsize=12)
        
        # 3. 层窗口效果分析
        ax3 = fig.add_subplot(gs[1, 1])
        self._plot_layer_window_effect(ax3)
        ax3.set_title('Layer Window Effect\nAnalysis', fontsize=12)
        
        # 4. Knockout模式比较
        ax4 = fig.add_subplot(gs[1, 2])
        self._plot_knockout_pattern_comparison(ax4)
        ax4.set_title('Knockout Pattern\nComparison', fontsize=12)
        
        # 5. 统计摘要
        ax5 = fig.add_subplot(gs[2, :])
        self._plot_statistical_summary(ax5)
        ax5.set_title('Statistical Summary', fontsize=14)
        
        plt.suptitle('SpatialVLA Attention Knockout Analysis Results', 
                    fontsize=16, fontweight='bold')
        
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"可视化已保存: {save_path}")

    def _plot_performance_heatmap(self, ax):
        """绘制性能比率热力图"""
        # 收集数据
        regions = []
        layer_windows = []
        performance_data = []
        
        for region_name, region_data in self.knockout_results.items():
            if not isinstance(region_data, dict):
                continue
                
            for pattern_name, pattern_data in region_data.items():
                if not isinstance(pattern_data, dict):
                    continue
                    
                for window_name, metrics in pattern_data.items():
                    if isinstance(metrics, dict) and 'performance_ratio' in metrics:
                        row_label = f"{region_name}_{pattern_name}"
                        if row_label not in regions:
                            regions.append(row_label)
                        if window_name not in layer_windows:
                            layer_windows.append(window_name)
        
        if not regions or not layer_windows:
            ax.text(0.5, 0.5, 'No Data Available', ha='center', va='center')
            return
        
        # 创建数据矩阵
        data_matrix = np.ones((len(regions), len(layer_windows)))
        
        for i, region_pattern in enumerate(regions):
            region_name, pattern_name = region_pattern.split('_', 1)
            if region_name in self.knockout_results:
                region_data = self.knockout_results[region_name]
                if pattern_name in region_data:
                    pattern_data = region_data[pattern_name]
                    for j, window_name in enumerate(layer_windows):
                        if window_name in pattern_data:
                            metrics = pattern_data[window_name]
                            if isinstance(metrics, dict) and 'performance_ratio' in metrics:
                                data_matrix[i, j] = metrics['performance_ratio']
        
        # 计算适当的颜色范围
        min_val = np.min(data_matrix)
        max_val = np.max(data_matrix)
        
        # 使用分段颜色映射：红色(<1), 白色(=1), 蓝色(>1)
        from matplotlib.colors import TwoSlopeNorm
        import matplotlib.colors as mcolors
        
        # 创建自定义颜色映射
        colors = ['darkred', 'red', 'white', 'lightblue', 'darkblue']
        n_bins = 256
        cmap = mcolors.LinearSegmentedColormap.from_list('custom', colors, N=n_bins)
        
        # 使用TwoSlopeNorm以1为中心
        norm = TwoSlopeNorm(vmin=min_val, vcenter=1.0, vmax=max_val)
        
        # 绘制热力图
        im = ax.imshow(data_matrix, aspect='auto', cmap=cmap, norm=norm)
        
        # 设置标签
        ax.set_xticks(range(len(layer_windows)))
        ax.set_xticklabels(layer_windows, rotation=45)
        ax.set_yticks(range(len(regions)))
        ax.set_yticklabels(regions)
        
        # 添加颜色条
        cbar = plt.colorbar(im, ax=ax)
        cbar.set_label('Performance Ratio\n(Red: <1, White: =1, Blue: >1)')
        
        # 添加数值标注
        for i in range(len(regions)):
            for j in range(len(layer_windows)):
                # 根据值选择文字颜色
                text_color = 'white' if data_matrix[i, j] < 0.5 or data_matrix[i, j] > 1.5 else 'black'
                text = ax.text(j, i, f'{data_matrix[i, j]:.3f}',
                             ha="center", va="center", color=text_color, fontsize=8, weight='bold')

    def _plot_region_comparison(self, ax):
        """绘制区域性能对比"""
        region_performance = {}
        
        for region_name, region_data in self.knockout_results.items():
            if not isinstance(region_data, dict):
                continue
            
            performances = []
            for pattern_data in region_data.values():
                if isinstance(pattern_data, dict):
                    for metrics in pattern_data.values():
                        if isinstance(metrics, dict) and 'performance_ratio' in metrics:
                            performances.append(1.0 - metrics['performance_ratio'])  # 转换为性能下降
            
            if performances:
                region_performance[region_name] = np.mean(performances)
        
        if region_performance:
            regions = list(region_performance.keys())
            values = list(region_performance.values())
            
            bars = ax.bar(regions, values, alpha=0.7)
            ax.set_ylabel('Average Performance Drop')
            ax.set_xlabel('Region')
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                       f'{value:.3f}', ha='center', va='bottom')
        else:
            ax.text(0.5, 0.5, 'No Data', ha='center', va='center')

    def _plot_layer_window_effect(self, ax):
        """绘制层窗口效果分析"""
        window_effects = {}
        
        for region_data in self.knockout_results.values():
            if not isinstance(region_data, dict):
                continue
            
            for pattern_data in region_data.values():
                if not isinstance(pattern_data, dict):
                    continue
                
                for window_name, metrics in pattern_data.items():
                    if isinstance(metrics, dict) and 'performance_ratio' in metrics:
                        if window_name not in window_effects:
                            window_effects[window_name] = []
                        window_effects[window_name].append(1.0 - metrics['performance_ratio'])
        
        if window_effects:
            windows = list(window_effects.keys())
            avg_effects = [np.mean(effects) for effects in window_effects.values()]
            
            bars = ax.bar(range(len(windows)), avg_effects, alpha=0.7)
            ax.set_xticks(range(len(windows)))
            ax.set_xticklabels(windows, rotation=45)
            ax.set_ylabel('Average Effect')
            
            for bar, value in zip(bars, avg_effects):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                       f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        else:
            ax.text(0.5, 0.5, 'No Data', ha='center', va='center')

    def _plot_knockout_pattern_comparison(self, ax):
        """绘制knockout模式比较"""
        pattern_effects = {}
        
        for region_data in self.knockout_results.values():
            if not isinstance(region_data, dict):
                continue
            
            for pattern_name, pattern_data in region_data.items():
                if not isinstance(pattern_data, dict):
                    continue
                
                effects = []
                for metrics in pattern_data.values():
                    if isinstance(metrics, dict) and 'performance_ratio' in metrics:
                        effects.append(1.0 - metrics['performance_ratio'])
                
                if effects:
                    if pattern_name not in pattern_effects:
                        pattern_effects[pattern_name] = []
                    pattern_effects[pattern_name].extend(effects)
        
        if pattern_effects:
            patterns = list(pattern_effects.keys())
            avg_effects = [np.mean(effects) for effects in pattern_effects.values()]
            
            bars = ax.bar(range(len(patterns)), avg_effects, alpha=0.7)
            ax.set_xticks(range(len(patterns)))
            ax.set_xticklabels(patterns, rotation=45, ha='right')
            ax.set_ylabel('Average Effect')
            
            for bar, value in zip(bars, avg_effects):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                       f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        else:
            ax.text(0.5, 0.5, 'No Data', ha='center', va='center')

    def _plot_statistical_summary(self, ax):
        """绘制统计摘要"""
        # 收集统计数据
        total_tests = 0
        effective_tests = 0
        avg_performance_drop = 0
        performance_drops = []
        
        for region_data in self.knockout_results.values():
            if not isinstance(region_data, dict):
                continue
            
            for pattern_data in region_data.values():
                if not isinstance(pattern_data, dict):
                    continue
                
                for metrics in pattern_data.values():
                    if isinstance(metrics, dict) and 'performance_ratio' in metrics:
                        total_tests += 1
                        performance_drop = 1.0 - metrics['performance_ratio']
                        performance_drops.append(performance_drop)
                        
                        if performance_drop > 0.01:  # 1%阈值
                            effective_tests += 1
        
        if performance_drops:
            avg_performance_drop = np.mean(performance_drops)
            std_performance_drop = np.std(performance_drops)
        
        # 创建统计文本
        stats_text = []
        stats_text.append("Attention Knockout Analysis Summary:")
        stats_text.append("")
        stats_text.append(f"Total Tests: {total_tests}")
        stats_text.append(f"Effective Tests: {effective_tests}")
        stats_text.append(f"Effectiveness Rate: {effective_tests/max(1,total_tests)*100:.1f}%")
        stats_text.append("")
        stats_text.append(f"Average Performance Drop: {avg_performance_drop:.4f}")
        if performance_drops:
            stats_text.append(f"Std Performance Drop: {std_performance_drop:.4f}")
            stats_text.append(f"Max Performance Drop: {max(performance_drops):.4f}")
            stats_text.append(f"Min Performance Drop: {min(performance_drops):.4f}")
        
        # 显示文本
        ax.text(0.05, 0.95, '\n'.join(stats_text), transform=ax.transAxes,
               verticalalignment='top', fontsize=11, family='monospace')
        ax.set_xlim(0, 1)
        ax.set_ylim(0, 1)
        ax.axis('off')

    def save_results(self):
        """保存分析结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存JSON结果
        results_path = os.path.join(self.config.output_dir, f"knockout_results_{timestamp}.json")
        
        serializable_results = self._make_serializable({
            'config': self.config.__dict__,
            'knockout_results': self.knockout_results,
            'saved_images': self.saved_images,
            'analysis_timestamp': timestamp
        })
        
        with open(results_path, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"分析结果已保存: {results_path}")
        return results_path

    def _make_serializable(self, obj):
        """转换为可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(v) for v in obj]
        elif isinstance(obj, (int, float, str, bool, type(None))):
            return obj
        elif hasattr(obj, 'item'):  # torch tensor scalars
            return obj.item()
        else:
            return str(obj)

    def cleanup(self):
        """清理资源"""
        if self.env:
            self.env.close()
        if self.spatialvla_model:
            del self.spatialvla_model
        logger.info("资源清理完成")

    def run_full_analysis(self):
        """运行完整的attention knockout分析"""
        logger.info("开始完整的Attention Knockout分析流程...")
        
        try:
            # 1. 设置模型
            if not self.setup_model():
                raise Exception("模型设置失败")
            
            # 2. 设置SimplerEnv环境
            obs, reset_info = self.setup_simpler_env()
            if obs is None:
                raise Exception("SimplerEnv环境设置失败")
            
            # 3. 创建SpatialVLA推理模型
            if not self.create_spatialvla_model():
                raise Exception("SpatialVLA推理模型创建失败")
            
            # 4. 获取真实图像数据
            rgb_overhead, seg_overhead, success = self.get_overhead_camera_data(obs)
            if not success:
                raise Exception("获取overhead camera数据失败")
            
            # 5. 保存真实图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            self.save_real_image(rgb_overhead, timestamp)
            
            # 6. 检测关键物体
            self.key_objects = self.detect_key_objects(seg_overhead, rgb_overhead)
            
            # 7. 设置当前图像和预处理输入
            self.current_image = Image.fromarray(rgb_overhead)
            if not self.preprocess_inputs():
                raise Exception("输入预处理失败")
            
            # 8. 获取基线预测
            if not self.get_baseline_prediction():
                raise Exception("基线预测获取失败")
            
            # 9. 运行基于区域的knockout分析
            self.knockout_results = self.run_region_based_knockout_analysis()
            
            # 10. 创建可视化
            viz_path = os.path.join(self.config.output_dir, f"attention_knockout_analysis_{timestamp}.png")
            self.create_paper_style_visualization(viz_path)
            
            # 11. 保存结果
            results_path = self.save_results()
            
            logger.info("✅ Attention Knockout分析完成!")
            logger.info(f"结果文件: {results_path}")
            logger.info(f"可视化文件: {viz_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 分析过程中出现错误: {e}")
            return False
            
        finally:
            # 12. 清理资源
            self.cleanup()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='SpatialVLA Attention Knockout Analysis')
    parser.add_argument('--model_path', type=str, default="IPEC-COMMUNITY/spatialvla-4b-224-pt",
                       help='模型路径')
    parser.add_argument('--task_name', type=str, default="google_robot_pick_coke_can",
                       help='SimplerEnv任务名称')
    parser.add_argument('--device', type=str, default="cuda:0",
                       help='设备')
    parser.add_argument('--output_dir', type=str, default="./attention_knockout_diagnostic_output",
                       help='输出目录')
    
    args = parser.parse_args()
    
    # 创建配置
    config = AttentionKnockoutConfig(
        model_path=args.model_path,
        task_name=args.task_name,
        device=args.device,
        output_dir=args.output_dir
    )
    
    # 创建分析器
    analyzer = PaperBasedAttentionKnockout(config)
    
    # 运行分析
    success = analyzer.run_full_analysis()
    
    if success:
        logger.info("🎉 Attention Knockout分析成功完成!")
    else:
        logger.error("💥 Attention Knockout分析失败!")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main()) 